{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            إدارة الجرد
        </h3>
    </div>
    <div class="col-md-4 text-right">
        {% if current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager %}
        <a href="{{ url_for('inventory.create_audit') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> بدء جرد جديد
        </a>
        {% endif %}
        <a href="{{ url_for('warehouse.dashboard') }}" class="btn btn-outline-secondary ml-2">
            <i class="fas fa-arrow-right"></i> العودة للصفحة الرئيسية
        </a>
    </div>
</div>

<!-- تنبيهات الجرد القادم -->
{% if audit_notifications %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-bell"></i> تنبيهات الجرد القادم
                    <span class="badge badge-dark ml-2">{{ audit_notifications|length }}</span>
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th>المستودع</th>
                                <th>تاريخ الجرد السابق</th>
                                <th>تاريخ الجرد القادم</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for notification in audit_notifications %}
                            <tr class="{% if notification.is_overdue %}table-danger{% elif notification.is_today %}table-warning{% elif notification.is_urgent %}table-info{% endif %}">
                                <td>
                                    <strong>{{ notification.warehouse_name }}</strong>
                                </td>
                                <td>{{ notification.audit.audit_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ notification.next_audit_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if notification.is_overdue %}
                                        <span class="badge badge-danger">
                                            <i class="fas fa-exclamation-triangle"></i> متأخر {{ notification.days_remaining|abs }} يوم
                                        </span>
                                    {% elif notification.is_today %}
                                        <span class="badge badge-warning">
                                            <i class="fas fa-clock"></i> اليوم
                                        </span>
                                    {% elif notification.is_urgent %}
                                        <span class="badge badge-info">
                                            <i class="fas fa-hourglass-half"></i> خلال {{ notification.days_remaining }} يوم
                                        </span>
                                    {% else %}
                                        <span class="badge badge-secondary">
                                            <i class="fas fa-calendar-alt"></i> خلال {{ notification.days_remaining }} يوم
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('inventory.create_audit') }}?warehouse_id={{ notification.audit.warehouse_id }}"
                                       class="btn btn-sm btn-primary" title="بدء جرد جديد">
                                        <i class="fas fa-plus"></i> بدء جرد
                                    </a>
                                    <a href="{{ url_for('inventory.audit_details', audit_id=notification.audit.id) }}"
                                       class="btn btn-sm btn-outline-info ml-1" title="عرض الجرد السابق">
                                        <i class="fas fa-eye"></i> عرض السابق
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-clipboard-check"></i> سجل عمليات الجرد
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>تاريخ الجرد</th>
                        <th>المستودع</th>
                        <th>الحالة</th>
                        <th>المستخدم</th>
                        <th>تاريخ الاكتمال</th>
                        <th>الجرد القادم</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for audit in audits %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ audit.audit_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ audit.warehouse.name }}</td>
                        <td>
                            {% if audit.status == 'in-progress' %}
                            <span class="badge badge-warning">قيد التنفيذ</span>
                            {% elif audit.status == 'completed' %}
                            <span class="badge badge-success">مكتمل</span>
                            {% elif audit.status == 'cancelled' %}
                            <span class="badge badge-danger">ملغي</span>
                            {% endif %}
                        </td>
                        <td>{{ audit.user.username }}</td>
                        <td>{% if audit.completed_at %}{{ audit.completed_at.strftime('%Y-%m-%d %H:%M') }}{% else %}-{% endif %}</td>
                        <td>
                            {% if audit.status == 'completed' and audit.next_audit_date %}
                                {% set days_remaining = (audit.next_audit_date - current_date).days %}
                                <span class="{% if days_remaining < 0 %}text-danger{% elif days_remaining == 0 %}text-warning{% elif days_remaining <= 7 %}text-info{% else %}text-muted{% endif %}">
                                    {{ audit.next_audit_date.strftime('%Y-%m-%d') }}
                                </span>
                                <br>
                                <small class="{% if days_remaining < 0 %}text-danger{% elif days_remaining == 0 %}text-warning{% elif days_remaining <= 7 %}text-info{% else %}text-muted{% endif %}">
                                    {% if days_remaining < 0 %}
                                        متأخر {{ days_remaining|abs }} يوم
                                    {% elif days_remaining == 0 %}
                                        اليوم
                                    {% else %}
                                        خلال {{ days_remaining }} يوم
                                    {% endif %}
                                </small>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('inventory.audit_details', audit_id=audit.id) }}"
                                    class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.is_admin_role or current_user.is_warehouse_manager %}
                                {% if audit.status != 'completed' %}
                                <a href="{{ url_for('inventory.audit_details', audit_id=audit.id) }}"
                                    class="btn btn-sm btn-outline-info" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                <button type="button" class="btn btn-sm btn-outline-danger" title="حذف"
                                    onclick="confirmDeleteAudit({{ audit.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center py-3">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> لا توجد عمليات جرد مسجلة حتى الآن
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

{% if audits %}
<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">الجرد حسب المستودع</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 250px;">
                    <canvas id="auditWarehouseChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">حالة عمليات الجرد</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 250px;">
                    <canvas id="auditStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function confirmDeleteAudit(auditId) {
    if (confirm('هل أنت متأكد من حذف هذا الجرد؟')) {
        window.location.href = '/inventory/audits/' + auditId + '/delete';
    }
}
</script>
{% if audits %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Count audits by warehouse
        const warehouseCounts = {};
        const warehouseNames = {};

        // Count audits by status
        let inProgressCount = 0;
        let completedCount = 0;
        let cancelledCount = 0;

        {% for audit in audits %}
        if (!warehouseCounts[{{ audit.warehouse_id }}]) {
            warehouseCounts[{{ audit.warehouse_id }}] = 0;
            warehouseNames[{{ audit.warehouse_id }}] = "{{ audit.warehouse.name }}";
        }
    warehouseCounts[{{ audit.warehouse_id }}]++;

    {% if audit.status == 'in-progress' %}
    inProgressCount++;
    {% elif audit.status == 'completed' %}
    completedCount++;
    {% elif audit.status == 'cancelled' %}
    cancelledCount++;
    {% endif %}
    {% endfor %}

    // Warehouse Chart
    const warehouseCtx = document.getElementById('auditWarehouseChart').getContext('2d');
    new Chart(warehouseCtx, {
        type: 'pie',
        data: {
            labels: Object.values(warehouseNames),
            datasets: [{
                data: Object.values(warehouseCounts),
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#6c757d'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            legend: {
                position: 'right',
                labels: {
                    fontFamily: 'Cairo, Tajawal, sans-serif',
                    fontColor: document.body.classList.contains('light-theme') ? '#212529' : '#f0f0f0'
                }
            }
        }
    });

    // Status Chart
    const statusCtx = document.getElementById('auditStatusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['قيد التنفيذ', 'مكتمل', 'ملغي'],
            datasets: [{
                data: [inProgressCount, completedCount, cancelledCount],
                backgroundColor: [
                    '#ffc107', // قيد التنفيذ
                    '#28a745', // مكتمل  (No change needed here as per the request)
                    '#dc3545'  // ملغي
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            legend: {
                position: 'right',
                labels: {
                    fontFamily: 'Cairo, Tajawal, sans-serif',
                    fontColor: document.body.classList.contains('light-theme') ? '#212529' : '#f0f0f0'
                }
            }
        }
    });
    });
</script>
{% endif %}
{% endblock %}