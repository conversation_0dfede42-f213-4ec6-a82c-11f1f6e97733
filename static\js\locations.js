// إدارة المواقع - JavaScript Functions
let allLocations = [];
let filteredLocations = [];

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadLocations();
    initializeEventListeners();
});

// تهيئة مستمعي الأحداث
function initializeEventListeners() {
    // البحث عند الكتابة
    document.getElementById('searchInput').addEventListener('input', debounce(searchLocations, 300));
    
    // البحث عند الضغط على Enter
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchLocations();
        }
    });
}

// تحميل المواقع
function loadLocations() {
    // في التطبيق الحقيقي، سيتم تحميل البيانات من الخادم
    // هنا نستخدم البيانات الموجودة في الصفحة
    const locationCards = document.querySelectorAll('.location-card');
    allLocations = Array.from(locationCards).map(card => ({
        id: card.dataset.locationId,
        element: card
    }));
    filteredLocations = [...allLocations];
}

// فتح نافذة إضافة موقع جديد
function openAddLocationModal() {
    const modal = new bootstrap.Modal(document.getElementById('addLocationModal'));
    modal.show();
    
    // إعادة تعيين النموذج
    document.getElementById('addLocationForm').reset();
}

// حفظ موقع جديد
function saveLocation() {
    const form = document.getElementById('addLocationForm');
    const formData = new FormData(form);
    
    // التحقق من صحة البيانات
    if (!formData.get('location_name')) {
        showAlert('يرجى إدخال اسم الموقع', 'error');
        return;
    }
    
    // تحويل البيانات إلى JSON
    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });
    
    // الحصول على CSRF token
    const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
    
    // إرسال البيانات للخادم
    fetch('/locations/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إضافة الموقع بنجاح', 'success');
            
            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addLocationModal'));
            modal.hide();
            
            // إعادة تحميل الصفحة
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showAlert(data.message || 'حدث خطأ أثناء إضافة الموقع', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'error');
    });
}

// عرض تفاصيل موقع
function viewLocation(locationId) {
    window.location.href = `/locations/${locationId}`;
}

// تعديل موقع
function editLocation(locationId) {
    window.location.href = `/locations/${locationId}/edit`;
}

// حذف موقع
function deleteLocation(locationId) {
    if (!confirm('هل أنت متأكد من حذف هذا الموقع؟\nلا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }
    
    // الحصول على CSRF token
    const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
    
    fetch(`/locations/${locationId}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حذف الموقع بنجاح', 'success');
            
            // إزالة البطاقة من الواجهة
            const locationCard = document.querySelector(`[data-location-id="${locationId}"]`);
            if (locationCard) {
                locationCard.remove();
            }
            
            // تحديث قائمة المواقع
            allLocations = allLocations.filter(loc => loc.id !== locationId.toString());
            filteredLocations = filteredLocations.filter(loc => loc.id !== locationId.toString());
            
            // التحقق من وجود مواقع
            checkEmptyState();
        } else {
            showAlert(data.message || 'حدث خطأ أثناء حذف الموقع', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'error');
    });
}

// البحث في المواقع
function searchLocations() {
    const query = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;
    
    // إخفاء جميع البطاقات
    allLocations.forEach(location => {
        location.element.style.display = 'none';
    });
    
    // إرسال طلب البحث للخادم
    const params = new URLSearchParams();
    if (query) params.append('q', query);
    if (typeFilter) params.append('type', typeFilter);
    
    fetch(`/locations/search?${params.toString()}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            filteredLocations = data.locations;
            displayFilteredLocations();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // في حالة الخطأ، استخدم البحث المحلي
        localSearch(query, typeFilter);
    });
}

// البحث المحلي (احتياطي)
function localSearch(query, typeFilter) {
    filteredLocations = allLocations.filter(location => {
        const card = location.element;
        const name = card.querySelector('h5').textContent.toLowerCase();
        const type = card.querySelector('.badge').textContent;
        
        const matchesQuery = !query || name.includes(query);
        const matchesType = !typeFilter || type === typeFilter;
        
        return matchesQuery && matchesType;
    });
    
    displayFilteredLocations();
}

// عرض المواقع المفلترة
function displayFilteredLocations() {
    // إخفاء جميع البطاقات
    allLocations.forEach(location => {
        location.element.style.display = 'none';
    });
    
    // عرض المواقع المفلترة
    if (filteredLocations.length > 0) {
        filteredLocations.forEach(location => {
            const element = allLocations.find(loc => loc.id === location.id.toString())?.element;
            if (element) {
                element.style.display = 'block';
            }
        });
    }
    
    checkEmptyState();
}

// تصفية حسب النوع
function filterByType() {
    searchLocations();
}

// إعادة تعيين الفلاتر
function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('typeFilter').value = '';
    
    // عرض جميع المواقع
    allLocations.forEach(location => {
        location.element.style.display = 'block';
    });
    
    filteredLocations = [...allLocations];
    checkEmptyState();
}

// التحقق من حالة عدم وجود مواقع
function checkEmptyState() {
    const visibleCards = document.querySelectorAll('.location-card[style*="block"], .location-card:not([style*="none"])');
    const emptyState = document.getElementById('emptyState');
    
    if (visibleCards.length === 0) {
        emptyState.style.display = 'block';
    } else {
        emptyState.style.display = 'none';
    }
}

// عرض رسالة تنبيه
function showAlert(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const alert = document.createElement('div');
    alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';
    
    alert.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

// دالة تأخير للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تصدير البيانات (للتطوير المستقبلي)
function exportLocations() {
    // يمكن تطوير هذه الوظيفة لتصدير البيانات بصيغ مختلفة
    showAlert('وظيفة التصدير قيد التطوير', 'info');
}

// طباعة قائمة المواقع
function printLocations() {
    window.print();
}
