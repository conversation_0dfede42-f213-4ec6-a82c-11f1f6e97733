#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تشخيص سريع لمشكلة ملفات التعليمات في المواقع
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import os

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'database': 'military_warehouse',
    'user': 'postgres',
    'password': 'postgres'
}

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def diagnose_location_instructions():
    """تشخيص مشكلة ملفات التعليمات"""
    
    print("🔍 تشخيص مشكلة ملفات التعليمات في المواقع")
    print("=" * 50)
    
    conn = connect_to_database()
    if not conn:
        return
    
    cursor = conn.cursor()
    
    try:
        # 1. فحص وجود جدول المواقع
        print("1️⃣ فحص جدول المواقع...")
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'locations'
            );
        """)
        
        if cursor.fetchone()[0]:
            print("   ✅ جدول المواقع موجود")
        else:
            print("   ❌ جدول المواقع غير موجود!")
            return
        
        # 2. فحص أعمدة جدول المواقع
        print("\n2️⃣ فحص أعمدة جدول المواقع...")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'locations'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        print(f"   📊 عدد الأعمدة: {len(columns)}")
        
        instructions_file_exists = False
        for column_name, data_type, is_nullable in columns:
            if column_name == 'instructions_file':
                instructions_file_exists = True
                print(f"   ✅ instructions_file: {data_type} ({'NULL' if is_nullable == 'YES' else 'NOT NULL'})")
            elif column_name in ['id', 'name', 'serial_number', 'type', 'status']:
                print(f"   ✅ {column_name}: {data_type}")
        
        if not instructions_file_exists:
            print("   ❌ عمود instructions_file غير موجود!")
            print("   💡 هذا هو سبب المشكلة!")
        
        # 3. فحص عدد المواقع
        print("\n3️⃣ فحص المواقع الموجودة...")
        cursor.execute("SELECT COUNT(*) FROM locations;")
        location_count = cursor.fetchone()[0]
        print(f"   📊 عدد المواقع: {location_count}")
        
        if location_count > 0:
            # عرض عينة من المواقع
            cursor.execute("SELECT id, name, serial_number FROM locations LIMIT 3;")
            sample_locations = cursor.fetchall()
            print("   📋 عينة من المواقع:")
            for location_id, name, serial_number in sample_locations:
                print(f"      📍 {name} (ID: {location_id}, Serial: {serial_number})")
        
        # 4. فحص مجلد الرفع
        print("\n4️⃣ فحص مجلد الرفع...")
        upload_dir = os.path.join('static', 'uploads', 'location_instructions')
        
        if os.path.exists(upload_dir):
            print(f"   ✅ مجلد الرفع موجود: {upload_dir}")
            
            # فحص الملفات الموجودة
            files = os.listdir(upload_dir)
            print(f"   📊 عدد الملفات: {len(files)}")
            
            if files:
                print("   📄 الملفات الموجودة:")
                for file in files[:5]:  # أول 5 ملفات
                    file_path = os.path.join(upload_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"      📄 {file} ({file_size} بايت)")
            
            # فحص صلاحيات الكتابة
            if os.access(upload_dir, os.W_OK):
                print("   ✅ صلاحيات الكتابة متوفرة")
            else:
                print("   ❌ صلاحيات الكتابة غير متوفرة")
        else:
            print(f"   ❌ مجلد الرفع غير موجود: {upload_dir}")
        
        # 5. فحص ملفات التعليمات (إذا كان العمود موجود)
        if instructions_file_exists:
            print("\n5️⃣ فحص ملفات التعليمات المحفوظة...")
            try:
                cursor.execute("""
                    SELECT id, name, instructions_file 
                    FROM locations 
                    WHERE instructions_file IS NOT NULL 
                    AND instructions_file != ''
                    LIMIT 10;
                """)
                
                locations_with_files = cursor.fetchall()
                
                if locations_with_files:
                    print(f"   📊 مواقع تحتوي على ملفات: {len(locations_with_files)}")
                    for location_id, name, instructions_file in locations_with_files:
                        print(f"      📍 {name}")
                        print(f"         📄 الملف: {instructions_file}")
                        
                        # فحص وجود الملف فعلياً
                        if instructions_file:
                            full_path = os.path.join(upload_dir, os.path.basename(instructions_file))
                            if os.path.exists(full_path):
                                print(f"         ✅ الملف موجود على القرص")
                            else:
                                print(f"         ❌ الملف غير موجود على القرص")
                else:
                    print("   ℹ️  لا توجد مواقع تحتوي على ملفات تعليمات")
            except Exception as e:
                print(f"   ❌ خطأ في فحص ملفات التعليمات: {e}")
        
        # 6. التشخيص النهائي
        print("\n" + "=" * 50)
        print("📋 تقرير التشخيص:")
        
        if not instructions_file_exists:
            print("❌ المشكلة الرئيسية: عمود instructions_file غير موجود في قاعدة البيانات")
            print("💡 الحل: تشغيل سكريبت fix_location_instructions.py")
        elif not os.path.exists(upload_dir):
            print("❌ المشكلة الرئيسية: مجلد الرفع غير موجود")
            print("💡 الحل: إنشاء مجلد الرفع")
        elif not os.access(upload_dir, os.W_OK):
            print("❌ المشكلة الرئيسية: لا توجد صلاحيات كتابة في مجلد الرفع")
            print("💡 الحل: تعديل صلاحيات المجلد")
        else:
            print("✅ جميع المتطلبات متوفرة")
            print("💡 المشكلة قد تكون في الكود أو في عملية الرفع")
        
        print("\n🔧 الخطوات المقترحة:")
        print("1. تشغيل سكريبت fix_location_instructions.py")
        print("2. إعادة تشغيل الخادم")
        print("3. محاولة رفع ملف جديد")
        print("4. فحص تفاصيل الموقع")
        
    except Exception as e:
        print(f"❌ خطأ أثناء التشخيص: {e}")
    
    finally:
        cursor.close()
        conn.close()

def main():
    """الدالة الرئيسية"""
    print("🔍 أداة تشخيص مشكلة ملفات التعليمات")
    print("📋 هذه الأداة تحدد سبب عدم ظهور المرفقات")
    print("🔒 آمنة تماماً - فقط قراءة وتشخيص")
    print("=" * 50)
    
    diagnose_location_instructions()

if __name__ == "__main__":
    main()
