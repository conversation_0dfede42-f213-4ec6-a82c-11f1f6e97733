{% extends 'base.html' %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card bg-dark text-white">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4>رمز QR للسلاح: {{ weapon.name }}</h4>
                    <a href="{{ url_for('weapons.details', weapon_id=weapon.id) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i> العودة
                    </a>
                </div>
                <div class="card-body text-center">
                    <div class="qr-code-container mb-4">
                        <div id="qrcode-display" class="mx-auto"></div>
                    </div>

                    <div class="weapon-info mb-4">
                        <p><strong>الرقم التسلسلي:</strong> {{ weapon.serial_number }}</p>
                        <p><strong>اسم السلاح:</strong> {{ weapon.name }}</p>
                        <p><strong>النوع:</strong> {% if weapon.type == 'pistol' %}مسدس{% elif weapon.type == 'rifle' %}بندقية{% elif weapon.type == 'sniper' %}قناص{% elif weapon.type == 'machine_gun' %}رشاش{% else %}{{ weapon.type }}{% endif %}</p>
                        <p><strong>المستودع:</strong> {{ weapon.warehouse.name }}</p>
                    </div>

                    <div class="action-buttons">
                        <a href="{{ url_for('weapons.get_qr_code', weapon_id=weapon.id, download=1) }}" class="btn btn-primary">
                            <i class="fas fa-download"></i> تحميل رمز QR
                        </a>
                        <a href="{{ url_for('weapons.details', weapon_id=weapon.id) }}" class="btn btn-secondary">
                            <i class="fas fa-info-circle"></i> عرض تفاصيل السلاح
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Generate QR code with JavaScript
        try {
            var qrContainer = document.getElementById("qrcode-display");
            if (!qrContainer) {
                console.error('QR container not found');
                return;
            }

            // Check if QRCode library is available
            if (typeof QRCode === 'undefined') {
                console.error('QRCode library not loaded');
                qrContainer.innerHTML = '<div class="alert alert-warning">مكتبة QR Code غير متوفرة</div>';
                return;
            }

            // Clear any existing content
            qrContainer.innerHTML = '';

            // Generate QR code with full serial number using optimized settings
            var qrData = "{{ weapon.serial_number }}";
            console.log('Generating QR code with data:', qrData);

            // Try different approaches if the first one fails
            try {
                var qrcode = new QRCode(qrContainer, {
                    text: qrData,
                    width: 200,
                    height: 200,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.L,
                    typeNumber: 0  // Auto-detect first
                });
            } catch (e) {
                console.log('Trying with version 6...');
                try {
                    qrContainer.innerHTML = '';
                    var qrcode = new QRCode(qrContainer, {
                        text: qrData,
                        width: 200,
                        height: 200,
                        colorDark: "#000000",
                        colorLight: "#ffffff",
                        correctLevel: QRCode.CorrectLevel.L,
                        typeNumber: 6  // Higher version for more data
                    });
                } catch (e2) {
                    console.log('Trying with shortened text...');
                    qrContainer.innerHTML = '';
                    var shortData = qrData.substring(0, 20);
                    var qrcode = new QRCode(qrContainer, {
                        text: shortData,
                        width: 200,
                        height: 200,
                        colorDark: "#000000",
                        colorLight: "#ffffff",
                        correctLevel: QRCode.CorrectLevel.L,
                        typeNumber: 0
                    });
                    // Add warning about shortened data
                    qrContainer.insertAdjacentHTML('afterend',
                        '<div class="alert alert-warning mt-2">تم اقتطاع الرقم التسلسلي لـ ' + shortData + '</div>');
                }
            }

            console.log('✅ QR code generated successfully');

        } catch (error) {
            console.error('❌ Error generating QR code:', error);
            var qrContainer = document.getElementById("qrcode-display");
            if (qrContainer) {
                qrContainer.innerHTML = '<div class="alert alert-danger">خطأ في إنشاء QR Code</div>';
            }
        }
    });
</script>
{% endblock %}
