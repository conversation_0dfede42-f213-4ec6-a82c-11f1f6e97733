import uuid
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Length
from db import db
from models import Weapon, Personnel, ActivityLog, WeaponTransaction

# إنشاء Blueprint للحالات
statuses_bp = Blueprint('statuses', __name__, url_prefix='/statuses')

# نموذج تحديث الحالات
class StatusUpdateForm(FlaskForm):
    weapon_status = SelectField('حالة الأسلحة', choices=[
        ('نشط', 'نشط'),
        ('إجازة', 'إجازة'),
        ('مهمة', 'مهمة'),
        ('صيانة', 'صيانة'),
        ('دورة', 'دورة'),
        ('شاغر', 'شاغر'),
        ('مستلم', 'مستلم'),
        ('رماية', 'رماية'),
        ('أخرى', 'أخرى')
    ])
    personnel_status = SelectField('حالة الأفراد', choices=[
        ('نشط', 'نشط'),
        ('إجازة', 'إجازة'),
        ('مهمة', 'مهمة'),
        ('دورة', 'دورة'),
        ('مستلم', 'مستلم'),
        ('رماية', 'رماية')
    ])
    barcodes = TextAreaField('الباركودات', validators=[DataRequired()])
    notes = TextAreaField('ملاحظات')
    submit = SubmitField('حفظ التغييرات')

# الصفحة الرئيسية للحالات
@statuses_bp.route('/')
@login_required
def index():
    form = StatusUpdateForm()
    return render_template('statuses/index.html', form=form, title='إدارة الحالات')

# معالجة تحديث الحالات
@statuses_bp.route('/update', methods=['POST'])
@login_required
def update_status():
    form = StatusUpdateForm()
    if form.validate_on_submit():
        weapon_status = form.weapon_status.data
        personnel_status = form.personnel_status.data
        barcodes_text = form.barcodes.data
        notes = form.notes.data

        # تقسيم الباركودات إلى قائمة
        barcodes = [barcode.strip() for barcode in barcodes_text.split('\n') if barcode.strip()]

        # إحصائيات التحديث
        stats = {
            'total_barcodes': len(barcodes),
            'weapons_updated': 0,
            'personnel_updated': 0,
            'not_found': 0,
            'no_access': 0,
            'errors': []
        }

        # قائمة الأسلحة والأفراد الذين تم تحديثهم
        updated_weapons = []
        updated_personnel = []
        not_found_barcodes = []
        no_access_barcodes = []

        # معالجة كل باركود
        for barcode in barcodes:
            # البحث عن السلاح بواسطة الباركود
            weapon = Weapon.query.filter_by(barcode=barcode).first()

            # إذا لم يتم العثور على السلاح، جرب البحث بواسطة الرقم التسلسلي
            if not weapon:
                weapon = Weapon.query.filter_by(serial_number=barcode).first()

            # إذا لم يتم العثور على السلاح، جرب البحث بواسطة رمز QR
            if not weapon:
                weapon = Weapon.query.filter_by(qr_code=barcode).first()

            # إذا لم يتم العثور على السلاح، جرب البحث عن فرد بواسطة رقم الهوية الوطنية
            personnel_from_id = None
            if not weapon:
                personnel_from_id = Personnel.query.filter_by(phone=barcode).first()
                if personnel_from_id:
                    # التحقق من صلاحية الوصول إلى المستودع (السماح لمناوب السرية)
                    if not current_user.is_admin_role and not current_user.is_company_duty and personnel_from_id.warehouse not in current_user.warehouses:
                        stats['no_access'] += 1
                        no_access_barcodes.append(barcode)
                        continue

                    # تحديث حالة الفرد
                    old_personnel_status = personnel_from_id.status
                    personnel_from_id.status = personnel_status
                    stats['personnel_updated'] += 1
                    updated_personnel.append(personnel_from_id)

                    # تسجيل تغيير حالة الفرد مع وصف محسن
                    log = ActivityLog(
                        action="تغيير حالة فرد",
                        description=f"تم تغيير حالة الفرد: {personnel_from_id.name} من {old_personnel_status} إلى {personnel_status} عبر صفحة الحالات (باستخدام رقم الهوية الوطنية)",
                        ip_address=request.remote_addr,
                        user_id=current_user.id,
                        warehouse_id=personnel_from_id.warehouse_id
                    )

                    # حفظ إدخال في التقرير الأسبوعي
                    from utils import save_weekly_report_entry
                    save_weekly_report_entry(personnel_from_id, old_personnel_status, personnel_status, current_user, "تغيير حالة عبر الباركود")
                    db.session.add(log)

                    # تحديث حالة الأسلحة المرتبطة بالفرد
                    if personnel_from_id.weapons:
                        for weapon_item in personnel_from_id.weapons:
                            old_weapon_status = weapon_item.status
                            weapon_item.status = weapon_status
                            stats['weapons_updated'] += 1
                            updated_weapons.append(weapon_item)

                            # تسجيل تغيير حالة السلاح مع وصف محسن
                            log = ActivityLog(
                                action="تغيير حالة سلاح",
                                description=f"تم تغيير حالة السلاح للفرد {personnel_from_id.name}: {weapon_item.name} (الرقم التسلسلي: {weapon_item.serial_number}) من {old_weapon_status} إلى {weapon_status} عبر صفحة الحالات (باستخدام رقم الهوية الوطنية)",
                                ip_address=request.remote_addr,
                                user_id=current_user.id,
                                warehouse_id=weapon_item.warehouse_id
                            )
                            db.session.add(log)
                    continue

            # إذا لم يتم العثور على السلاح أو فرد
            if not weapon and not personnel_from_id:
                stats['not_found'] += 1
                not_found_barcodes.append(barcode)
                continue

            # التحقق من صلاحية الوصول إلى المستودع (السماح لمناوب السرية)
            if not current_user.is_admin_role and not current_user.is_company_duty and weapon.warehouse not in current_user.warehouses:
                stats['no_access'] += 1
                no_access_barcodes.append(barcode)
                continue

            # تحديث حالة السلاح
            old_weapon_status = weapon.status
            weapon.status = weapon_status
            stats['weapons_updated'] += 1
            updated_weapons.append(weapon)

            # Get personnel associated with the weapon
            personnel_name = None
            if weapon.personnel:
                for person in weapon.personnel:
                    personnel_name = person.name
                    break

            # تسجيل تغيير حالة السلاح مع وصف محسن
            log = ActivityLog(
                action="تغيير حالة سلاح",
                description=f"تم تغيير حالة السلاح{' للفرد ' + personnel_name if personnel_name else ''}: {weapon.name} (الرقم التسلسلي: {weapon.serial_number}) من {old_weapon_status} إلى {weapon_status} عبر صفحة الحالات",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=weapon.warehouse_id
            )
            db.session.add(log)

            # تحديث حالة الأفراد المرتبطين بالسلاح
            for person in weapon.personnel:
                if person not in updated_personnel:
                    old_personnel_status = person.status
                    person.status = personnel_status
                    stats['personnel_updated'] += 1
                    updated_personnel.append(person)

                    # تسجيل تغيير حالة الفرد مع وصف محسن
                    log = ActivityLog(
                        action="تغيير حالة فرد",
                        description=f"تم تغيير حالة الفرد: {person.name} من {old_personnel_status} إلى {personnel_status} عبر صفحة الحالات",
                        ip_address=request.remote_addr,
                        user_id=current_user.id,
                        warehouse_id=person.warehouse_id
                    )
                    db.session.add(log)

                    # حفظ إدخال في التقرير الأسبوعي
                    from utils import save_weekly_report_entry
                    save_weekly_report_entry(person, old_personnel_status, personnel_status, current_user, "تغيير حالة عبر سلاح")

        # حفظ التغييرات في قاعدة البيانات
        db.session.commit()

        # إعداد رسالة النجاح
        success_message = f"تم تحديث {stats['weapons_updated']} سلاح و {stats['personnel_updated']} فرد بنجاح."
        if stats['not_found'] > 0:
            success_message += f" لم يتم العثور على {stats['not_found']} باركود."
        if stats['no_access'] > 0:
            success_message += f" ليس لديك صلاحية للوصول إلى {stats['no_access']} سلاح."

        flash(success_message, 'success')
        return redirect(url_for('statuses.index'))

    flash('حدث خطأ أثناء معالجة النموذج. يرجى المحاولة مرة أخرى.', 'danger')
    return redirect(url_for('statuses.index'))

# البحث عن سلاح أو فرد بواسطة الباركود أو رقم الهوية الوطنية (للاستخدام مع AJAX)
@statuses_bp.route('/search_weapon', methods=['POST'])
@login_required
def search_weapon():
    barcode = request.form.get('barcode', '')

    # البحث عن السلاح بواسطة الباركود
    weapon = Weapon.query.filter_by(barcode=barcode).first()

    # إذا لم يتم العثور على السلاح، جرب البحث بواسطة الرقم التسلسلي
    if not weapon:
        weapon = Weapon.query.filter_by(serial_number=barcode).first()

    # إذا لم يتم العثور على السلاح، جرب البحث بواسطة رمز QR
    if not weapon:
        weapon = Weapon.query.filter_by(qr_code=barcode).first()

    # إذا لم يتم العثور على السلاح، جرب البحث عن فرد بواسطة رقم الهوية الوطنية
    if not weapon:
        personnel = Personnel.query.filter_by(phone=barcode).first()
        if personnel:
            # إعداد معلومات الفرد
            personnel_info = {
                'found': True,
                'is_personnel': True,
                'id': personnel.id,
                'name': personnel.name,
                'personnel_id': personnel.personnel_id,
                'national_id': personnel.phone,
                'status': personnel.status,
                'warehouse': personnel.warehouse.name,
                'weapons': []
            }

            # إضافة رسالة مناسبة
            if not personnel.weapons:
                personnel_info['message'] = 'تم العثور على الفرد ولكن ليس لديه أسلحة مرتبطة'
            else:
                personnel_info['message'] = f'تم العثور على الفرد وله {len(personnel.weapons)} سلاح مرتبط'
                # إضافة معلومات الأسلحة المرتبطة
                for weapon_item in personnel.weapons:
                    personnel_info['weapons'].append({
                        'id': weapon_item.id,
                        'name': weapon_item.name,
                        'serial_number': weapon_item.serial_number,
                        'status': weapon_item.status,
                        'barcode': weapon_item.barcode
                    })

            return jsonify(personnel_info)

    if not weapon:
        return jsonify({'found': False, 'message': 'لم يتم العثور على سلاح أو فرد بهذا الرقم'})

    # التحقق من صلاحية الوصول إلى المستودع (السماح لمناوب السرية)
    if not current_user.is_admin_role and not current_user.is_company_duty and weapon.warehouse not in current_user.warehouses:
        return jsonify({'found': False, 'message': 'ليس لديك صلاحية للوصول إلى هذا السلاح'})

    # إعداد معلومات السلاح
    weapon_info = {
        'found': True,
        'id': weapon.id,
        'name': weapon.name,
        'serial_number': weapon.serial_number,
        'status': weapon.status,
        'warehouse': weapon.warehouse.name,
        'personnel': []
    }

    # إضافة معلومات الأفراد المرتبطين
    for person in weapon.personnel:
        weapon_info['personnel'].append({
            'id': person.id,
            'name': person.name,
            'personnel_id': person.personnel_id,
            'status': person.status
        })

    return jsonify(weapon_info)
