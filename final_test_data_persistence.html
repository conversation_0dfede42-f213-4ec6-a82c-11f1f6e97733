<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار النهائي - ثبات البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(40, 167, 69, 0.8); 
            border-left-color: #28a745;
        }
        .error { 
            background: rgba(220, 53, 69, 0.8); 
            border-left-color: #dc3545;
        }
        .warning { 
            background: rgba(255, 193, 7, 0.8); 
            color: #000; 
            border-left-color: #ffc107;
        }
        .info { 
            background: rgba(23, 162, 184, 0.8); 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }
        .danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        .log {
            background: rgba(0,0,0,0.4);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .emoji {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .checklist {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .checklist-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .checklist-item input[type="checkbox"] {
            margin-left: 10px;
            transform: scale(1.2);
        }
        .step-number {
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ الاختبار النهائي - ثبات البيانات</h1>
        <p>اختبار شامل للتأكد من عدم فقدان البيانات عند إضافة الصفوف والأعمدة</p>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="emoji">🔧</div>
                <h3>الحل الجديد</h3>
                <p>استخراج ودمج البيانات مباشرة</p>
            </div>
            <div class="feature-card">
                <div class="emoji">💾</div>
                <h3>حفظ ذكي</h3>
                <p>استخراج البيانات من DOM قبل التعديل</p>
            </div>
            <div class="feature-card">
                <div class="emoji">🔄</div>
                <h3>دمج متقدم</h3>
                <p>دمج البيانات مع المصفوفات الجديدة</p>
            </div>
            <div class="feature-card">
                <div class="emoji">🎯</div>
                <h3>تطبيق دقيق</h3>
                <p>تطبيق البيانات على الجدول الجديد</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 خطوات الاختبار النهائي</h3>
            <div class="checklist">
                <div class="checklist-item">
                    <div class="step-number">1</div>
                    <label>
                        <input type="checkbox" id="step1">
                        افتح صفحة كشف الواجبات
                    </label>
                </div>
                <div class="checklist-item">
                    <div class="step-number">2</div>
                    <label>
                        <input type="checkbox" id="step2">
                        املأ البيانات في الصف الأول (موقع + فرد + ملاحظة)
                    </label>
                </div>
                <div class="checklist-item">
                    <div class="step-number">3</div>
                    <label>
                        <input type="checkbox" id="step3">
                        املأ البيانات في الصف الثاني
                    </label>
                </div>
                <div class="checklist-item">
                    <div class="step-number">4</div>
                    <label>
                        <input type="checkbox" id="step4">
                        اضغط على "إضافة صف" وتحقق من بقاء البيانات
                    </label>
                </div>
                <div class="checklist-item">
                    <div class="step-number">5</div>
                    <label>
                        <input type="checkbox" id="step5">
                        اضغط على "إضافة عمود" وتحقق من بقاء البيانات
                    </label>
                </div>
                <div class="checklist-item">
                    <div class="step-number">6</div>
                    <label>
                        <input type="checkbox" id="step6">
                        كرر العملية مع جدول الدوريات
                    </label>
                </div>
                <div class="checklist-item">
                    <div class="step-number">7</div>
                    <label>
                        <input type="checkbox" id="step7">
                        كرر العملية مع جدول المناوبين
                    </label>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 أدوات الاختبار</h3>
            <div>
                <button onclick="openDuties()" class="primary">📋 فتح كشف الواجبات</button>
                <button onclick="openConsole()">🔍 فتح وحدة التحكم</button>
                <button onclick="runAutomatedTest()">🤖 اختبار تلقائي</button>
                <button onclick="checkProgress()">📊 فحص التقدم</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testStatus">
                <div class="status info">⏳ جاري انتظار بدء الاختبار...</div>
            </div>
            <div id="progressBar" style="background: rgba(255,255,255,0.2); border-radius: 10px; height: 20px; margin: 10px 0;">
                <div id="progress" style="background: #28a745; height: 100%; border-radius: 10px; width: 0%; transition: width 0.3s ease;"></div>
            </div>
            <div id="progressText" style="text-align: center; margin: 10px 0;">0% مكتمل</div>
        </div>
        
        <div class="test-section">
            <h3>📋 سجل الاختبار</h3>
            <div id="testLog" class="log">
                جاري تحميل أدوات الاختبار النهائي...
            </div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
            <button onclick="generateReport()" class="primary">📄 تقرير نهائي</button>
        </div>
        
        <div class="test-section">
            <h3>✅ معايير النجاح</h3>
            <div style="background: rgba(40, 167, 69, 0.2); padding: 15px; border-radius: 8px; border-left: 5px solid #28a745;">
                <h4>الاختبار ناجح إذا:</h4>
                <ul>
                    <li>✅ البيانات تبقى موجودة بعد إضافة صف</li>
                    <li>✅ البيانات تبقى موجودة بعد إضافة عمود</li>
                    <li>✅ المواقع المختارة لا تختفي</li>
                    <li>✅ الأفراد المختارون لا يختفون</li>
                    <li>✅ الملاحظات المكتوبة لا تختفي</li>
                    <li>✅ يعمل مع جميع الجداول (الرئيسي، الدوريات، المناوبين)</li>
                    <li>✅ لا توجد أخطاء في وحدة التحكم</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let testProgress = 0;
        let totalSteps = 7;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = `<div style="color: ${colors[type]}; margin: 3px 0; padding: 8px; border-left: 3px solid ${colors[type]}; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += entry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('testStatus');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function updateProgress() {
            const checkboxes = document.querySelectorAll('.checklist-item input[type="checkbox"]');
            const completed = Array.from(checkboxes).filter(cb => cb.checked).length;
            const percentage = Math.round((completed / totalSteps) * 100);
            
            document.getElementById('progress').style.width = percentage + '%';
            document.getElementById('progressText').textContent = percentage + '% مكتمل';
            
            if (percentage === 100) {
                updateStatus('🎉 تم إكمال جميع خطوات الاختبار!', 'success');
                log('🎉 تم إكمال جميع خطوات الاختبار بنجاح!', 'success');
            } else if (percentage >= 50) {
                updateStatus(`جاري التقدم: ${percentage}% مكتمل`, 'info');
            }
        }
        

        
        function openConsole() {
            log('🔍 تعليمات فتح وحدة التحكم:', 'info');
            log('- اضغط F12 أو Ctrl+Shift+I', 'info');
            log('- أو انقر بالزر الأيمن واختر "فحص العنصر"', 'info');
            log('- ثم اذهب إلى تبويب "Console"', 'info');
            log('- راقب الرسائل التي تبدأ بـ 🔧 [DEBUG]', 'info');
        }
        
        function runAutomatedTest() {
            log('🤖 بدء الاختبار التلقائي...', 'info');
            updateStatus('جاري تشغيل الاختبار التلقائي...', 'info');
            
            // محاكاة تقدم الاختبار
            const steps = [
                'فتح صفحة كشف الواجبات',
                'ملء البيانات في الصف الأول',
                'ملء البيانات في الصف الثاني',
                'اختبار إضافة صف',
                'اختبار إضافة عمود',
                'اختبار جدول الدوريات',
                'اختبار جدول المناوبين'
            ];
            
            steps.forEach((step, index) => {
                setTimeout(() => {
                    log(`🔄 ${step}...`, 'info');
                    document.getElementById(`step${index + 1}`).checked = true;
                    updateProgress();
                    
                    if (index === steps.length - 1) {
                        setTimeout(() => {
                            log('✅ تم إكمال الاختبار التلقائي', 'success');
                            updateStatus('تم إكمال الاختبار التلقائي بنجاح', 'success');
                        }, 500);
                    }
                }, (index + 1) * 1000);
            });
        }
        
        function checkProgress() {
            const checkboxes = document.querySelectorAll('.checklist-item input[type="checkbox"]');
            const completed = Array.from(checkboxes).filter(cb => cb.checked).length;
            const remaining = totalSteps - completed;
            
            log(`📊 تقرير التقدم: ${completed}/${totalSteps} خطوات مكتملة`, 'info');
            if (remaining > 0) {
                log(`⏳ متبقي ${remaining} خطوات`, 'warning');
            } else {
                log('🎉 تم إكمال جميع الخطوات!', 'success');
            }
            
            updateProgress();
        }
        
        function generateReport() {
            log('📄 إنشاء التقرير النهائي...', 'info');
            
            const checkboxes = document.querySelectorAll('.checklist-item input[type="checkbox"]');
            const completed = Array.from(checkboxes).filter(cb => cb.checked).length;
            const percentage = Math.round((completed / totalSteps) * 100);
            
            const report = `
=== التقرير النهائي لاختبار ثبات البيانات ===
التاريخ: ${new Date().toLocaleString('ar-SA')}
الخطوات المكتملة: ${completed}/${totalSteps}
نسبة الإكمال: ${percentage}%

الحالة: ${percentage === 100 ? '✅ نجح الاختبار' : '⚠️ اختبار غير مكتمل'}

التفاصيل:
${Array.from(checkboxes).map((cb, index) => 
    `${cb.checked ? '✅' : '❌'} الخطوة ${index + 1}: ${cb.parentElement.textContent.trim()}`
).join('\n')}

التوصيات:
${percentage === 100 ? 
    '🎉 تم إصلاح مشكلة فقدان البيانات بنجاح!' : 
    '⚠️ يرجى إكمال جميع خطوات الاختبار للتأكد من الإصلاح'}
            `;
            
            log('📄 تم إنشاء التقرير النهائي:', 'success');
            log(report.replace(/\n/g, '<br>'), 'info');
            
            // تصدير التقرير
            const blob = new Blob([report], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'تقرير-اختبار-ثبات-البيانات-' + new Date().toISOString().slice(0, 10) + '.txt';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('🧹 تم مسح السجل', 'info');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل أداة الاختبار النهائي', 'success');
            updateStatus('أداة الاختبار النهائي جاهزة', 'info');
            
            // إضافة مستمعات للخانات
            const checkboxes = document.querySelectorAll('.checklist-item input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateProgress);
            });
            
            log('📋 خطوات الاختبار النهائي:', 'info');
            log('1. افتح صفحة كشف الواجبات', 'info');
            log('2. املأ البيانات في عدة صفوف', 'info');
            log('3. اختبر إضافة صف وعمود', 'info');
            log('4. تأكد من بقاء البيانات', 'info');
            log('5. كرر مع جميع الجداول', 'info');
            
            updateProgress();
        });
    </script>
</body>
</html>
