{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>إعادة تعيين كلمة المرور: {{ user.username }}</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة المستخدمين
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-key"></i> إعادة تعيين كلمة المرور</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> سيتم إعادة تعيين كلمة المرور للمستخدم <strong>{{ user.username }}</strong>. يرجى إدخال كلمة مرور جديدة.
        </div>
        <form method="POST" action="{{ url_for('auth.reset_user_password', user_id=user.id) }}">
            {{ form.hidden_tag() }}
            <div class="form-group row">
                <label for="new_password" class="col-sm-3 col-form-label">كلمة المرور الجديدة</label>
                <div class="col-sm-9">
                    {{ form.new_password(class="form-control" + (" is-invalid" if form.new_password.errors else "")) }}
                    {% for error in form.new_password.errors %}
                    <div class="invalid-feedback">
                        {{ error }}
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="form-group row">
                <label for="confirm_password" class="col-sm-3 col-form-label">تأكيد كلمة المرور الجديدة</label>
                <div class="col-sm-9">
                    {{ form.confirm_password(class="form-control" + (" is-invalid" if form.confirm_password.errors else "")) }}
                    {% for error in form.confirm_password.errors %}
                    <div class="invalid-feedback">
                        {{ error }}
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="form-group row">
                <div class="col-sm-9 offset-sm-3">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">إلغاء</a>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}
