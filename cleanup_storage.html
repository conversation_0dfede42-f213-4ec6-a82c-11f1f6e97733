<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظيف البيانات المتضاربة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .cleanup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
            margin: 50px auto;
            max-width: 800px;
        }
        
        .status-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .btn-cleanup {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-cleanup:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
            color: white;
        }
        
        .btn-status {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .btn-status:hover {
            color: white;
            transform: translateY(-1px);
        }
        
        .alert-custom {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .step {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 8px 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="cleanup-container">
            <div class="text-center">
                <div class="icon">🧹</div>
                <h1 class="mb-4">تنظيف البيانات المتضاربة</h1>
                <p class="lead text-muted">حل مشكلة تداخل البيانات بين كشف الواجبات وكشف الاستلامات</p>
            </div>
            
            <div class="alert alert-warning alert-custom">
                <h5>⚠️ تحذير مهم</h5>
                <p class="mb-0">هذه العملية ستقوم بحذف البيانات المتضاربة وإعادة تعيين النظام. تأكد من حفظ أي بيانات مهمة قبل المتابعة.</p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="step">
                        <h6>🔍 الخطوة 1: فحص الحالة</h6>
                        <p>فحص البيانات المتضاربة في localStorage</p>
                        <button class="btn btn-status" onclick="checkStatus()">فحص الحالة</button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="step">
                        <h6>🧹 الخطوة 2: التنظيف</h6>
                        <p>حذف البيانات المتضاربة وإعادة التعيين</p>
                        <button class="btn btn-cleanup" onclick="performCleanup()">تنظيف البيانات</button>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h5>📊 حالة النظام:</h5>
                <div id="statusOutput" class="status-box">
                    اضغط على "فحص الحالة" لعرض حالة البيانات الحالية...
                </div>
            </div>
            
            <div class="alert alert-info alert-custom mt-4">
                <h6>📋 ما يحدث عند التنظيف:</h6>
                <ul class="mb-0">
                    <li>حذف المفاتيح المتضاربة: <code>patrolData</code>, <code>shiftsData</code>, <code>assignmentData</code></li>
                    <li>إنشاء مفاتيح منفصلة: <code>duties_*</code> لكشف الواجبات و <code>receipts_*</code> لكشف الاستلامات</li>
                    <li>إعادة تعيين البيانات الافتراضية لكلا النظامين</li>
                    <li>ضمان عدم تداخل البيانات مستقبلاً</li>
                </ul>
            </div>
            
            <div class="text-center mt-4">
                <div class="row">
                    <div class="col-md-6">
                        <a href="/receipts" class="btn btn-outline-primary w-100">
                            📋 كشف الاستلامات
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="/duties/test" class="btn btn-outline-success w-100">
                            📝 كشف الواجبات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة فحص الحالة
        function checkStatus() {
            const output = document.getElementById('statusOutput');
            output.innerHTML = '🔍 جاري فحص الحالة...\n';
            
            setTimeout(() => {
                let statusText = '📊 حالة localStorage الحالية:\n\n';
                
                const allKeys = Object.keys(localStorage);
                const dutiesKeys = allKeys.filter(key => key.startsWith('duties_'));
                const receiptsKeys = allKeys.filter(key => key.startsWith('receipts_'));
                const conflictingKeys = ['patrolData', 'shiftsData', 'assignmentData', 'selectedLocations'];
                const conflictingKeysFound = allKeys.filter(key => conflictingKeys.includes(key));
                
                statusText += `🔧 مفاتيح كشف الواجبات (${dutiesKeys.length}):\n`;
                dutiesKeys.forEach(key => statusText += `  ✅ ${key}\n`);
                
                statusText += `\n📋 مفاتيح كشف الاستلامات (${receiptsKeys.length}):\n`;
                receiptsKeys.forEach(key => statusText += `  ✅ ${key}\n`);
                
                statusText += `\n⚠️ مفاتيح متضاربة موجودة (${conflictingKeysFound.length}):\n`;
                if (conflictingKeysFound.length > 0) {
                    conflictingKeysFound.forEach(key => statusText += `  ❌ ${key} (يسبب تضارب!)\n`);
                    statusText += '\n🚨 يوجد تضارب في البيانات! يجب تشغيل التنظيف.\n';
                } else {
                    statusText += '  🎉 لا توجد مفاتيح متضاربة - النظام نظيف!\n';
                }
                
                statusText += `\n📈 إجمالي المفاتيح: ${allKeys.length}`;
                
                output.innerHTML = statusText;
            }, 500);
        }
        
        // دالة التنظيف
        function performCleanup() {
            if (!confirm('هل أنت متأكد من تنظيف البيانات المتضاربة؟\n\nسيتم حذف البيانات المتضاربة وإعادة تعيين النظام.')) {
                return;
            }
            
            const output = document.getElementById('statusOutput');
            output.innerHTML = '🧹 جاري تنظيف البيانات المتضاربة...\n';
            
            setTimeout(() => {
                // قائمة المفاتيح المتضاربة
                const conflictingKeys = ['patrolData', 'shiftsData', 'assignmentData', 'selectedLocations', 'dutyFormData', 'receiptData'];
                
                let removedCount = 0;
                let statusText = '🗑️ إزالة المفاتيح المتضاربة:\n';
                
                conflictingKeys.forEach(key => {
                    if (localStorage.getItem(key)) {
                        localStorage.removeItem(key);
                        statusText += `  ✅ تم حذف: ${key}\n`;
                        removedCount++;
                    }
                });
                
                statusText += `\n🎉 تم حذف ${removedCount} مفتاح متضارب\n\n`;
                
                // إعادة تعيين البيانات الافتراضية
                statusText += '🔄 إعادة تعيين البيانات الافتراضية:\n';
                
                const defaultDutiesData = {
                    assignmentData: { headers: ['الرقم', 'موقع الواجب', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'من 10 صباحاً إلى 2 ظهراً', 'من 2 ظهراً إلى 6 مساءً', 'ملاحظات'], rows: [] },
                    patrolData: { headers: ['الرقم', 'ملاحظات', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات إضافية'], rows: [] },
                    shiftsData: { headers: ['الرقم', 'موقع المناوبة', 'المناوب الأول', 'المناوب الثاني', 'المناوب الثالث', 'ملاحظات'], rows: [] }
                };
                
                const defaultReceiptsData = {
                    patrolData: { headers: ['الرقم', 'ملاحظات', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات إضافية'], rows: [] },
                    shiftsData: { headers: ['الرقم', 'ملاحظات المناوبين', 'المناوب الأول', 'المناوب الثاني', 'المناوب الثالث', 'ملاحظات إضافية'], rows: [] }
                };
                
                localStorage.setItem('duties_assignmentData', JSON.stringify(defaultDutiesData.assignmentData));
                localStorage.setItem('duties_patrolData', JSON.stringify(defaultDutiesData.patrolData));
                localStorage.setItem('duties_shiftsData', JSON.stringify(defaultDutiesData.shiftsData));
                localStorage.setItem('duties_selectedLocations', JSON.stringify({}));
                
                localStorage.setItem('receipts_patrolData', JSON.stringify(defaultReceiptsData.patrolData));
                localStorage.setItem('receipts_shiftsData', JSON.stringify(defaultReceiptsData.shiftsData));
                
                statusText += '  ✅ duties_assignmentData\n';
                statusText += '  ✅ duties_patrolData\n';
                statusText += '  ✅ duties_shiftsData\n';
                statusText += '  ✅ duties_selectedLocations\n';
                statusText += '  ✅ receipts_patrolData\n';
                statusText += '  ✅ receipts_shiftsData\n';
                
                statusText += '\n🎉 تم الانتهاء من التنظيف بنجاح!\n';
                statusText += '🔄 يرجى تحديث صفحات كشف الواجبات وكشف الاستلامات.\n';
                
                output.innerHTML = statusText;
                
                // عرض رسالة نجاح
                setTimeout(() => {
                    alert('✅ تم تنظيف البيانات المتضاربة بنجاح!\n\nالآن كل نظام له بياناته المنفصلة:\n\n• كشف الواجبات: duties_*\n• كشف الاستلامات: receipts_*\n\nيرجى تحديث الصفحات لرؤية التأثير.');
                }, 1000);
                
            }, 1000);
        }
        
        // فحص الحالة عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(checkStatus, 500);
        });
    </script>
</body>
</html>
