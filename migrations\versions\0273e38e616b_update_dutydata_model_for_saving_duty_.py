"""Update DutyData model for saving duty schedules

Revision ID: 0273e38e616b
Revises: add_warehouse_capacity
Create Date: 2025-08-07 10:03:46.520787

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0273e38e616b'
down_revision = 'add_warehouse_capacity'
branch_labels = None
depends_on = None


def upgrade():
    # إضافة حقل timestamp إلى جدول duty_data
    op.add_column('duty_data', sa.Column('timestamp', sa.String(50), nullable=True))

    # تعديل الحقول لتصبح اختيارية
    op.alter_column('duty_data', 'location_id', nullable=True)
    op.alter_column('duty_data', 'duty_date', nullable=True)
    op.alter_column('duty_data', 'duty_time', nullable=True)


def downgrade():
    # إزالة حقل timestamp
    op.drop_column('duty_data', 'timestamp')

    # إعادة الحقول إلى كونها مطلوبة
    op.alter_column('duty_data', 'location_id', nullable=False)
    op.alter_column('duty_data', 'duty_date', nullable=False)
    op.alter_column('duty_data', 'duty_time', nullable=False)
