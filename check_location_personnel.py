#!/usr/bin/env python
# -*- coding: utf-8 -*-

from db import db
from models import LocationPersonnel, Personnel, Location
from app import app

def check_location_personnel():
    with app.app_context():
        print('🔍 فحص بيانات جدول LocationPersonnel:')
        
        # فحص جميع التعيينات
        assignments = db.session.query(LocationPersonnel, Personnel, Location).join(
            Personnel, LocationPersonnel.personnel_id == Personnel.id
        ).join(
            Location, LocationPersonnel.location_id == Location.id
        ).all()
        
        print(f'📊 إجمالي التعيينات: {len(assignments)}')
        print()
        
        # تجميع حسب الموقع
        location_groups = {}
        for assignment, person, location in assignments:
            if location.id not in location_groups:
                location_groups[location.id] = {
                    'location': location,
                    'assignments': []
                }
            location_groups[location.id]['assignments'].append((assignment, person))
        
        # عرض النتائج
        for location_id, data in location_groups.items():
            location = data['location']
            assignments = data['assignments']
            
            print(f'📍 الموقع: {location.name} (ID: {location.id})')
            print(f'   عدد الأفراد المعينين: {len(assignments)}')
            
            for assignment, person in assignments:
                status = '✅ نشط' if assignment.is_active else '❌ غير نشط'
                person_status = '👤 نشط' if person.status == 'نشط' else f'⚠️ {person.status}'
                print(f'   {status} | {person_status} | {person.rank} {person.name} (ID: {person.id})')
            print()
        
        # فحص الأفراد غير المعينين
        print('🔍 فحص الأفراد غير المعينين لأي موقع:')
        all_personnel = Personnel.query.all()
        assigned_personnel_ids = [assignment.personnel_id for assignment, _, _ in assignments]
        unassigned = [p for p in all_personnel if p.id not in assigned_personnel_ids]
        
        print(f'📊 عدد الأفراد غير المعينين: {len(unassigned)}')
        for person in unassigned:
            print(f'   ⚠️ {person.rank} {person.name} (ID: {person.id}) - حالة: {person.status}')

if __name__ == '__main__':
    check_location_personnel()
