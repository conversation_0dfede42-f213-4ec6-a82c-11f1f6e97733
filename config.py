import os

class Config:
    # إعدادات المنطقة الزمنية
    TIMEZONE = 'Asia/Riyadh'  # توقيت السعودية (UTC+3)
    
    """Base configuration."""
    # Application
    SECRET_KEY = os.environ.get('SESSION_SECRET', 'military_warehouse_secret')
    APP_DIR = os.path.abspath(os.path.dirname(__file__))
    
    # Database
    SQLALCHEMY_DATABASE_URI = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/military_warehouse")
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Localization
    BABEL_DEFAULT_LOCALE = 'ar'
    
    # Backup directory
    BACKUP_DIR = os.path.join(APP_DIR, 'backups')
    
    # Ensure backup directory exists
    if not os.path.exists(BACKUP_DIR):
        os.makedirs(BACKUP_DIR)


