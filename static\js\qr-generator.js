/**
 * QR Code Generator for Military Warehouse Management System
 * This utility provides QR code generation functionality
 */

// Wait for the DOM to be fully loaded
document.addEventListener("DOMContentLoaded", function () {
  // Initialize any QR buttons on the page
  initQRButtons();
  displayQRCodes();
});

/**
 * Initialize QR code generation buttons
 */
function initQRButtons() {
  const generateButtons = document.querySelectorAll(".generate-qr-btn");

  generateButtons.forEach(function (button) {
    button.addEventListener("click", function () {
      const dataField = button.getAttribute("data-field");
      const dataValue = button.getAttribute("data-value");
      const targetContainer = button.getAttribute("data-target");

      if (dataField && dataValue && targetContainer) {
        generateQRCode(dataField, dataValue, targetContainer);
      }
    });
  });
}

/**
 * Display QR codes that are embedded in data attributes
 */
function displayQRCodes() {
  const qrContainers = document.querySelectorAll("[data-qr]");

  qrContainers.forEach(function (container) {
    const qrData = container.getAttribute("data-qr");
    if (qrData) {
      // Use the container ID as target
      generateQRCode("data", qrData, "#" + container.id);
    }
  });
}

/**
 * Generate QR code using data and display it in a target container
 * @param {string} field - Field name for the data
 * @param {string} value - Value to encode in the QR code
 * @param {string} targetSelector - CSS selector for the target container
 */
function generateQRCode(field, value, targetSelector) {
  const container = document.querySelector(targetSelector);
  if (!container) {
    console.error("Target container not found:", targetSelector);
    return;
  }

  // Clear the container
  container.innerHTML = "";

  try {
    // Use QRCode.js library if available
    if (typeof QRCode === "function") {
      new QRCode(container, {
        text: `${field}:${value}`,
        width: 128,
        height: 128,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
    } else {
      // Fallback: fetch from API if QRCode.js is not available
      // Use Google QR Code API as a fallback
      const img = document.createElement("img");
      img.src = `https://chart.googleapis.com/chart?cht=qr&chl=${encodeURIComponent(
        field + ":" + value
      )}&chs=200x200&choe=UTF-8`;
      img.alt = "QR Code";
      container.appendChild(img);
    }
  } catch (error) {
    console.error("Failed to generate QR code:", error);
    container.innerHTML =
      '<div class="alert alert-danger">فشل إنشاء رمز QR</div>';
  }
}

/**
 * Utility function to print a QR code
 * @param {string} containerId - ID of the container to print
 */
function printQRCode(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  const printWindow = window.open("", "_blank");

  if (printWindow) {
    printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>طباعة رمز QR</title>
                <style>
                    body {
                        font-family: 'Cairo', 'Tajawal', sans-serif;
                        text-align: center;
                        padding: 20px;
                    }
                    .qr-container {
                        margin: 0 auto;
                        max-width: 300px;
                    }
                    .qr-container img {
                        max-width: 100%;
                        height: auto;
                    }
                    .qr-info {
                        margin-top: 20px;
                        font-size: 16px;
                    }
                    @media print {
                        @page {
                            size: 80mm 80mm;
                            margin: 0;
                        }
                        body {
                            margin: 10mm;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="qr-container">
                    ${container.innerHTML}
                </div>
                <div class="qr-info">
                    ${container.getAttribute("data-info") || ""}
                </div>
                <script>
                    window.onload = function() {
                        setTimeout(function() {
                            window.print();
                            setTimeout(function() {
                                window.close();
                            }, 500);
                        }, 500);
                    };
                </script>
            </body>
            </html>
        `);

    printWindow.document.close();
  } else {
    alert("فشل في فتح نافذة الطباعة. يرجى التحقق من إعدادات المتصفح الخاص بك.");
  }
}

/**
 * Save QR code as an image
 * @param {string} containerId - ID of the container with QR code
 * @param {string} filename - Filename for the downloaded image
 */
function downloadQRCode(containerId, filename = "qrcode") {
  const container = document.getElementById(containerId);
  if (!container) return;

  const qrImage = container.querySelector("img");
  if (!qrImage) return;

  // Create a temporary link element
  const downloadLink = document.createElement("a");

  // Try to use toDataURL if canvas is available
  const canvas = container.querySelector("canvas");
  if (canvas) {
    downloadLink.href = canvas.toDataURL("image/png");
  } else {
    // Fallback to the img src
    downloadLink.href = qrImage.src;
  }

  downloadLink.download = `${filename}.png`;
  document.body.appendChild(downloadLink);
  downloadLink.click();
  document.body.removeChild(downloadLink);
}

/**
 * Generate a batch of QR codes for multiple items
 * @param {Array} items - Array of items for which to generate QR codes
 * @param {string} containerSelector - CSS selector for the container to append QR codes
 */
function generateBatchQRCodes(items, containerSelector) {
  const container = document.querySelector(containerSelector);
  if (!container) return;

  // Clear the container
  container.innerHTML = "";

  items.forEach(function (item, index) {
    // Create a new container for each QR code
    const qrContainer = document.createElement("div");
    qrContainer.className = "qr-code-item";
    qrContainer.id = `qr-code-${index}`;

    // Create the QR code
    generateQRCode("id", item.id, `#qr-code-${index}`);

    // Add item information
    const infoDiv = document.createElement("div");
    infoDiv.className = "qr-info";
    infoDiv.innerHTML = `
            <p>${item.name || ""}</p>
            <p>${item.serial || ""}</p>
        `;

    qrContainer.appendChild(infoDiv);
    container.appendChild(qrContainer);
  });

  // Show print all button if there are items
  if (items.length > 0) {
    const printAllBtn = document.createElement("button");
    printAllBtn.className = "btn btn-primary mt-3";
    printAllBtn.innerHTML = '<i class="fas fa-print"></i> طباعة جميع الأكواد';
    printAllBtn.addEventListener("click", function () {
      printBatchQRCodes(items);
    });
    container.appendChild(printAllBtn);
  }
}

/**
 * Print a batch of QR codes
 * @param {Array} items - Array of items for which to print QR codes
 */
function printBatchQRCodes(items) {
  if (!items || items.length === 0) return;

  const printWindow = window.open("", "_blank");

  if (printWindow) {
    let qrCodesHtml = "";

    items.forEach(function (item) {
      qrCodesHtml += `
                <div class="qr-item">
                    <div class="qr-container" id="print-qr-${item.id}"></div>
                    <div class="qr-info">
                        <p>${item.name || ""}</p>
                        <p>${item.serial || ""}</p>
                    </div>
                </div>
            `;
    });

    printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>طباعة رموز QR</title>
                <style>
                    body {
                        font-family: 'Cairo', 'Tajawal', sans-serif;
                        padding: 10px;
                    }
                    .qr-container {
                        margin: 0 auto;
                        width: 100px;
                        height: 100px;
                    }
                    .qr-item {
                        float: right;
                        width: 50mm;
                        height: 50mm;
                        padding: 5mm;
                        margin: 1mm;
                        border: 1px solid #ccc;
                        text-align: center;
                        page-break-inside: avoid;
                    }
                    .qr-info {
                        margin-top: 5px;
                        font-size: 10px;
                    }
                    .qr-info p {
                        margin: 2px 0;
                    }
                    @media print {
                        @page {
                            size: A4;
                            margin: 10mm;
                        }
                    }
                </style>
                <script src="{{ url_for('static', filename='js/qrcode.min.js') }}"></script>
            </head>
            <body>
                <div class="qr-grid">
                    ${qrCodesHtml}
                </div>
                <script>
                    window.onload = function() {
                        // Generate QR codes
                        ${items
                          .map(
                            (item) => `
                            new QRCode(document.getElementById('print-qr-${item.id}'), {
                                text: 'id:${item.id}',
                                width: 100,
                                height: 100,
                                colorDark: "#000000",
                                colorLight: "#ffffff",
                                correctLevel: QRCode.CorrectLevel.H
                            });
                        `
                          )
                          .join("")}
                        
                        // Print after a short delay to ensure QR codes are rendered
                        setTimeout(function() {
                            window.print();
                            setTimeout(function() {
                                window.close();
                            }, 500);
                        }, 1000);
                    };
                </script>
            </body>
            </html>
        `);

    printWindow.document.close();
  } else {
    alert("فشل في فتح نافذة الطباعة. يرجى التحقق من إعدادات المتصفح الخاص بك.");
  }
}
