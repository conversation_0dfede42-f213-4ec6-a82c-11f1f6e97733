/* ===== Collapsible Panels Stylesheet ===== */
/* Author: نظام عتاد
   Description: CSS for collapsible panels in the warehouse display screens
*/

/* ===== Panel Toggle Button ===== */
.panel-toggle-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--display-text-secondary, #aaa);
  font-size: 1rem;
  cursor: pointer;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-right: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.panel-toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--display-text, #fff);
  border-color: rgba(255, 255, 255, 0.3);
}

.panel-toggle-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* ===== Panel Header Adjustments ===== */
.panel-header, .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* ===== Light Theme Adjustments ===== */
body.light-theme .panel-toggle-btn {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: var(--display-light-text-secondary, #666);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

body.light-theme .panel-toggle-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--display-light-text, #333);
  border-color: rgba(0, 0, 0, 0.2);
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 768px) {
  .panel-toggle-btn {
    padding: 0.15rem 0.35rem;
    font-size: 0.8rem;
  }
}
