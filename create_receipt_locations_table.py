#!/usr/bin/env python3
"""
إنشاء جدول receipt_locations لحفظ مواقع كشف الاستلامات
"""

import sqlite3
import os

def create_receipt_locations_table():
    """إنشاء جدول receipt_locations"""
    
    # مسار قاعدة البيانات
    db_path = 'military_warehouse.db'
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # حذف الجدول القديم إذا كان موجوداً
        cursor.execute('DROP TABLE IF EXISTS receipt_locations')

        # إنشاء الجدول الجديد
        cursor.execute('''
            CREATE TABLE receipt_locations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                row_index INTEGER NOT NULL,
                location_id TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                created_by INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء فهرس للبحث السريع
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_receipt_locations_row 
            ON receipt_locations(row_index)
        ''')
        
        # حفظ التغييرات
        conn.commit()
        print("✅ تم إنشاء جدول receipt_locations بنجاح")
        
        # التحقق من الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='receipt_locations'")
        if cursor.fetchone():
            print("✅ تم التحقق من وجود الجدول")
        else:
            print("❌ فشل في إنشاء الجدول")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدول: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    create_receipt_locations_table()
