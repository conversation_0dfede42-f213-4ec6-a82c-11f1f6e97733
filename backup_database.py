#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ احتياطي تلقائي لقاعدة البيانات
Automatic Database Backup

هذا الملف يقوم بإنشاء نسخة احتياطية من قاعدة البيانات تلقائياً
"""

import os
import subprocess
import datetime
import shutil
from pathlib import Path

def create_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    
    print("🔄 بدء عملية النسخ الاحتياطي...")
    print("=" * 50)
    
    try:
        # إعدادات قاعدة البيانات
        DB_HOST = "localhost"
        DB_PORT = "5432"
        DB_NAME = "military_warehouse"
        DB_USER = "postgres"
        
        # إنشاء مجلد النسخ الاحتياطية
        backup_dir = Path("backups")
        backup_dir.mkdir(exist_ok=True)
        
        # تاريخ ووقت النسخة الاحتياطية
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"military_warehouse_backup_{timestamp}.sql"
        backup_path = backup_dir / backup_filename
        
        print(f"📁 مجلد النسخ الاحتياطية: {backup_dir.absolute()}")
        print(f"📄 اسم الملف: {backup_filename}")
        
        # أمر النسخ الاحتياطي
        pg_dump_cmd = [
            "pg_dump",
            "-h", DB_HOST,
            "-p", DB_PORT,
            "-U", DB_USER,
            "-d", DB_NAME,
            "-f", str(backup_path),
            "--verbose",
            "--no-password"
        ]
        
        print(f"\n🔄 تنفيذ أمر النسخ الاحتياطي...")
        print(f"💾 حفظ في: {backup_path}")
        
        # تنفيذ الأمر
        result = subprocess.run(
            pg_dump_cmd,
            capture_output=True,
            text=True,
            env={**os.environ, "PGPASSWORD": "postgres"}  # كلمة مرور افتراضية
        )
        
        if result.returncode == 0:
            # التحقق من حجم الملف
            file_size = backup_path.stat().st_size
            file_size_mb = file_size / (1024 * 1024)
            
            print(f"✅ تم إنشاء النسخة الاحتياطية بنجاح!")
            print(f"📊 حجم الملف: {file_size_mb:.2f} MB")
            print(f"📍 المسار: {backup_path.absolute()}")
            
            # ضغط الملف (اختياري)
            compressed_path = backup_path.with_suffix('.sql.gz')
            print(f"\n🗜️  ضغط الملف...")
            
            import gzip
            with open(backup_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # حذف الملف غير المضغوط
            backup_path.unlink()
            
            compressed_size = compressed_path.stat().st_size
            compressed_size_mb = compressed_size / (1024 * 1024)
            compression_ratio = (1 - compressed_size / file_size) * 100
            
            print(f"✅ تم ضغط الملف بنجاح!")
            print(f"📊 الحجم بعد الضغط: {compressed_size_mb:.2f} MB")
            print(f"📈 نسبة الضغط: {compression_ratio:.1f}%")
            print(f"📍 الملف النهائي: {compressed_path.absolute()}")
            
            # تنظيف النسخ القديمة (الاحتفاظ بآخر 10 نسخ)
            cleanup_old_backups(backup_dir)
            
            return True, str(compressed_path)
            
        else:
            print(f"❌ فشل في إنشاء النسخة الاحتياطية!")
            print(f"🔍 رسالة الخطأ: {result.stderr}")
            return False, result.stderr
            
    except Exception as e:
        print(f"❌ خطأ في عملية النسخ الاحتياطي: {str(e)}")
        return False, str(e)

def cleanup_old_backups(backup_dir, keep_count=10):
    """تنظيف النسخ الاحتياطية القديمة"""
    
    print(f"\n🧹 تنظيف النسخ الاحتياطية القديمة...")
    
    try:
        # البحث عن جميع ملفات النسخ الاحتياطية
        backup_files = list(backup_dir.glob("military_warehouse_backup_*.sql.gz"))
        
        if len(backup_files) <= keep_count:
            print(f"ℹ️  عدد النسخ الحالية: {len(backup_files)} (لا حاجة للتنظيف)")
            return
        
        # ترتيب الملفات حسب تاريخ الإنشاء
        backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # حذف الملفات الزائدة
        files_to_delete = backup_files[keep_count:]
        
        for file_path in files_to_delete:
            file_path.unlink()
            print(f"🗑️  تم حذف: {file_path.name}")
        
        print(f"✅ تم تنظيف {len(files_to_delete)} ملف قديم")
        print(f"📊 النسخ المتبقية: {len(backup_files) - len(files_to_delete)}")
        
    except Exception as e:
        print(f"⚠️  خطأ في تنظيف النسخ القديمة: {str(e)}")

def restore_backup(backup_file):
    """استعادة نسخة احتياطية"""
    
    print(f"🔄 استعادة النسخة الاحتياطية من: {backup_file}")
    
    try:
        # إعدادات قاعدة البيانات
        DB_HOST = "localhost"
        DB_PORT = "5432"
        DB_NAME = "military_warehouse"
        DB_USER = "postgres"
        
        # فك ضغط الملف إذا كان مضغوطاً
        if backup_file.endswith('.gz'):
            print("🗜️  فك ضغط الملف...")
            import gzip
            
            uncompressed_file = backup_file.replace('.gz', '')
            with gzip.open(backup_file, 'rb') as f_in:
                with open(uncompressed_file, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            backup_file = uncompressed_file
        
        # أمر الاستعادة
        psql_cmd = [
            "psql",
            "-h", DB_HOST,
            "-p", DB_PORT,
            "-U", DB_USER,
            "-d", DB_NAME,
            "-f", backup_file,
            "--quiet"
        ]
        
        print("🔄 تنفيذ أمر الاستعادة...")
        
        result = subprocess.run(
            psql_cmd,
            capture_output=True,
            text=True,
            env={**os.environ, "PGPASSWORD": "postgres"}
        )
        
        if result.returncode == 0:
            print("✅ تم استعادة النسخة الاحتياطية بنجاح!")
            
            # حذف الملف المفكوك الضغط
            if backup_file.endswith('.sql') and os.path.exists(backup_file):
                os.unlink(backup_file)
            
            return True
        else:
            print(f"❌ فشل في استعادة النسخة الاحتياطية!")
            print(f"🔍 رسالة الخطأ: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في استعادة النسخة الاحتياطية: {str(e)}")
        return False

def schedule_daily_backup():
    """جدولة نسخ احتياطية يومية"""
    
    print("📅 إعداد النسخ الاحتياطية اليومية...")
    
    # يمكن استخدام مكتبة schedule أو cron jobs
    # هذا مثال بسيط للتشغيل اليدوي
    
    import schedule
    import time
    
    # جدولة نسخة احتياطية يومياً في الساعة 2:00 صباحاً
    schedule.every().day.at("02:00").do(create_backup)
    
    print("⏰ تم جدولة النسخ الاحتياطية اليومية في الساعة 2:00 صباحاً")
    print("🔄 للتشغيل المستمر، اترك هذا البرنامج يعمل...")
    
    while True:
        schedule.run_pending()
        time.sleep(60)  # فحص كل دقيقة

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "restore" and len(sys.argv) > 2:
            # استعادة نسخة احتياطية
            restore_backup(sys.argv[2])
        elif sys.argv[1] == "schedule":
            # تشغيل الجدولة
            schedule_daily_backup()
        else:
            print("الاستخدام:")
            print("  python backup_database.py                 # إنشاء نسخة احتياطية")
            print("  python backup_database.py restore file.sql # استعادة نسخة احتياطية")
            print("  python backup_database.py schedule        # جدولة نسخ يومية")
    else:
        # إنشاء نسخة احتياطية
        success, result = create_backup()
        if success:
            print(f"\n🎉 تمت العملية بنجاح!")
        else:
            print(f"\n❌ فشلت العملية: {result}")
            sys.exit(1)
