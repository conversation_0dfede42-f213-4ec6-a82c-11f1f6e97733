#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت للتحقق من البيانات الموجودة في قاعدة البيانات
يعرض تقرير مفصل عن البيانات المحفوظة
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import json
from datetime import datetime

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'database': 'military_warehouse',
    'user': 'postgres',
    'password': 'postgres'
}

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def check_table_data(cursor, table_name, description=""):
    """فحص بيانات جدول معين"""
    try:
        # فحص وجود الجدول
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = %s
            );
        """, (table_name,))
        
        if not cursor.fetchone()[0]:
            print(f"❌ الجدول {table_name} غير موجود")
            return
        
        # عدد الصفوف
        cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
        row_count = cursor.fetchone()[0]
        
        print(f"📊 {table_name} ({description}): {row_count} صف")
        
        # إذا كان الجدول يحتوي على بيانات، اعرض عينة
        if row_count > 0:
            # محاولة الحصول على آخر 3 سجلات
            try:
                cursor.execute(f"""
                    SELECT * FROM {table_name} 
                    ORDER BY id DESC 
                    LIMIT 3;
                """)
                
                rows = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]
                
                print(f"   📋 آخر {len(rows)} سجل:")
                for i, row in enumerate(rows, 1):
                    print(f"      {i}. ID: {row[0]}")
                    
                    # عرض بعض التفاصيل حسب نوع الجدول
                    if 'data_json' in columns:
                        try:
                            data = json.loads(row[columns.index('data_json')])
                            if isinstance(data, dict):
                                if 'headers' in data:
                                    print(f"         العناوين: {len(data.get('headers', []))}")
                                if 'rows' in data:
                                    print(f"         الصفوف: {len(data.get('rows', []))}")
                        except:
                            print(f"         بيانات JSON")
                    
                    if 'created_at' in columns:
                        created_at = row[columns.index('created_at')]
                        if created_at:
                            print(f"         تاريخ الإنشاء: {created_at}")
                
            except Exception as e:
                print(f"   ⚠️ لا يمكن عرض تفاصيل البيانات: {e}")
        
        print()  # سطر فارغ
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجدول {table_name}: {e}")

def check_all_data():
    """فحص جميع البيانات في قاعدة البيانات"""
    
    print("🔍 تقرير شامل عن البيانات الموجودة في قاعدة البيانات")
    print("=" * 60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        print(f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 1. البيانات الأساسية
        print("📋 البيانات الأساسية:")
        print("-" * 30)
        check_table_data(cursor, 'users', 'المستخدمين')
        check_table_data(cursor, 'warehouses', 'المستودعات')
        check_table_data(cursor, 'weapons', 'الأسلحة')
        check_table_data(cursor, 'personnel', 'الأفراد')
        check_table_data(cursor, 'locations', 'المواقع')
        
        # 2. بيانات كشف الاستلامات
        print("📄 بيانات كشف الاستلامات:")
        print("-" * 30)
        check_table_data(cursor, 'receipt_data', 'بيانات الكشوف العامة')
        check_table_data(cursor, 'receipts_patrol_data', 'بيانات جدول الدوريات')
        check_table_data(cursor, 'receipts_shifts_data', 'بيانات جدول المناوبين')
        check_table_data(cursor, 'receipts_patrol_locations', 'مواقع الدوريات')
        check_table_data(cursor, 'receipts_shifts_locations', 'مواقع المناوبين')
        
        # 3. بيانات كشف الواجبات
        print("🛡️ بيانات كشف الواجبات:")
        print("-" * 30)
        check_table_data(cursor, 'duty_data', 'بيانات الواجبات')
        check_table_data(cursor, 'duty_patrol_data', 'بيانات دوريات الواجبات')
        check_table_data(cursor, 'duty_shifts_data', 'بيانات مناوبين الواجبات')
        check_table_data(cursor, 'duty_drafts', 'مسودات الواجبات')
        
        # 4. بيانات أخرى
        print("📊 بيانات أخرى:")
        print("-" * 30)
        check_table_data(cursor, 'activity_logs', 'سجلات النشاط')
        check_table_data(cursor, 'backup_records', 'سجلات النسخ الاحتياطية')
        check_table_data(cursor, 'inventory_items', 'عناصر المخزون')
        
        # 5. ملخص عام
        print("📈 ملخص عام:")
        print("-" * 30)
        
        # حساب إجمالي البيانات
        total_tables = 0
        total_rows = 0
        tables_with_data = 0
        
        important_tables = [
            'users', 'warehouses', 'weapons', 'personnel', 'locations',
            'receipt_data', 'receipts_patrol_data', 'receipts_shifts_data',
            'duty_data', 'duty_patrol_data', 'duty_shifts_data'
        ]
        
        for table_name in important_tables:
            try:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = %s
                    );
                """, (table_name,))
                
                if cursor.fetchone()[0]:
                    total_tables += 1
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                    row_count = cursor.fetchone()[0]
                    total_rows += row_count
                    if row_count > 0:
                        tables_with_data += 1
            except:
                pass
        
        print(f"📊 إجمالي الجداول المهمة: {total_tables}")
        print(f"📈 الجداول التي تحتوي على بيانات: {tables_with_data}")
        print(f"🔢 إجمالي الصفوف: {total_rows:,}")
        
        if total_rows > 0:
            print(f"\n✅ قاعدة البيانات تحتوي على بيانات مهمة!")
            print(f"🛡️ التحديث سيكون آمن ولن يؤثر على البيانات الموجودة")
        else:
            print(f"\nℹ️ قاعدة البيانات فارغة أو تحتوي على بيانات قليلة")
            print(f"✅ التحديث آمن تماماً")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ أثناء فحص البيانات: {e}")
        return False
    
    finally:
        cursor.close()
        conn.close()

def main():
    """الدالة الرئيسية"""
    print("🔍 أداة فحص البيانات الموجودة")
    print("📋 هذه الأداة تعرض تقرير مفصل عن البيانات المحفوظة")
    print("🔒 لا تقوم بأي تعديل على البيانات")
    print("=" * 60)
    
    success = check_all_data()
    
    if success:
        print("\n✅ تم إنشاء التقرير بنجاح!")
        print("💡 يمكنك الآن تشغيل سكريبت التحديث الآمن إذا كنت تريد")
    else:
        print("\n❌ فشل في إنشاء التقرير")
        print("🔧 تحقق من إعدادات قاعدة البيانات")

if __name__ == "__main__":
    main()
