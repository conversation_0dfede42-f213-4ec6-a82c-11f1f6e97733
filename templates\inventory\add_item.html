{% extends "base.html" %}

{% block title %}إضافة صنف جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-plus text-primary"></i>
                    إضافة صنف جديد للمخزون
                </h2>
                <a href="{{ url_for('inventory_management.items') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        بيانات الصنف
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('inventory_management.add_item') }}">
                        {{ form.hidden_tag() }}
                        
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">المعلومات الأساسية</h6>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.item_code.label(class="form-label required") }}
                                    {{ form.item_code(class="form-control") }}
                                    {% if form.item_code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.item_code.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-8 mb-3">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label required") }}
                                    {{ form.name(class="form-control") }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.category.label(class="form-label required") }}
                                    {{ form.category(class="form-control", id="category") }}
                                    {% if form.category.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.category.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.subcategory.label(class="form-label") }}
                                    {{ form.subcategory(class="form-control", id="subcategory") }}
                                    {% if form.subcategory.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.subcategory.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.warehouse_id.label(class="form-label required") }}
                                    {{ form.warehouse_id(class="form-control") }}
                                    {% if form.warehouse_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.warehouse_id.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.brand.label(class="form-label") }}
                                    {{ form.brand(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.model.label(class="form-label") }}
                                    {{ form.model(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.size.label(class="form-label") }}
                                    {{ form.size(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.color.label(class="form-label") }}
                                    {{ form.color(class="form-control") }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.material.label(class="form-label") }}
                                    {{ form.material(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.status.label(class="form-label required") }}
                                    {{ form.status(class="form-control") }}
                                </div>
                            </div>
                        </div>

                        <!-- Quantity Information -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">معلومات الكمية</h6>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.quantity_in_stock.label(class="form-label required") }}
                                    {{ form.quantity_in_stock(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.minimum_stock.label(class="form-label") }}
                                    {{ form.minimum_stock(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.maximum_stock.label(class="form-label") }}
                                    {{ form.maximum_stock(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.unit_cost.label(class="form-label") }}
                                    {{ form.unit_cost(class="form-control") }}
                                </div>
                            </div>
                        </div>

                        <!-- Dates -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">التواريخ</h6>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.manufacture_date.label(class="form-label") }}
                                    {{ form.manufacture_date(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.expiry_date.label(class="form-label") }}
                                    {{ form.expiry_date(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.purchase_date.label(class="form-label") }}
                                    {{ form.purchase_date(class="form-control") }}
                                </div>
                            </div>
                        </div>

                        <!-- Location -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">الموقع</h6>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.location.label(class="form-label") }}
                                    {{ form.location(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.shelf_number.label(class="form-label") }}
                                    {{ form.shelf_number(class="form-control") }}
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">معلومات إضافية</h6>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.supplier.label(class="form-label") }}
                                    {{ form.supplier(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.batch_number.label(class="form-label") }}
                                    {{ form.batch_number(class="form-control") }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.serial_numbers.label(class="form-label") }}
                                    {{ form.serial_numbers(class="form-control", rows="3") }}
                                    <small class="form-text text-muted">للمعدات: اكتب كل رقم تسلسلي في سطر منفصل</small>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.specifications.label(class="form-label") }}
                                    {{ form.specifications(class="form-control", rows="3") }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-group">
                                    {{ form.notes.label(class="form-label") }}
                                    {{ form.notes(class="form-control", rows="3") }}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{{ url_for('inventory_management.items') }}" class="btn btn-secondary me-2">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    {{ form.submit(class="btn btn-primary") }}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تحديث الفئات الفرعية عند تغيير الفئة الرئيسية
    function updateSubcategories() {
        var category = $('#category').val();
        var subcategorySelect = $('#subcategory');

        // مسح الخيارات الحالية
        subcategorySelect.empty();
        subcategorySelect.append('<option value="">اختر الفئة الفرعية</option>');

        if (category) {
            // جلب الفئات الفرعية
            $.get('/inventory_management/api/subcategories/' + category, function(data) {
                $.each(data, function(index, subcategory) {
                    subcategorySelect.append('<option value="' + subcategory + '">' + subcategory + '</option>');
                });
            });
        }
    }

    $('#category').change(updateSubcategories);

    // تحديث الفئات الفرعية عند تحميل الصفحة إذا كانت الفئة محددة مسبقاً
    if ($('#category').val()) {
        updateSubcategories();
    }

    // تفعيل date picker للتواريخ
    $('input[type="datetime-local"]').each(function() {
        $(this).attr('type', 'date');
    });
});

// بيانات الفئات والفئات الفرعية
var categories = {{ categories|tojson }};
</script>
{% endblock %}
