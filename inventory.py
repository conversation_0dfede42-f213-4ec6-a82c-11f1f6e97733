# ملف المخزون المدمج - يجمع بين نظام المخزون الجديد ووظائف الأجهزة القديمة
from flask import Blueprint, redirect, url_for
from flask_login import login_required

# إنشاء Blueprint للمخزون
inventory_bp = Blueprint('inventory', __name__, url_prefix='/inventory')

@inventory_bp.route('/')
@login_required
def index():
    """توجيه إلى صفحة المخزون الجديدة"""
    return redirect(url_for('inventory_management.index'))

# استيراد وتسجيل وظائف الأجهزة من الملف القديم
try:
    from inventory_devices import (
        devices, device_details, create_device, edit_device, delete_device,
        add_device_maintenance, edit_device_maintenance, delete_device_maintenance,
        warehouse_devices, audits, create_audit, audit_details, edit_audit,
        delete_audit, complete_audit, cancel_audit, audit_weapon
    )
    
    # تسجيل وظائف الأجهزة
    inventory_bp.add_url_rule('/devices', 'devices', devices, methods=['GET'])
    inventory_bp.add_url_rule('/devices/<int:device_id>', 'device_details', device_details, methods=['GET'])
    inventory_bp.add_url_rule('/devices/create', 'create_device', create_device, methods=['GET', 'POST'])
    inventory_bp.add_url_rule('/devices/<int:device_id>/edit', 'edit_device', edit_device, methods=['GET', 'POST'])
    inventory_bp.add_url_rule('/devices/<int:device_id>/delete', 'delete_device', delete_device, methods=['POST'])
    inventory_bp.add_url_rule('/devices/<int:device_id>/maintenance', 'add_device_maintenance', add_device_maintenance, methods=['GET', 'POST'])
    inventory_bp.add_url_rule('/devices/<int:device_id>/maintenance/<int:maintenance_id>/edit', 'edit_device_maintenance', edit_device_maintenance, methods=['GET', 'POST'])
    inventory_bp.add_url_rule('/devices/<int:device_id>/maintenance/<int:maintenance_id>/delete', 'delete_device_maintenance', delete_device_maintenance, methods=['GET', 'POST'])
    inventory_bp.add_url_rule('/devices/warehouse/<int:warehouse_id>', 'warehouse_devices', warehouse_devices, methods=['GET'])
    inventory_bp.add_url_rule('/warehouses/<int:warehouse_id>/devices', 'warehouse_devices', warehouse_devices, methods=['GET'])
    
    # وظائف الجرد
    inventory_bp.add_url_rule('/audits', 'audits', audits, methods=['GET'])
    inventory_bp.add_url_rule('/audits/create', 'create_audit', create_audit, methods=['GET', 'POST'])
    inventory_bp.add_url_rule('/audits/<int:audit_id>', 'audit_details', audit_details, methods=['GET'])
    inventory_bp.add_url_rule('/audits/<int:audit_id>/edit', 'edit_audit', edit_audit, methods=['GET', 'POST'])
    inventory_bp.add_url_rule('/audits/<int:audit_id>/delete', 'delete_audit', delete_audit, methods=['GET'])
    inventory_bp.add_url_rule('/audits/<int:audit_id>/complete', 'complete_audit', complete_audit, methods=['POST'])
    inventory_bp.add_url_rule('/audits/<int:audit_id>/cancel', 'cancel_audit', cancel_audit, methods=['POST'])
    inventory_bp.add_url_rule('/audits/<int:audit_id>/weapons/<int:weapon_id>', 'audit_weapon', audit_weapon, methods=['GET', 'POST'])
    
except ImportError as e:
    print(f"تعذر استيراد وظائف الأجهزة: {e}")
    
    # وظائف بديلة في حالة فشل الاستيراد
    @inventory_bp.route('/devices')
    @login_required
    def devices():
        return redirect(url_for('inventory_management.index'))
    
    @inventory_bp.route('/audits')
    @login_required
    def audits():
        return redirect(url_for('inventory_management.index'))
