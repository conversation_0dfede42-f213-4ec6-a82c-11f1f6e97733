{% extends 'base.html' %}

{% block title %}تفاصيل السلاح - {{ weapon.name }}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h3 class="mb-0">تفاصيل السلاح</h3>
    <div class="btn-toolbar">
        <div class="btn-group mr-2">
            <a href="{{ url_for('weapons.edit', weapon_id=weapon.id) }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-edit"></i> تعديل
            </a>

            {% if current_user.is_admin %}
            <a href="#" class="btn btn-sm btn-outline-danger" onclick="alert('لم يتم تنفيذ هذه الوظيفة بعد'); return false;">
                <i class="fas fa-trash"></i> حذف
            </a>
            {% endif %}
        </div>

        <div class="btn-group mr-2">
            <button type="button" class="btn btn-sm btn-outline-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-qrcode"></i> الباركود / QR
            </button>
            <div class="dropdown-menu dropdown-menu-right">
                <a class="dropdown-item" href="{{ url_for('weapons.get_qr_code', weapon_id=weapon.id) }}">QR Code</a>
                <a class="dropdown-item" href="{{ url_for('weapons.barcode', weapon_id=weapon.id) }}">Barcode</a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{{ url_for('weapons.get_qr_code', weapon_id=weapon.id, download=1) }}">تنزيل QR Code</a>
                <a class="dropdown-item" href="{{ url_for('weapons.barcode', weapon_id=weapon.id, download=1) }}">تنزيل الباركود</a>
            </div>
        </div>
        
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-tasks"></i> إدارة
            </button>
            <div class="dropdown-menu dropdown-menu-right">
                <a class="dropdown-item" href="#" onclick="$('#checkoutModal').modal('show'); return false;">تسليم السلاح</a>
                <a class="dropdown-item" href="#" onclick="$('#transferModal').modal('show'); return false;">نقل السلاح</a>
                <a class="dropdown-item" href="#" onclick="$('#maintenanceModal').modal('show'); return false;">إضافة سجل صيانة</a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='نشط') }}">تعيين كنشط</a>
                <a class="dropdown-item" href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='إجازة') }}">تعيين في إجازة</a>
                <a class="dropdown-item" href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='مهمة') }}">تعيين في مهمة</a>
                <a class="dropdown-item" href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='صيانة') }}">تعيين في صيانة</a>
                <a class="dropdown-item" href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='تالف') }}">تعيين كتالف</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card shadow-sm border-0">
            <div class="card-body p-0">
                <div class="row no-gutters">
                    <div class="col-md-8">
                        <div class="p-4">
                            <div class="d-flex justify-content-between mb-3">
                                <h4 class="card-title mb-0">{{ weapon.name }}</h4>
                                <span class="badge badge-{{ weapon.status|status_color }} badge-pill px-3 py-2">{{ weapon.status }}</span>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <p class="text-muted mb-1">اسم السلاح</p>
                                        <h6>{{ weapon.name }}</h6>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <p class="text-muted mb-1">رقم السلاح</p>
                                        <h6>{{ weapon.weapon_number or 'غير محدد' }}</h6>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <p class="text-muted mb-1">الرقم التسلسلي</p>
                                        <h6>{{ weapon.serial_number }}</h6>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <p class="text-muted mb-1">النوع</p>
                                        <h6>{{ weapon.type }}</h6>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <p class="text-muted mb-1">المستودع</p>
                                        <h6>{{ weapon.warehouse.name }}</h6>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <p class="text-muted mb-1">الحالة الفنية</p>
                                        <h6>{{ weapon.condition or 'سليم' }}</h6>
                                    </div>
                                </div>
                            </div>
                            {% if weapon.notes %}
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <p class="text-muted mb-1">ملاحظات</p>
                                        <p>{{ weapon.notes }}</p>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4 bg-light d-flex align-items-center justify-content-center">
                        <div class="text-center p-4">
                            <div id="qrcode" class="mb-3 mx-auto"></div>
                            <p class="font-weight-bold">{{ weapon.serial_number }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
        
    <!-- Transactions History Card -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-exchange-alt"></i> سجل المعاملات
            </div>
            <div class="card-body">
                {% if transactions %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الشخص</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>بواسطة</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span class="badge {% if transaction.transaction_type == 'checkout' %}badge-warning{% elif transaction.transaction_type == 'return' %}badge-success{% else %}badge-info{% endif %}">
                                        {% if transaction.transaction_type == 'checkout' %}
                                            صرف
                                        {% elif transaction.transaction_type == 'return' %}
                                            إعادة
                                        {% elif transaction.transaction_type == 'transfer' %}
                                            نقل
                                        {% endif %}
                                    </span>
                                </td>
                                <td>{{ transaction.personnel.name }}</td>
                                <td>{{ transaction.source_warehouse.name }}</td>
                                <td>{{ transaction.target_warehouse.name if transaction.target_warehouse else 'غير محدد' }}</td>
                                <td>{{ transaction.user.username }}</td>
                                <td>
                                    {% if transaction.transaction_type == 'checkout' and current_user.is_admin %}
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                data-toggle="modal" 
                                                data-target="#returnModal{{ transaction.id }}">
                                            استلام
                                        </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد معاملات لهذا السلاح.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Maintenance History Card -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tools"></i> سجل الصيانة
            </div>
            <div class="card-body">
                {% if maintenance_records %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الحالة</th>
                                <th>الوصف</th>
                                <th>بواسطة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in maintenance_records %}
                            <tr>
                                <td>{{ record.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ record.maintenance_type }}</td>
                                <td>
                                    <span class="badge {% if record.status == 'ongoing' %}badge-warning{% elif record.status == 'completed' %}badge-success{% else %}badge-danger{% endif %}">
                                        {% if record.status == 'ongoing' %}
                                            جارية
                                        {% elif record.status == 'completed' %}
                                            مكتملة
                                        {% elif record.status == 'cancelled' %}
                                            ملغاة
                                        {% endif %}
                                    </span>
                                </td>
                                <td>{{ record.description }}</td>
                                <td>{{ record.user.username }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد سجلات صيانة لهذا السلاح.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Checkout Modal -->
<div class="modal fade" id="checkoutModal" tabindex="-1" aria-labelledby="checkoutModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('weapons.checkout', weapon_id=weapon.id) }}">
                {{ checkout_form.hidden_tag() }}
                <div class="modal-header">
                    <h5 class="modal-title" id="checkoutModalLabel">تسليم السلاح</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="personnel_id" class="form-label">{{ checkout_form.personnel_id.label }}</label>
                        {{ checkout_form.personnel_id(class="form-control") }}
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">{{ checkout_form.notes.label }}</label>
                        {{ checkout_form.notes(class="form-control") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    {{ checkout_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div class="modal fade" id="transferModal" tabindex="-1" aria-labelledby="transferModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('weapons.transfer', weapon_id=weapon.id) }}">
                {{ transfer_form.hidden_tag() }}
                <div class="modal-header">
                    <h5 class="modal-title" id="transferModalLabel">نقل السلاح</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="target_warehouse_id" class="form-label">{{ transfer_form.target_warehouse_id.label }}</label>
                        {{ transfer_form.target_warehouse_id(class="form-control") }}
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">{{ transfer_form.notes.label }}</label>
                        {{ transfer_form.notes(class="form-control") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    {{ transfer_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Maintenance Modal -->
<div class="modal fade" id="maintenanceModal" tabindex="-1" aria-labelledby="maintenanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('weapons.add_maintenance', weapon_id=weapon.id) }}">
                {{ maintenance_form.hidden_tag() }}
                <div class="modal-header">
                    <h5 class="modal-title" id="maintenanceModalLabel">إضافة سجل صيانة</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="maintenance_type" class="form-label">{{ maintenance_form.maintenance_type.label }}</label>
                        {{ maintenance_form.maintenance_type(class="form-control") }}
                    </div>
                    <div class="form-group">
                        <label for="description" class="form-label">{{ maintenance_form.description.label }}</label>
                        {{ maintenance_form.description(class="form-control", rows="3") }}
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="start_date" class="form-label">{{ maintenance_form.start_date.label }}</label>
                                {{ maintenance_form.start_date(class="form-control datepicker") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="end_date" class="form-label">{{ maintenance_form.end_date.label }}</label>
                                {{ maintenance_form.end_date(class="form-control datepicker") }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status" class="form-label">{{ maintenance_form.status.label }}</label>
                                {{ maintenance_form.status(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="cost" class="form-label">{{ maintenance_form.cost.label }}</label>
                                {{ maintenance_form.cost(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">{{ maintenance_form.notes.label }}</label>
                        {{ maintenance_form.notes(class="form-control", rows="3") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    {{ maintenance_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Return Modals for each checkout transaction -->
{% for transaction in transactions %}
    {% if transaction.transaction_type == 'checkout' %}
    <div class="modal fade" id="returnModal{{ transaction.id }}" tabindex="-1" aria-labelledby="returnModalLabel{{ transaction.id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="{{ url_for('weapons.return_weapon', weapon_id=weapon.id, transaction_id=transaction.id) }}">
                    {{ return_form.hidden_tag() }}
                    <input type="hidden" name="transaction_id" value="{{ transaction.id }}">
                    <div class="modal-header">
                        <h5 class="modal-title" id="returnModalLabel{{ transaction.id }}">استلام السلاح من {{ transaction.personnel.name }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="condition" class="form-label">{{ return_form.condition.label }}</label>
                            {{ return_form.condition(class="form-control") }}
                        </div>
                        <div class="form-group">
                            <label for="notes" class="form-label">{{ return_form.notes.label }}</label>
                            {{ return_form.notes(class="form-control", rows="3") }}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                        {{ return_form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endif %}
{% endfor %}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Generate QR code
        var qrcode = new QRCode(document.getElementById("qrcode"), {
            text: "{{ weapon.serial_number }}",
            width: 128,
            height: 128,
            colorDark : "#000000",
            colorLight : "#ffffff",
            correctLevel : QRCode.CorrectLevel.H
        });
    });
</script>
{% endblock %}
