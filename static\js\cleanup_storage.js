// دالة تنظيف شاملة للبيانات المتضاربة في localStorage
// يجب تشغيل هذا الملف مرة واحدة لحل مشكلة تضارب البيانات

console.log('🧹 بدء تنظيف شامل للبيانات المتضاربة...');

// قائمة بجميع المفاتيح المتضاربة
const conflictingKeys = [
    // المفاتيح القديمة التي تسبب التضارب
    'patrolData',
    'shiftsData', 
    'assignmentData',
    'selectedLocations',
    
    // مفاتيح أخرى قد تسبب مشاكل
    'dutyFormData',
    'receiptData',
    'lastSaved',
    'autoSaveEnabled'
];

// دالة التنظيف الرئيسية
function performCompleteCleanup() {
    console.log('🗑️ إزالة جميع المفاتيح المتضاربة...');
    
    let removedCount = 0;
    
    conflictingKeys.forEach(key => {
        if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
            console.log(`✅ تم حذف: ${key}`);
            removedCount++;
        }
    });
    
    console.log(`🎉 تم حذف ${removedCount} مفتاح متضارب`);
    
    // إعادة تعيين البيانات الجديدة بالمفاتيح الصحيحة
    resetWithCorrectKeys();
}

// دالة إعادة تعيين البيانات بالمفاتيح الصحيحة
function resetWithCorrectKeys() {
    console.log('🔄 إعادة تعيين البيانات بالمفاتيح الصحيحة...');
    
    // بيانات افتراضية لكشف الواجبات
    const defaultDutiesData = {
        assignmentData: {
            headers: ['الرقم', 'موقع الواجب', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'من 10 صباحاً إلى 2 ظهراً', 'من 2 ظهراً إلى 6 مساءً', 'ملاحظات'],
            rows: []
        },
        patrolData: {
            headers: ['الرقم', 'ملاحظات', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات إضافية'],
            rows: []
        },
        shiftsData: {
            headers: ['الرقم', 'موقع المناوبة', 'المناوب الأول', 'المناوب الثاني', 'المناوب الثالث', 'ملاحظات'],
            rows: []
        }
    };
    
    // بيانات افتراضية لكشف الاستلامات
    const defaultReceiptsData = {
        patrolData: {
            headers: ['الرقم', 'ملاحظات', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات إضافية'],
            rows: []
        },
        shiftsData: {
            headers: ['الرقم', 'ملاحظات المناوبين', 'المناوب الأول', 'المناوب الثاني', 'المناوب الثالث', 'ملاحظات إضافية'],
            rows: []
        }
    };
    
    // حفظ البيانات بالمفاتيح الصحيحة
    localStorage.setItem('duties_assignmentData', JSON.stringify(defaultDutiesData.assignmentData));
    localStorage.setItem('duties_patrolData', JSON.stringify(defaultDutiesData.patrolData));
    localStorage.setItem('duties_shiftsData', JSON.stringify(defaultDutiesData.shiftsData));
    localStorage.setItem('duties_selectedLocations', JSON.stringify({}));
    
    localStorage.setItem('receipts_patrolData', JSON.stringify(defaultReceiptsData.patrolData));
    localStorage.setItem('receipts_shiftsData', JSON.stringify(defaultReceiptsData.shiftsData));
    
    console.log('✅ تم إعادة تعيين البيانات بالمفاتيح الصحيحة');
}

// دالة عرض حالة localStorage
function showLocalStorageStatus() {
    console.log('📊 حالة localStorage الحالية:');
    
    const allKeys = Object.keys(localStorage);
    const dutiesKeys = allKeys.filter(key => key.startsWith('duties_'));
    const receiptsKeys = allKeys.filter(key => key.startsWith('receipts_'));
    const conflictingKeysFound = allKeys.filter(key => conflictingKeys.includes(key));
    
    console.log(`🔧 مفاتيح كشف الواجبات: ${dutiesKeys.length}`);
    dutiesKeys.forEach(key => console.log(`  - ${key}`));
    
    console.log(`📋 مفاتيح كشف الاستلامات: ${receiptsKeys.length}`);
    receiptsKeys.forEach(key => console.log(`  - ${key}`));
    
    console.log(`⚠️ مفاتيح متضاربة موجودة: ${conflictingKeysFound.length}`);
    conflictingKeysFound.forEach(key => console.log(`  - ${key} ⚠️`));
    
    if (conflictingKeysFound.length === 0) {
        console.log('🎉 لا توجد مفاتيح متضاربة - النظام نظيف!');
    }
}

// تشغيل التنظيف
function runCleanup() {
    console.log('🚀 بدء عملية التنظيف الشاملة...');
    
    showLocalStorageStatus();
    performCompleteCleanup();
    showLocalStorageStatus();
    
    console.log('✅ تم الانتهاء من التنظيف الشامل');
    console.log('🔄 يرجى تحديث الصفحات لرؤية التأثير');
    
    // عرض رسالة للمستخدم
    if (typeof alert !== 'undefined') {
        alert('✅ تم تنظيف البيانات المتضاربة بنجاح!\n\nيرجى تحديث صفحة كشف الواجبات وصفحة كشف الاستلامات لرؤية التأثير.');
    }
}

// تشغيل التنظيف عند تحميل الملف
runCleanup();

// تصدير الدوال للاستخدام الخارجي
if (typeof window !== 'undefined') {
    window.cleanupStorage = {
        runCleanup,
        showLocalStorageStatus,
        performCompleteCleanup,
        resetWithCorrectKeys
    };
}
