{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>إدارة المستودعات</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('warehouse.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة مستودع جديد
        </a>
        <a href="{{ url_for('warehouse.dashboard') }}" class="btn btn-outline-secondary ml-2">
            <i class="fas fa-arrow-right"></i> العودة
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-warehouse"></i> المستودعات الحالية</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المستودع</th>
                        <th>الموقع</th>
                        <th>الأسلحة</th>
                        <th>الطاقة الاستيعابية</th>
                        <th>الأفراد</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for warehouse in warehouses %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>
                            <strong>{{ warehouse.name }}</strong>
                            {% if warehouse.description %}
                            <small class="d-block text-muted">{{ warehouse.description[:50] }}{% if
                                warehouse.description|length > 50 %}...{% endif %}</small>
                            {% endif %}
                        </td>
                        <td>{{ warehouse.location or '-' }}</td>
                        <td>{{ warehouse.weapons.count() }}</td>
                        <td>
                            {% if warehouse.capacity > 0 %}
                            <div class="d-flex align-items-center">
                                <span class="mr-2">{{ warehouse.weapons.count() }} / {{ warehouse.capacity }}</span>
                                <div class="progress flex-grow-1" style="height: 6px; width: 60px;">
                                    <div class="progress-bar
                                        {% if warehouse.get_usage_percentage() >= 90 %}bg-danger
                                        {% elif warehouse.get_usage_percentage() >= 75 %}bg-warning
                                        {% else %}bg-success{% endif %}"
                                        style="width: {{ warehouse.get_usage_percentage() }}%">
                                    </div>
                                </div>
                                <small class="ml-1 text-muted">{{ warehouse.get_usage_percentage() }}%</small>
                            </div>
                            {% else %}
                            <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>{{ warehouse.personnel.count() }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('warehouse.warehouse_detail', warehouse_id=warehouse.id) }}"
                                    class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('warehouse.edit_warehouse', warehouse_id=warehouse.id) }}"
                                    class="btn btn-sm btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('warehouse.display', warehouse_id=warehouse.id) }}"
                                    class="btn btn-sm btn-outline-info" target="_blank" title="عرض الشاشة">
                                    <i class="fas fa-desktop"></i>
                                </a>
                                {% if current_user.is_admin_role or current_user.is_warehouse_manager %}
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="confirmDelete('{{ warehouse.id }}', '{{ warehouse.name }}')"
                                        title="حذف المستودع">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center py-3">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> لا توجد مستودعات مسجلة حتى الآن
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- نموذج حذف المستودع -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
</form>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-danger text-white border-0">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i> تأكيد حذف المستودع
                </h5>
            </div>
            <div class="modal-body p-4">
                <div class="alert alert-warning border-0 mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                </div>
                <p class="mb-3">هل أنت متأكد من رغبتك في حذف المستودع <strong id="warehouseName" class="text-danger"></strong>؟</p>
                <p class="text-muted small mb-0">
                    <i class="fas fa-info-circle me-1"></i>
                    ملاحظة: لا يمكن حذف المستودع إذا كان يحتوي على أسلحة أو أفراد أو أجهزة مرتبطة به.
                </p>
            </div>
            <div class="modal-footer border-0 pt-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="cancelDelete()">
                    <i class="fas fa-times me-1"></i> إلغاء
                </button>
                <button type="button" class="btn btn-danger" onclick="deleteWarehouse()">
                    <i class="fas fa-trash me-1"></i> حذف المستودع
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let warehouseToDelete = null;

function confirmDelete(warehouseId, warehouseName) {
    warehouseToDelete = warehouseId;
    document.getElementById('warehouseName').textContent = warehouseName;

    // عرض إشعار تحذيري عند النقر على زر الحذف
    if (typeof showNotification === 'function') {
        showNotification('تحذير: هذا الإجراء لا يمكن التراجع عنه', 'warning', 3000);
    }

    // عرض النافذة المنبثقة
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function cancelDelete() {
    warehouseToDelete = null;
    const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
    if (modal) {
        modal.hide();
    }
}

function deleteWarehouse() {
    if (warehouseToDelete) {
        const form = document.getElementById('deleteForm');
        form.action = `{{ url_for('warehouse.delete_warehouse', warehouse_id=0) }}`.replace('0', warehouseToDelete);
        form.submit();
    }
}

// إخفاء الإشعارات التلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء أي إشعارات موجودة عند تحميل الصفحة
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => {
        // التحقق من أن الإشعار يحتوي على نص التحذير المحدد
        if (alert.textContent.includes('تحذير: هذا الإجراء لا يمكن التراجع عنه')) {
            alert.style.display = 'none';
        }
    });
});
</script>
{% endblock %}