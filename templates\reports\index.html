{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            التقارير والإحصائيات
        </h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('reports.backup') }}" class="btn btn-outline-secondary">
            <i class="fas fa-database"></i> النسخ الاحتياطي
        </a>
    </div>
</div>

<!-- معلومات التاريخ الحالي -->
<div class="alert alert-info mb-4">
    <div class="row">
        <div class="col-md-6">
            <h6><i class="fas fa-calendar-alt"></i> التاريخ الحالي:</h6>
            <p class="mb-0">
                <strong>هجري:</strong> <span id="current-hijri-date"></span><br>
                <strong>ميلادي:</strong> <span id="current-gregorian-date"></span><br>
                <strong>اليوم:</strong> <span id="current-day"></span>
            </p>
        </div>
        <div class="col-md-6">
            <h6><i class="fas fa-clock"></i> الوقت الحالي:</h6>
            <p class="mb-0">
                <strong>الوقت:</strong> <span id="current-time"></span><br>
                <strong>فترة الأسبوع الحالي:</strong><br>
                <small><span id="current-week-range"></span></small>
            </p>
        </div>
    </div>
</div>

<!-- قسم تقارير الأفراد -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-users mr-2"></i> تقارير الأفراد</h4>
                <small>تصدير تقارير الأفراد حسب الحالة الحالية أو تغييرات الحالات خلال الأسبوع</small>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- التقارير السريعة -->
                    <div class="col-lg-6 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-bolt text-warning mr-2"></i> تقارير الحالة الحالية</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0) }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-primary mr-2"></i> جميع الأفراد
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='نشط') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-success mr-2"></i> الأفراد النشطين
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='إجازة') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-info mr-2"></i> الأفراد في إجازة
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='مستلم') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-secondary mr-2"></i> الأفراد المستلمين
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='مهمة') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-warning mr-2"></i> الأفراد في مهمة
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='دورة') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-primary mr-2"></i> الأفراد في دورة
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='رماية') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-danger mr-2"></i> الأفراد في رماية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التقارير الأسبوعية -->
                    <div class="col-lg-6 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-calendar-week text-info mr-2"></i> تقارير التغييرات الأسبوعية</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, weekly='true') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-primary mr-2"></i> جميع تغييرات الحالات
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='نشط', weekly='true') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-success mr-2"></i> تغييرات حالة "نشط"
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='إجازة', weekly='true') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-info mr-2"></i> تغييرات حالة "إجازة"
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='مستلم', weekly='true') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-secondary mr-2"></i> تغييرات حالة "مستلم"
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='مهمة', weekly='true') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-warning mr-2"></i> تغييرات حالة "مهمة"
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='دورة', weekly='true') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-primary mr-2"></i> تغييرات حالة "دورة"
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0, status='رماية', weekly='true') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-danger mr-2"></i> تغييرات حالة "رماية"
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير المستودعات -->
                {% if warehouses %}
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-warehouse text-secondary mr-2"></i> تقارير حسب المستودع</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for warehouse in warehouses %}
                                    <div class="col-md-6 col-lg-4 mb-2">
                                        <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=warehouse.id) }}"
                                            class="btn btn-outline-secondary btn-block">
                                            <i class="fas fa-building mr-2"></i> {{ warehouse.name }}
                                        </a>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- قسم التقارير الأخرى -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0"><i class="fas fa-chart-line mr-2"></i> التقارير الأخرى</h4>
                <small>تقارير الأسلحة والأجهزة والمعاملات والصيانة</small>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- تقارير الأسلحة -->
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-gun text-danger mr-2"></i> تقارير الأسلحة</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <a href="{{ url_for('reports.export', export_type='weapons', warehouse_id=0) }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-danger mr-2"></i> جميع الأسلحة
                                    </a>
                                    {% for warehouse in warehouses %}
                                    <a href="{{ url_for('reports.export', export_type='weapons', warehouse_id=warehouse.id) }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-building text-secondary mr-1"></i> {{ warehouse.name }}
                                    </a>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقارير الأجهزة -->
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-desktop text-info mr-2"></i> تقارير الأجهزة</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <a href="{{ url_for('reports.export', export_type='devices', warehouse_id=0) }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-info mr-2"></i> جميع الأجهزة
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='device_maintenance') }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-tools text-warning mr-2"></i> صيانة الأجهزة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقارير المعاملات -->
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-exchange-alt text-warning mr-2"></i> تقارير المعاملات</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <a href="{{ url_for('reports.export', export_type='transactions', warehouse_id=0) }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-warning mr-2"></i> جميع المعاملات
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='transactions', transaction_type='checkout', warehouse_id=0) }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-sign-out-alt text-danger mr-2"></i> معاملات التسليم
                                    </a>
                                    <a href="{{ url_for('reports.export', export_type='transactions', transaction_type='return', warehouse_id=0) }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-sign-in-alt text-success mr-2"></i> معاملات الاستلام
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقارير الصيانة -->
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-tools text-secondary mr-2"></i> تقارير الصيانة</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <a href="{{ url_for('reports.export', export_type='maintenance', warehouse_id=0) }}"
                                        class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-export text-secondary mr-2"></i> تقارير الصيانة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قسم الإحصائيات -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0"><i class="fas fa-chart-bar mr-2"></i> الإحصائيات والتحليلات</h4>
                <small>تقارير إحصائية شاملة وتحليلات بيانية للمستودعات والأسلحة والأفراد والأجهزة</small>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('reports.statistics', stat_type='warehouses') }}"
                            class="btn btn-outline-primary btn-lg btn-block h-100 d-flex align-items-center justify-content-center">
                            <div class="text-center">
                                <i class="fas fa-warehouse fa-2x mb-2"></i><br>
                                <strong>إحصائيات المستودعات</strong>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('reports.statistics', stat_type='weapons') }}"
                            class="btn btn-outline-danger btn-lg btn-block h-100 d-flex align-items-center justify-content-center">
                            <div class="text-center">
                                <i class="fas fa-gun fa-2x mb-2"></i><br>
                                <strong>إحصائيات الأسلحة</strong>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('reports.statistics', stat_type='personnel') }}"
                            class="btn btn-outline-success btn-lg btn-block h-100 d-flex align-items-center justify-content-center">
                            <div class="text-center">
                                <i class="fas fa-users fa-2x mb-2"></i><br>
                                <strong>إحصائيات الأفراد</strong>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('reports.statistics', stat_type='devices') }}"
                            class="btn btn-outline-info btn-lg btn-block h-100 d-flex align-items-center justify-content-center">
                            <div class="text-center">
                                <i class="fas fa-desktop fa-2x mb-2"></i><br>
                                <strong>إحصائيات الأجهزة</strong>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<style>
/* تحسينات التصميم للتقارير */
.card {
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.list-group-item-action:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* إصلاح العناوين التي لها خلفية بيضاء فقط */
.card-header.bg-light {
    background-color: #343a40 !important;
    color: #ffffff !important;
    border-bottom: 1px solid #495057 !important;
}

.card-header h4 {
    font-weight: 600;
}

.card-header h6 {
    font-weight: 600;
}

.card-header small {
    opacity: 0.9;
}

.border-primary {
    border-color: #007bff !important;
    border-width: 2px !important;
}

.border-success {
    border-color: #28a745 !important;
    border-width: 2px !important;
}

.border-info {
    border-color: #17a2b8 !important;
    border-width: 2px !important;
}

/* تحسين الأيقونات */
.fa-2x {
    color: inherit;
}

/* تحسين المسافات */
.mb-4 {
    margin-bottom: 2rem !important;
}

/* تحسين الأزرار الكبيرة */
.btn-lg {
    padding: 1rem;
    min-height: 120px;
}

/* تحسين النصوص */
.text-center strong {
    font-size: 0.9rem;
}



/* تحسين الاستجابة */
@media (max-width: 768px) {
    .btn-lg {
        min-height: 80px;
        padding: 0.75rem;
    }

    .fa-2x {
        font-size: 1.5em !important;
    }

    .card-header h4 {
        font-size: 1.1rem;
    }
}
</style>

<script>
// دالة لتحديث التاريخ والوقت
function updateDateTime() {
    // استدعاء API للحصول على التاريخ الهجري
    fetch('/reports/api/current-date-info')
        .then(response => response.json())
        .then(data => {
            document.getElementById('current-hijri-date').textContent = data.hijri_formatted;
            document.getElementById('current-gregorian-date').textContent = data.gregorian_formatted;
            document.getElementById('current-day').textContent = data.day_name;
            document.getElementById('current-time').textContent = data.time_24h;
            document.getElementById('current-week-range').textContent = data.week_range;
        })
        .catch(error => {
            console.error('Error fetching date info:', error);
            // عرض التاريخ الميلادي كبديل
            const now = new Date();
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                weekday: 'long'
            };
            document.getElementById('current-gregorian-date').textContent = now.toLocaleDateString('ar-SA', options);
            document.getElementById('current-time').textContent = now.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
        });
}

// تحديث التاريخ عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateDateTime();
});

// تحديث التاريخ كل دقيقة
setInterval(updateDateTime, 60000);
</script>

{% endblock %}