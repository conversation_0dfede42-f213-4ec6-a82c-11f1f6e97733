{% extends "base.html" %}

{% block title %}إضافة ملبوسات جديدة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-tshirt text-success"></i>
                    إضافة ملبوسات جديدة
                </h2>
                <div>
                    <a href="{{ url_for('standalone_inventory.items', category='ملبوسات') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للملبوسات
                    </a>
                    <a href="{{ url_for('standalone_inventory.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        بيانات الملبوس
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('standalone_inventory.add_clothing') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.item_code.label(class="form-label required") }}
                                    {{ form.item_code(class="form-control", placeholder="مثال: CLO-001", id="item_code") }}
                                    {% if form.item_code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.item_code.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">كود فريد للملبوس (يمكن استخدامه كباركود)</small>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label required") }}
                                    {{ form.name(class="form-control", placeholder="مثال: زي عسكري صيفي") }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.subcategory.label(class="form-label required") }}
                                    {{ form.subcategory(class="form-control", id="subcategory") }}
                                    {% if form.subcategory.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.subcategory.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.size.label(class="form-label") }}
                                    {{ form.size(class="form-control") }}
                                    {% if form.size.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.size.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.color.label(class="form-label") }}
                                    {{ form.color(class="form-control") }}
                                    {% if form.color.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.color.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.material.label(class="form-label") }}
                                    {{ form.material(class="form-control", placeholder="مثال: قطن، بوليستر، جلد") }}
                                    {% if form.material.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.material.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.quantity.label(class="form-label required") }}
                                    {{ form.quantity(class="form-control", placeholder="0") }}
                                    {% if form.quantity.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.quantity.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-group">
                                    {{ form.notes.label(class="form-label") }}
                                    {{ form.notes(class="form-control", rows="3", placeholder="أي ملاحظات إضافية حول الملبوس...") }}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{{ url_for('standalone_inventory.items', category='ملبوسات') }}" class="btn btn-secondary me-2">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    {{ form.submit(class="btn btn-success") }}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Add Examples -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb"></i>
                        أمثلة سريعة للملبوسات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success btn-sm w-100" onclick="fillExample('زي رسمي', 'زي رسمي صيفي', 'M', 'أخضر')">
                                زي رسمي صيفي
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success btn-sm w-100" onclick="fillExample('أحذية', 'حذاء عسكري', '42', 'أسود')">
                                حذاء عسكري
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success btn-sm w-100" onclick="fillExample('قبعات', 'قبعة عسكرية', 'L', 'كاكي')">
                                قبعة عسكرية
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success btn-sm w-100" onclick="fillExample('أحزمة', 'حزام عسكري', 'L', 'بني')">
                                حزام عسكري
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barcode Scanner Info -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-barcode"></i> دعم مسح الباركود:</h6>
                <ul class="mb-0">
                    <li>يمكنك مسح الباركود مباشرة في حقل "كود الملبوس"</li>
                    <li>أو يمكنك مسح الباركود في أي مكان في الصفحة وسيتم وضعه تلقائياً في الحقل</li>
                    <li>إذا كان الملبوس موجود مسبقاً، ستظهر رسالة تنبيه</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تفعيل التركيز على حقل الكود عند تحميل الصفحة
    $('#item_code').focus();
    
    // إنشاء كود تلقائي بناءً على نوع الملبوس
    $('#subcategory').change(function() {
        var subcategory = $(this).val();
        var codeField = $('#item_code');
        
        if (subcategory && !codeField.val()) {
            var prefix = 'CLO-';
            var randomNum = Math.floor(Math.random() * 9000) + 1000;
            codeField.val(prefix + randomNum);
        }
    });
    
    // دعم مسح الباركود
    var barcodeBuffer = '';
    var barcodeTimeout;
    
    $(document).keypress(function(e) {
        if ($('input[type="text"], textarea, select').is(':focus')) {
            return;
        }
        
        barcodeBuffer += String.fromCharCode(e.which);
        
        clearTimeout(barcodeTimeout);
        barcodeTimeout = setTimeout(function() {
            if (barcodeBuffer.length > 3) {
                $('#item_code').val(barcodeBuffer);
                $('#item_code').focus();
                checkExistingItem(barcodeBuffer);
            }
            barcodeBuffer = '';
        }, 100);
    });
    
    // التحقق من وجود الملبوس عند تغيير الكود
    $('#item_code').blur(function() {
        var code = $(this).val();
        if (code) {
            checkExistingItem(code);
        }
    });
    
    function checkExistingItem(code) {
        $.get('{{ url_for("standalone_inventory.search_barcode") }}', {barcode: code})
        .done(function(data) {
            if (data.found) {
                showAlert('تحذير: هذا الكود موجود مسبقاً للصنف: ' + data.item.name, 'warning');
            }
        });
    }
    
    function showAlert(message, type) {
        var alertClass = 'alert-' + type;
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        
        $('.alert').not('.alert-info').remove();
        $('.container-fluid').prepend(alertHtml);
        
        setTimeout(function() {
            $('.alert-' + type).fadeOut();
        }, 5000);
    }
});

// ملء مثال سريع
function fillExample(subcategory, name, size, color) {
    $('#subcategory').val(subcategory);
    $('#name').val(name);
    $('#size').val(size);
    $('#color').val(color);
    
    // إنشاء كود تلقائي
    var randomNum = Math.floor(Math.random() * 9000) + 1000;
    $('#item_code').val('CLO-' + randomNum);
}
</script>
{% endblock %}
