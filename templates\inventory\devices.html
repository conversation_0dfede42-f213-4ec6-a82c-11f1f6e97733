{% extends "base.html" %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/warehouse-list.css') }}">
<style>
    /* تنسيقات خاصة بصفحة الأجهزة */
    .search-highlight {
        background-color: #ffff99;
        font-weight: bold;
    }

    /* تنسيق أزرار الإجراءات */
    .device-actions {
        display: flex;
        gap: 5px;
    }

    /* تنسيقات البحث */
    #searchForm {
        width: 300px;
    }

    #searchInput {
        border-radius: 4px 0 0 4px;
        border-right: none;
    }

    #searchForm .btn {
        border-radius: 0 4px 4px 0;
    }

    #searchInput:focus {
        box-shadow: none;
        border-color: #ced4da;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>الأجهزة الإدارية</h3>
    </div>
    <div class="col-md-4 text-right">
        {% if current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager %}
        <a href="{{ url_for('inventory.create_device') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle"></i> إضافة جهاز جديد
        </a>
        {% endif %}
    </div>
</div>

<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div class="me-0">
            <h5 class="mb-0">
                <i class="fas fa-desktop"></i>
                {% if search_query %}
                    نتائج البحث عن: "{{ search_query }}"
                    <span class="badge bg-light text-dark">{{ devices|length }} نتيجة</span>
                {% else %}
                    الأجهزة الإدارية
                {% endif %}
            </h5>
        </div>
        <div>
            <form action="{{ url_for('inventory.devices') }}" method="GET" class="d-flex" id="searchForm">
                <input type="text" name="q" class="form-control ms-2" placeholder="ابحث بالاسم أو الرقم التسلسلي..." value="{{ search_query }}" id="searchInput" autocomplete="off">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead class="thead-dark">
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">اسم الجهاز</th>
                        <th scope="col">النوع</th>
                        <th scope="col">الشركة المصنعة</th>
                        <th scope="col">الموديل</th>
                        <th scope="col">الرقم التسلسلي</th>
                        <th scope="col">الحالة</th>
                        <th scope="col">الموقع</th>
                        <th scope="col">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if devices %}
                    {% for device in devices %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ device.name }}</td>
                        <td>
                            {% if device.type == 'computer' %}
                            <span class="badge badge-primary">كمبيوتر</span>
                            {% elif device.type == 'laptop' %}
                            <span class="badge badge-primary">لابتوب</span>
                            {% elif device.type == 'printer' %}
                            <span class="badge badge-success">طابعة</span>
                            {% elif device.type == 'scanner' %}
                            <span class="badge badge-info">سكانر</span>
                            {% elif device.type == 'monitor' %}
                            <span class="badge badge-secondary">شاشة</span>
                            {% elif device.type == 'projector' %}
                            <span class="badge badge-warning">جهاز عرض</span>
                            {% elif device.type == 'server' %}
                            <span class="badge badge-danger">سيرفر</span>
                            {% elif device.type == 'network' %}
                            <span class="badge badge-primary">معدات شبكة</span>
                            {% elif device.type == 'camera' %}
                            <span class="badge badge-info">كاميرا</span>
                            {% elif device.type == 'ups' %}
                            <span class="badge badge-secondary">مزود طاقة احتياطي</span>
                            {% else %}
                            <span class="badge badge-dark">أخرى</span>
                            {% endif %}
                        </td>
                        <td>{{ device.manufacturer or '-' }}</td>
                        <td>{{ device.model or '-' }}</td>
                        <td>{{ device.serial_number or '-' }}</td>
                        <td>
                            {% if device.status == 'سليم' %}
                            <span class="badge badge-success">{{ device.status }}</span>
                            {% elif device.status == 'عطل بسيط' %}
                            <span class="badge badge-warning">{{ device.status }}</span>
                            {% elif device.status == 'عطل جسيم' %}
                            <span class="badge badge-danger">{{ device.status }}</span>
                            {% elif device.status == 'تحت الصيانة' %}
                            <span class="badge badge-maintenance">{{ device.status }}</span>
                            {% elif device.status == 'خارج الخدمة' %}
                            <span class="badge badge-secondary">{{ device.status }}</span>
                            {% elif device.status == 'مفقود' %}
                            <span class="badge badge-dark">{{ device.status }}</span>
                            {% else %}
                            <span class="badge badge-light">{{ device.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>
                                {% if device.location %}
                                {{ device.location }}
                                {% else %}
                                -
                                {% endif %}
                            </small>
                        </td>
                        <td>
                            <div class="device-actions">
                                <a href="{{ url_for('inventory.device_details', device_id=device.id) }}"
                                    class="btn btn-sm btn-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager %}
                                <a href="{{ url_for('inventory.edit_device', device_id=device.id) }}"
                                    class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('inventory.add_device_maintenance', device_id=device.id) }}"
                                    class="btn btn-sm btn-secondary" title="إضافة صيانة">
                                    <i class="fas fa-tools"></i>
                                </a>
                                <form method="POST" action="{{ url_for('inventory.delete_device', device_id=device.id) }}">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% else %}
                                <button class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                    {% else %}
                    <tr>
                        <td colspan="9" class="text-center">لا توجد أجهزة مسجلة</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const searchForm = document.getElementById('searchForm');

        // تعيين التركيز على حقل البحث عند تحميل الصفحة ووضع المؤشر في نهاية النص
        searchInput.focus();
        // وضع المؤشر في نهاية النص
        const inputValue = searchInput.value;
        searchInput.value = '';
        searchInput.value = inputValue;

        // متغير لتخزين الوقت المستغرق بين الإدخالات
        let typingTimer;
        // الفترة الزمنية بالمللي ثانية قبل إرسال النموذج (300 مللي ثانية = 0.3 ثانية)
        const doneTypingInterval = 300;

        // عند الكتابة في حقل البحث
        searchInput.addEventListener('input', function() {
            // إعادة ضبط المؤقت في كل مرة يتم فيها الكتابة
            clearTimeout(typingTimer);

            // إذا كان الحقل غير فارغ، ابدأ المؤقت
            if (searchInput.value) {
                typingTimer = setTimeout(submitForm, doneTypingInterval);
            }
        });

        // وظيفة إرسال النموذج
        function submitForm() {
            searchForm.submit();
        }

        // عند مسح الحقل بالكامل، قم بإرسال النموذج أيضًا للعودة إلى القائمة الكاملة
        searchInput.addEventListener('keyup', function(e) {
            if (searchInput.value === '' && e.key === 'Backspace') {
                submitForm();
            }
        });

        // إبراز نتائج البحث إذا كان هناك بحث
        const searchQuery = '{{ search_query }}';
        if (searchQuery) {
            highlightSearchResults(searchQuery);
        }

        // دالة لإبراز نتائج البحث
        function highlightSearchResults(query) {
            if (!query) return;

            // الأعمدة التي نريد البحث فيها
            const columns = [1, 5]; // اسم الجهاز والرقم التسلسلي

            // الحصول على جميع صفوف الجدول
            const rows = document.querySelectorAll('table tbody tr');

            rows.forEach(row => {
                columns.forEach(colIndex => {
                    const cell = row.cells[colIndex];
                    if (cell) {
                        const text = cell.textContent;
                        if (text.toLowerCase().includes(query.toLowerCase())) {
                            // إبراز النص المطابق
                            const regex = new RegExp(query, 'gi');
                            cell.innerHTML = text.replace(regex, match => `<span class="search-highlight">${match}</span>`);
                        }
                    }
                });
            });
        }
    });
</script>
{% endblock %}