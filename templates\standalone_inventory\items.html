{% extends "base.html" %}

{% block title %}قائمة الأصناف{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-list text-primary"></i>
                    قائمة الأصناف
                </h2>
                <div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-plus"></i> إضافة صنف جديد
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('standalone_inventory.add_clothing') }}">
                                <i class="fas fa-tshirt text-success"></i> إضافة ملبوسات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('standalone_inventory.add_equipment') }}">
                                <i class="fas fa-tools text-info"></i> إضافة معدات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('standalone_inventory.add_ammunition') }}">
                                <i class="fas fa-bomb text-danger"></i> إضافة ذخيرة
                            </a></li>
                        </ul>
                    </div>
                    <a href="{{ url_for('standalone_inventory.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search"></i>
                        البحث والفلترة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('standalone_inventory.items') }}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ search }}" placeholder="اسم الصنف، الكود، الباركود، أو الفئة الفرعية">
                                <small class="form-text text-muted">يمكنك البحث بالاسم أو الكود أو مسح الباركود مباشرة</small>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="category" class="form-label">الفئة</label>
                                <select class="form-control" id="category" name="category">
                                    <option value="">جميع الفئات</option>
                                    <option value="ملبوسات" {% if category == 'ملبوسات' %}selected{% endif %}>ملبوسات</option>
                                    <option value="معدات" {% if category == 'معدات' %}selected{% endif %}>معدات</option>
                                    <option value="ذخيرة" {% if category == 'ذخيرة' %}selected{% endif %}>ذخيرة</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="{{ url_for('standalone_inventory.items') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table"></i>
                        قائمة الأصناف ({{ items|length }} صنف)
                    </h5>
                </div>
                <div class="card-body">
                    {% if items %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="itemsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>كود الصنف</th>
                                    <th>اسم الصنف</th>
                                    <th>الفئة</th>
                                    <th>المقاس</th>
                                    <th>اللون</th>
                                    <th>الكمية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items %}
                                <tr>
                                    <td>
                                        <strong>{{ item.item_code }}</strong>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('standalone_inventory.item_details', item_id=item.id) }}"
                                           class="text-decoration-none">
                                            {{ item.name }}
                                        </a>
                                        {% if item.subcategory %}
                                        <br><small class="text-muted">{{ item.subcategory }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge 
                                            {% if item.category == 'ملبوسات' %}bg-success
                                            {% elif item.category == 'معدات' %}bg-info
                                            {% elif item.category == 'ذخيرة' %}bg-danger
                                            {% else %}bg-secondary{% endif %}">
                                            {{ item.category }}
                                        </span>
                                    </td>
                                    <td>{{ item.size or '-' }}</td>
                                    <td>{{ item.color or '-' }}</td>
                                    <td>
                                        <span class="fw-bold">{{ item.quantity_in_stock }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('standalone_inventory.item_details', item_id=item.id) }}"
                                               class="btn btn-outline-primary" title="التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('standalone_inventory.edit_item', item_id=item.id) }}"
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-info" title="نسخ الكود"
                                                    onclick="copyToClipboard('{{ item.item_code }}')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="حذف"
                                                    onclick="confirmDelete({{ item.id }}, '{{ item.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أصناف</h5>
                        <p class="text-muted">لم يتم العثور على أصناف تطابق معايير البحث</p>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-plus"></i> إضافة صنف جديد
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('standalone_inventory.add_clothing') }}">
                                    <i class="fas fa-tshirt text-success"></i> إضافة ملبوسات
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('standalone_inventory.add_equipment') }}">
                                    <i class="fas fa-tools text-info"></i> إضافة معدات
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('standalone_inventory.add_ammunition') }}">
                                    <i class="fas fa-bomb text-danger"></i> إضافة ذخيرة
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Category Filters -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-filter"></i>
                        فلترة سريعة حسب الفئة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('standalone_inventory.items', category='ملبوسات') }}" 
                               class="btn btn-outline-success btn-block w-100">
                                <i class="fas fa-tshirt"></i> ملبوسات
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('standalone_inventory.items', category='معدات') }}" 
                               class="btn btn-outline-info btn-block w-100">
                                <i class="fas fa-tools"></i> معدات
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('standalone_inventory.items', category='ذخيرة') }}" 
                               class="btn btn-outline-danger btn-block w-100">
                                <i class="fas fa-bomb"></i> ذخيرة
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('standalone_inventory.items') }}" 
                               class="btn btn-outline-secondary btn-block w-100">
                                <i class="fas fa-list"></i> جميع الأصناف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تفعيل DataTables للجدول
    if ($('#itemsTable tbody tr').length > 0) {
        $('#itemsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "pageLength": 25,
            "order": [[0, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": -1 }
            ]
        });
    }
    
    // دعم مسح الباركود
    var barcodeBuffer = '';
    var barcodeTimeout;
    
    $(document).keypress(function(e) {
        // إذا كان المستخدم يكتب في حقل البحث
        if ($('#search').is(':focus')) {
            return;
        }
        
        // تجميع الأحرف للباركود
        barcodeBuffer += String.fromCharCode(e.which);
        
        clearTimeout(barcodeTimeout);
        barcodeTimeout = setTimeout(function() {
            if (barcodeBuffer.length > 3) {
                // وضع الباركود في حقل البحث وتنفيذ البحث
                $('#search').val(barcodeBuffer);
                $('form').submit();
            }
            barcodeBuffer = '';
        }, 100);
    });
    
    // تفعيل البحث السريع عند الضغط على Enter
    $('#search').keypress(function(e) {
        if (e.which == 13) {
            $('form').submit();
        }
    });
});

// نسخ الكود إلى الحافظة
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // إظهار رسالة نجاح
        showToast('تم نسخ الكود: ' + text, 'success');
    }, function(err) {
        console.error('Could not copy text: ', err);
        showToast('فشل في نسخ الكود', 'error');
    });
}

// تأكيد الحذف
function confirmDelete(itemId, itemName) {
    if (confirm('هل أنت متأكد من حذف الصنف "' + itemName + '"؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // إنشاء نموذج مخفي لإرسال طلب الحذف
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("standalone_inventory.delete_item", item_id=0) }}'.replace('0', itemId);

        // إضافة CSRF token إذا كان متوفراً
        var csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            var csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// إظهار رسالة Toast
function showToast(message, type) {
    var toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
    var toastHtml = '<div class="toast align-items-center text-white ' + toastClass + ' border-0" role="alert">' +
                   '<div class="d-flex">' +
                   '<div class="toast-body">' + message + '</div>' +
                   '<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>' +
                   '</div></div>';

    // إضافة Toast container إذا لم يكن موجود
    if ($('#toast-container').length === 0) {
        $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }

    $('#toast-container').append(toastHtml);
    var toast = new bootstrap.Toast($('.toast').last());
    toast.show();
}
</script>
{% endblock %}
