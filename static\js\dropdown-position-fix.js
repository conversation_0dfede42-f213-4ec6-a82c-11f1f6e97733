/**
 * إصلاح موضع القوائم المنسدلة في صفحة قائمة الأسلحة
 */
document.addEventListener('DOMContentLoaded', function() {
    // الحصول على جميع أزرار القوائم المنسدلة
    const dropdownToggleButtons = document.querySelectorAll('.dropdown-toggle');

    // إضافة معالج حدث لكل زر
    dropdownToggleButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // إغلاق جميع القوائم المنسدلة الأخرى
            document.querySelectorAll('.dropdown-menu.show').forEach(function(openMenu) {
                if (!button.parentNode.contains(openMenu)) {
                    openMenu.classList.remove('show');
                }
            });

            // الحصول على القائمة المنسدلة المرتبطة بالزر
            let menu = null;

            // البحث عن القائمة المنسدلة في العناصر الشقيقة للزر
            let sibling = button.nextElementSibling;
            while (sibling) {
                if (sibling.classList.contains('dropdown-menu')) {
                    menu = sibling;
                    break;
                }
                sibling = sibling.nextElementSibling;
            }

            // إذا لم يتم العثور على القائمة، ابحث في العنصر الأب
            if (!menu) {
                const parent = button.closest('.btn-block, .dropdown, .btn-group');
                if (parent) {
                    menu = parent.querySelector('.dropdown-menu');
                }
            }

            if (menu) {
                // تبديل حالة القائمة المنسدلة
                menu.classList.toggle('show');

                // تحديد موضع القائمة المنسدلة بناءً على موضع الزر
                const buttonRect = button.getBoundingClientRect();
                menu.style.position = 'fixed';
                menu.style.top = (buttonRect.bottom + window.scrollY) + 'px';
                menu.style.right = (window.innerWidth - buttonRect.right) + 'px';

                // إغلاق القائمة عند النقر في أي مكان آخر
                const closeMenu = function(event) {
                    if (!button.contains(event.target) && !menu.contains(event.target)) {
                        menu.classList.remove('show');
                        document.removeEventListener('click', closeMenu);
                    }
                };

                document.addEventListener('click', closeMenu);
            }
        });
    });
});
