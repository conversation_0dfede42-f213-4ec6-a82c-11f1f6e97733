{% extends 'base.html' %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card bg-dark text-white">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4>باركود للسلاح: {{ weapon.name }}</h4>
                    <a href="{{ url_for('weapons.details', weapon_id=weapon.id) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i> العودة
                    </a>
                </div>
                <div class="card-body text-center">
                    <div class="barcode-container mb-4">
                        <div id="barcode-display" class="mx-auto"></div>
                    </div>

                    <div class="weapon-info mb-4">
                        <p><strong>الرقم التسلسلي:</strong> {{ weapon.serial_number }}</p>
                        <p><strong>اسم السلاح:</strong> {{ weapon.name }}</p>
                        <p><strong>النوع:</strong> {% if weapon.type == 'pistol' %}مسدس{% elif weapon.type == 'rifle' %}بندقية{% elif weapon.type == 'sniper' %}قناص{% elif weapon.type == 'machine_gun' %}رشاش{% else %}{{ weapon.type }}{% endif %}</p>
                        <p><strong>المستودع:</strong> {{ weapon.warehouse.name }}</p>
                    </div>

                    <div class="action-buttons">
                        <a href="{{ url_for('weapons.barcode', weapon_id=weapon.id, download=1) }}" class="btn btn-primary">
                            <i class="fas fa-download"></i> تحميل الباركود
                        </a>
                        <a href="{{ url_for('weapons.details', weapon_id=weapon.id) }}" class="btn btn-secondary">
                            <i class="fas fa-info-circle"></i> عرض تفاصيل السلاح
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Generate QR code instead of barcode (since barcode needs PIL)
        try {
            var barcodeContainer = document.getElementById("barcode-display");
            if (!barcodeContainer) {
                console.error('Barcode container not found');
                return;
            }

            // Check if QRCode library is available
            if (typeof QRCode === 'undefined') {
                console.error('QRCode library not loaded');
                barcodeContainer.innerHTML = '<div class="alert alert-warning">مكتبة QR Code غير متوفرة</div>';
                return;
            }

            // Clear any existing content
            barcodeContainer.innerHTML = '';

            // Generate QR code with full weapon serial number
            var qrData = "{{ weapon.serial_number }}";
            console.log('Generating QR code with data:', qrData);

            var qrcode = new QRCode(barcodeContainer, {
                text: qrData,
                width: 200,
                height: 200,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L,  // Lowest error correction for maximum data
                typeNumber: 4  // Use version 4 which supports more data
            });

            console.log('✅ QR code generated successfully');

        } catch (error) {
            console.error('❌ Error generating QR code:', error);
            var barcodeContainer = document.getElementById("barcode-display");
            if (barcodeContainer) {
                barcodeContainer.innerHTML = '<div class="alert alert-danger">خطأ في إنشاء QR Code</div>';
            }
        }
    });
</script>
{% endblock %}
