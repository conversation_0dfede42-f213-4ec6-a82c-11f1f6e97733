{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="m-0">لوحة المراقبة</h5>
                <div class="btn-group position-relative">
                    <button type="button" id="displayScreensButton" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-eye"></i> شاشات العرض
                    </button>
                    <div id="displayScreensDropdown" class="dropdown-menu">
                        {% for warehouse in warehouses %}
                        <a class="dropdown-item" href="{{ url_for('warehouse.display', warehouse_id=warehouse.id) }}">
                            <i class="fas fa-desktop"></i> شاشة {{ warehouse.name }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for warehouse in warehouses %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5>{{ warehouse.name }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <div class="stat-card">
                                            <div class="icon">
                                                <i class="fas fa-crosshairs"></i>
                                            </div>
                                            <div class="info">
                                                <h4>{{ warehouse_stats[warehouse.id]['weapons_count'] }}</h4>
                                                <span>الأسلحة</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-card">
                                            <div class="icon">
                                                <i class="fas fa-users"></i>
                                            </div>
                                            <div class="info">
                                                <h4>{{ warehouse_stats[warehouse.id]['personnel_count'] }}</h4>
                                                <span>الأفراد</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الطاقة الاستيعابية -->
                                {% if warehouse_stats[warehouse.id]['capacity'] > 0 %}
                                <div class="mb-3">
                                    <h6>الطاقة الاستيعابية</h6>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="stat-card small">
                                                <div class="icon">
                                                    <i class="fas fa-warehouse"></i>
                                                </div>
                                                <div class="info">
                                                    <h6>{{ warehouse_stats[warehouse.id]['capacity'] }}</h6>
                                                    <small>السعة القصوى</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-card small">
                                                <div class="icon">
                                                    <i class="fas fa-chart-pie"></i>
                                                </div>
                                                <div class="info">
                                                    <h6>{{ warehouse_stats[warehouse.id]['usage_percentage'] }}%</h6>
                                                    <small>نسبة الاستخدام</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-card small">
                                                <div class="icon">
                                                    <i class="fas fa-plus-circle"></i>
                                                </div>
                                                <div class="info">
                                                    <h6>{{ warehouse_stats[warehouse.id]['available_capacity'] }}</h6>
                                                    <small>المتاح</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- شريط التقدم -->
                                    <div class="progress mt-2" style="height: 8px;">
                                        <div class="progress-bar
                                            {% if warehouse_stats[warehouse.id]['usage_percentage'] >= 90 %}bg-danger
                                            {% elif warehouse_stats[warehouse.id]['usage_percentage'] >= 75 %}bg-warning
                                            {% else %}bg-success{% endif %}"
                                            role="progressbar"
                                            style="width: {{ warehouse_stats[warehouse.id]['usage_percentage'] }}%"
                                            aria-valuenow="{{ warehouse_stats[warehouse.id]['usage_percentage'] }}"
                                            aria-valuemin="0"
                                            aria-valuemax="100">
                                        </div>
                                    </div>
                                    {% if warehouse_stats[warehouse.id]['is_at_capacity'] %}
                                    <div class="alert alert-danger mt-2 py-1 px-2 small">
                                        <i class="fas fa-exclamation-triangle"></i> المستودع ممتلئ
                                    </div>
                                    {% endif %}
                                </div>
                                {% endif %}

                                <div class="mb-3">
                                    <h6>حالات الأسلحة</h6>
                                    <div class="row text-center">
                                        <div class="col">
                                            <div class="badge badge-success">{{
                                                warehouse_stats[warehouse.id]['weapon_status']['active'] }}</div>
                                            <small class="d-block">نشط</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-warning">{{
                                                warehouse_stats[warehouse.id]['weapon_status']['leave'] }}</div>
                                            <small class="d-block">إجازة</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-mission">{{
                                                warehouse_stats[warehouse.id]['weapon_status']['mission'] }}</div>
                                            <small class="d-block">مهمة</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-maintenance">{{
                                                warehouse_stats[warehouse.id]['weapon_status']['maintenance'] }}</div>
                                            <small class="d-block">صيانة</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-danger">{{
                                                warehouse_stats[warehouse.id]['weapon_status']['cycle'] }}</div>
                                            <small class="d-block">دورة</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-dark">{{
                                                warehouse_stats[warehouse.id]['weapon_status']['vacant'] }}</div>
                                            <small class="d-block">شاغر</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-recipient">{{
                                                warehouse_stats[warehouse.id]['weapon_status']['recipient']|default(0) }}</div>
                                            <small class="d-block">مستلم</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-shooting">{{
                                                warehouse_stats[warehouse.id]['weapon_status']['shooting']|default(0) }}</div>
                                            <small class="d-block">رماية</small>
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-3" style="border-top: 1px dashed #ccc;">

                                <div class="mb-3">
                                    <h6>حالات الأفراد</h6>
                                    <div class="row text-center">
                                        <div class="col">
                                            <div class="badge badge-success">{{
                                                warehouse_stats[warehouse.id]['personnel_status']['active']|default(0) }}</div>
                                            <small class="d-block">نشط</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-warning">{{
                                                warehouse_stats[warehouse.id]['personnel_status']['leave']|default(0) }}</div>
                                            <small class="d-block">إجازة</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-mission">{{
                                                warehouse_stats[warehouse.id]['personnel_status']['mission']|default(0) }}</div>
                                            <small class="d-block">مهمة</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-danger">{{
                                                warehouse_stats[warehouse.id]['personnel_status']['cycle']|default(0) }}</div>
                                            <small class="d-block">دورة</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-recipient">{{
                                                warehouse_stats[warehouse.id]['personnel_status']['recipient']|default(0) }}</div>
                                            <small class="d-block">مستلم</small>
                                        </div>
                                        <div class="col">
                                            <div class="badge badge-shooting">{{
                                                warehouse_stats[warehouse.id]['personnel_status']['shooting']|default(0) }}</div>
                                            <small class="d-block">رماية</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mt-3">
                                    <a href="{{ url_for('warehouse.warehouse_detail', warehouse_id=warehouse.id) }}"
                                        class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>النشاط الأخير</h5>
                <div>
                    <a href="{{ url_for('activities.index') }}" class="btn btn-sm btn-outline-primary me-2">
                        <i class="fas fa-list"></i> عرض الكل
                    </a>
                    <button class="panel-toggle-btn" data-target="recent-activity-body" title="طي">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" id="recent-activity-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>العملية</th>
                                <th>الوصف</th>
                                <th>المستودع</th>
                                <th>بواسطة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in recent_activities %}
                            <tr>
                                <td>
                                    {% if 'إضافة' in log.action %}
                                    <span class="badge bg-success">{{ log.action }}</span>
                                    {% elif 'تعديل' in log.action %}
                                    <span class="badge bg-primary">{{ log.action }}</span>
                                    {% elif 'حذف' in log.action %}
                                    <span class="badge bg-danger">{{ log.action }}</span>
                                    {% elif 'نقل' in log.action %}
                                    <span class="badge bg-info">{{ log.action }}</span>
                                    {% elif 'تسليم' in log.action %}
                                    <span class="badge bg-warning">{{ log.action }}</span>
                                    {% elif 'استلام' in log.action %}
                                    <span class="badge bg-success">{{ log.action }}</span>
                                    {% elif 'تغيير حالة' in log.action %}
                                    <span class="badge bg-primary">{{ log.action }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ log.action }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ log.description }}</td>
                                <td>
                                    {% if 'جهاز' in log.action or not log.warehouse %}
                                    <span class="text-muted">-</span>
                                    {% else %}
                                    {{ log.warehouse.name }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if log.user %}
                                    {{ log.user.full_name or log.user.username }}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>{{ format_datetime_12h(log.timestamp) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5>إحصائيات عامة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 250px;">
                    <canvas id="warehouseComparisonChart"></canvas>
                </div>
                <hr>
                <div class="row text-center mt-3">
                    <div class="col-6">
                        <h3>{{ total_weapons }}</h3>
                        <p class="text-muted">إجمالي الأسلحة</p>
                    </div>
                    <div class="col-6">
                        <h3>{{ total_personnel }}</h3>
                        <p class="text-muted">إجمالي الأفراد</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>المعاملات الأخيرة</h5>
                <button class="panel-toggle-btn" data-target="recent-transactions-body" title="طي">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
            <div class="card-body p-0" id="recent-transactions-body">
                <ul class="list-group list-group-flush">
                    {% for warehouse in warehouses %}
                    {% for transaction in warehouse_stats[warehouse.id]['recent_transactions'][:3] %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            {% if transaction.transaction_type == 'checkout' %}
                            <span class="badge badge-danger">تسليم</span>
                            {% elif transaction.transaction_type == 'return' %}
                            <span class="badge badge-success">استلام</span>
                            {% elif transaction.transaction_type == 'transfer' %}
                            <span class="badge badge-primary">نقل</span>
                            {% endif %}
                            <small class="text-muted mr-2">{{ warehouse.name }}</small>
                        </div>
                        <small class="text-muted">{{ format_datetime_12h(transaction.timestamp) }}</small>
                    </li>
                    {% endfor %}
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Hidden chart data for JavaScript processing -->
<div class="d-none">
    {% for warehouse in warehouses %}
    <div id="warehouse-data-{{ warehouse.id }}"
        data-weapon-status='{{ warehouse_stats[warehouse.id]["weapon_status"]|tojson }}'
        data-personnel-status='{{ warehouse_stats[warehouse.id]["personnel_status"]|tojson }}'>
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // حل مشكلة قائمة شاشات العرض المنسدلة
        const displayScreensButton = document.getElementById('displayScreensButton');
        const displayScreensDropdown = document.getElementById('displayScreensDropdown');

        if (displayScreensButton && displayScreensDropdown) {
            // تبديل القائمة عند النقر على الزر
            displayScreensButton.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();
                displayScreensDropdown.classList.toggle('show');
            });

            // إخفاء القائمة عند النقر خارجها
            document.addEventListener('click', function (e) {
                if (!displayScreensButton.contains(e.target) && !displayScreensDropdown.contains(e.target)) {
                    displayScreensDropdown.classList.remove('show');
                }
            });
            
            // تم إزالة معالج الأحداث المضاعف - سيتم التعامل مع النقرات في main.js
        }

        // Set up data for warehouse comparison chart
        const warehouseLabels = [
            {% for warehouse in warehouses %}
                "{{ warehouse.name }}"{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        const weaponsData = [
					{% for warehouse in warehouses %}
						{{ warehouse_stats[warehouse.id]['weapons_count'] }},
					{% endfor %}
        ];

        const personnelData = [
					{% for warehouse in warehouses %}
						{{ warehouse_stats[warehouse.id]['personnel_count'] }},
					{% endfor %}
        ];

        // لا نعرض الأجهزة في الرسم البياني لأنها أصبحت مستقلة عن المستودعات

				// Setup comparison chart data
				window.console.log(weaponsData, personnelData)
				const chartData = {
						labels: warehouseLabels,
						datasets: [
								{
										label: 'الأسلحة',
										data: weaponsData,
										backgroundColor: 'rgba(0, 123, 255, 0.7)'
								},
								{
										label: 'الأفراد',
										data: personnelData,
										backgroundColor: 'rgba(40, 167, 69, 0.7)'
								}
						]
				};
				// Initialize the comparison chart
				const comparisonChart = document.getElementById('warehouseComparisonChart');

				if (comparisonChart) {
						new Chart(comparisonChart, {
								type: 'bar',
								data: chartData,
								options: {
										responsive: true,
										maintainAspectRatio: false,
										scales: {
												y: {
														beginAtZero: true,
														ticks: {
																font: {
																		family: 'Cairo, Tajawal, sans-serif'
																},
																color: document.body.classList.contains('light-theme') ? '#212529' : '#f0f0f0'
														},
														grid: {
																color: document.body.classList.contains('light-theme') ? 'rgba(0,0,0,0.1)' : 'rgba(255,255,255,0.1)'
														}
												},
												x: {
														ticks: {
																font: {
																		family: 'Cairo, Tajawal, sans-serif'
																},
																color: document.body.classList.contains('light-theme') ? '#212529' : '#f0f0f0'
														},
														grid: {
																color: document.body.classList.contains('light-theme') ? 'rgba(0,0,0,0.1)' : 'rgba(255,255,255,0.1)'
														}
												}
										},
										plugins: {
												legend: {
														position: 'top',
														labels: {
																font: {
																		family: 'Cairo, Tajawal, sans-serif'
																},
																color: document.body.classList.contains('light-theme') ? '#212529' : '#f0f0f0'
														}
												}
										}
								}
						});
				}
    });
</script>
{% endblock %}
