{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>تعديل المستخدم: {{ user.username }}</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة المستخدمين
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-user-edit"></i> تعديل بيانات المستخدم</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('auth.edit_user', user_id=user.id) }}">
            {{ form.hidden_tag() }}
            <div class="form-group row">
                <label for="username" class="col-sm-3 col-form-label">اسم المستخدم</label>
                <div class="col-sm-9">
                    {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                    {% for error in form.username.errors %}
                    <div class="invalid-feedback">
                        {{ error }}
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="form-group row">
                <label for="full_name" class="col-sm-3 col-form-label">الاسم الكامل</label>
                <div class="col-sm-9">
                    {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                    {% for error in form.full_name.errors %}
                    <div class="invalid-feedback">
                        {{ error }}
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="form-group row">
                <label for="email" class="col-sm-3 col-form-label">البريد الإلكتروني</label>
                <div class="col-sm-9">
                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                    {% for error in form.email.errors %}
                    <div class="invalid-feedback">
                        {{ error }}
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="form-group row">
                <label for="role" class="col-sm-3 col-form-label">الدور</label>
                <div class="col-sm-9">
                    {{ form.role(class="form-control" + (" is-invalid" if form.role.errors else "")) }}
                    {% for error in form.role.errors %}
                    <div class="invalid-feedback">
                        {{ error }}
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="form-group row warehouse-field" id="warehouse-field">
                <label for="warehouses" class="col-sm-3 col-form-label">المستودع</label>
                <div class="col-sm-9">
                    {{ form.warehouses(class="form-control" + (" is-invalid" if form.warehouses.errors else "")) }}
                    {% for error in form.warehouses.errors %}
                    <div class="invalid-feedback">
                        {{ error }}
                    </div>
                    {% endfor %}
                    <small class="form-text text-muted admin-note" style="display: none;">مدير النظام لديه صلاحية على جميع المستودعات تلقائياً</small>
                </div>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const roleSelect = document.getElementById('role');
                    const warehouseField = document.getElementById('warehouse-field');
                    const adminNote = document.querySelector('.admin-note');

                    // إخفاء خيار "مدير نظام" إذا كان المستخدم الحالي هو مدير مستودعات
                    {% if current_user.is_warehouse_manager and not current_user.is_admin_role %}
                    // إزالة خيار مدير النظام من القائمة
                    for (let i = 0; i < roleSelect.options.length; i++) {
                        if (roleSelect.options[i].value === 'admin') {
                            roleSelect.remove(i);
                            break;
                        }
                    }
                    {% endif %}

                    function updateWarehouseField() {
                        if (roleSelect.value === 'admin' || roleSelect.value === 'warehouse_manager') {
                            // بدلاً من تعطيل الحقل، نقوم بإخفائه ولكن نترك قيمته الافتراضية
                            // هذا يضمن إرسال قيمة صالحة مع النموذج
                            warehouseField.style.display = 'none';
                            adminNote.style.display = 'block';
                            if (roleSelect.value === 'warehouse_manager') {
                                adminNote.textContent = 'مدير المستودعات لديه صلاحية على جميع المستودعات تلقائياً';
                            } else {
                                adminNote.textContent = 'مدير النظام لديه صلاحية على جميع المستودعات تلقائياً';
                            }
                        } else if (roleSelect.value === 'company_duty') {
                            // إخفاء حقل المستودع لمناوب السرية
                            warehouseField.style.display = 'none';
                            adminNote.style.display = 'block';
                            adminNote.textContent = 'مناوب السرية لا يحتاج إلى مستودعات (يعمل على كشف الاستلامات وإدارة المواقع)';
                        } else {
                            warehouseField.style.display = 'block';
                            warehouseField.querySelector('select').disabled = false;
                            adminNote.style.display = 'none';
                        }
                    }

                    // تنفيذ عند تحميل الصفحة
                    updateWarehouseField();

                    // تنفيذ عند تغيير الدور
                    roleSelect.addEventListener('change', updateWarehouseField);
                });
            </script>
            <div class="form-group row">
                <div class="col-sm-9 offset-sm-3">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">إلغاء</a>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}
