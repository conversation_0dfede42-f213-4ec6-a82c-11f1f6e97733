{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            تعديل الجرد
        </h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('inventory.audit_details', audit_id=audit.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل الجرد
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit"></i> تعديل بيانات الجرد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="form-group">
                        {{ form.warehouse_id.label(class="form-label") }}
                        {{ form.warehouse_id(class="form-control") }}
                        {% if form.warehouse_id.errors %}
                            <div class="text-danger">
                                {% for error in form.warehouse_id.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3") }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3") }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                        <a href="{{ url_for('inventory.audit_details', audit_id=audit.id) }}" class="btn btn-secondary ml-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> معلومات الجرد
                </h5>
            </div>
            <div class="card-body">
                <p><strong>تاريخ الإنشاء:</strong><br>
                {{ audit.audit_date.strftime('%Y-%m-%d %H:%M') }}</p>
                
                <p><strong>الحالة:</strong><br>
                {% if audit.status == 'in-progress' %}
                    <span class="badge badge-warning">قيد التنفيذ</span>
                {% elif audit.status == 'completed' %}
                    <span class="badge badge-success">مكتمل</span>
                {% elif audit.status == 'cancelled' %}
                    <span class="badge badge-danger">ملغي</span>
                {% endif %}
                </p>
                
                <p><strong>المنشئ:</strong><br>
                {{ audit.user.full_name or audit.user.username }}</p>
                
                {% if audit.completed_at %}
                <p><strong>تاريخ الإكمال:</strong><br>
                {{ audit.completed_at.strftime('%Y-%m-%d %H:%M') }}</p>
                {% endif %}
                
                {% if audit.next_audit_date %}
                <p><strong>الجرد القادم:</strong><br>
                {{ audit.next_audit_date.strftime('%Y-%m-%d') }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle"></i> تنبيه
                </h5>
            </div>
            <div class="card-body">
                <p class="text-warning">
                    <i class="fas fa-info-circle"></i>
                    يمكن تعديل الجرد فقط إذا كان في حالة "قيد التنفيذ".
                </p>
                <p class="text-muted">
                    تعديل المستودع سيؤثر على الأسلحة المتاحة للجرد.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
