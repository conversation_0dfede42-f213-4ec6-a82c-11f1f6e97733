/* تحسين تنسيق جدول الأسلحة */

/* تقليل المساحات الفارغة في الجدول */
.table {
    width: 100%;
    margin-bottom: 1rem;
    border-collapse: collapse;
}

/* تقليل حجم الخلايا */
.table td, .table th {
    padding: 0.5rem !important;
    vertical-align: middle !important;
    border-top: 1px solid #dee2e6;
    font-size: 0.9rem !important;
}

/* تحسين مظهر رأس الجدول */
.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

/* تحسين مظهر الصفوف */
.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* تحسين عرض الأعمدة */
.table th:nth-child(1), .table td:nth-child(1) { /* رقم الحفظ */
    width: 8%;
}
.table th:nth-child(2), .table td:nth-child(2) { /* الرقم التسلسلي */
    width: 12%;
}
.table th:nth-child(3), .table td:nth-child(3) { /* اسم السلاح */
    width: 15%;
}
.table th:nth-child(4), .table td:nth-child(4) { /* رقم السلاح */
    width: 20%;
}
.table th:nth-child(5), .table td:nth-child(5) { /* الحالة */
    width: 8%;
}
.table th:nth-child(6), .table td:nth-child(6) { /* المستودع */
    width: 12%;
}
.table th:nth-child(7), .table td:nth-child(7) { /* الإجراءات */
    width: 15%;
    text-align: center;
}

/* تحسين مظهر الأزرار في عمود الإجراءات */
.btn-block {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 5px !important;
}

/* تحسين حجم الأزرار */
.btn-sm {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.75rem !important;
    line-height: 1.5 !important;
    border-radius: 0.2rem !important;
}

/* تحسين مظهر الشارات */
.badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

/* تحسين مظهر القائمة المنسدلة */
.dropdown-menu {
    position: absolute !important;
    z-index: 9999 !important;
    display: none;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0;
    font-size: 0.875rem;
    color: #212529;
    text-align: right;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

/* تأكيد أن القائمة المنسدلة تظهر خارج الجدول */
.dropdown-menu.show {
    display: block !important;
}

/* تحسين مظهر عناصر القائمة المنسدلة */
.dropdown-item {
    display: flex !important;
    align-items: center !important;
    width: 100%;
    padding: 0.5rem 1rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

.dropdown-item i {
    margin-left: 0.5rem;
    width: 1rem;
    text-align: center;
}

.dropdown-item:hover, .dropdown-item:focus {
    color: #16181b;
    text-decoration: none;
    background-color: #f8f9fa;
}

/* تحسين مظهر الفاصل في القائمة المنسدلة */
.dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #e9ecef;
}

/* تحسين مظهر زر البحث */
.form-control {
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* تحسين مظهر زر البحث */
.btn-outline-primary {
    color: #007bff;
    background-color: transparent;
    background-image: none;
    border-color: #007bff;
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

/* تحسين مظهر الصفحة بشكل عام */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-body {
    padding: 1rem;
}

/* تحسين مظهر زر إضافة سلاح جديد */
.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc;
}

/* تحسين مظهر أيقونات الأزرار */
.fas {
    margin-left: 0.25rem;
}

/* تحسين مظهر الصفحة على الشاشات الصغيرة */
@media (max-width: 768px) {
    .table {
        font-size: 0.8rem;
    }

    .table td, .table th {
        padding: 0.3rem !important;
    }

    .btn-sm {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.7rem !important;
    }
}
