from functools import wraps
from flask import flash, redirect, url_for
from flask_login import current_user

def admin_required(f):
    """
    مزخرف للتحقق من أن المستخدم هو مدير نظام
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_admin_role:
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('warehouse.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def warehouse_manager_required(f):
    """
    مزخرف للتحقق من أن المستخدم هو مدير مستودع أو مدير نظام
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not (current_user.is_admin_role or current_user.is_warehouse_manager):
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('warehouse.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def inventory_manager_required(f):
    """
    مزخرف للتحقق من أن المستخدم هو مسؤول مخزون أو مدير مستودع أو مدير نظام
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('warehouse.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def check_warehouse_access(warehouse_id):
    """
    التحقق من أن المستخدم لديه صلاحية على المستودع المحدد
    """
    # مدير النظام ومدير المستودعات لديهم صلاحية على جميع المستودعات
    if current_user.is_admin_role or current_user.is_warehouse_manager or current_user.role == 'admin' or current_user.role == 'مدير نظام' or current_user.role == 'مدير المستودعات':
        return True

    # التحقق من أن المستخدم لديه صلاحية على المستودع المحدد
    for warehouse in current_user.warehouses:
        if warehouse.id == warehouse_id:
            return True

    return False

def can_view(warehouse_id=None):
    """
    التحقق من صلاحية المستخدم للاطلاع على البيانات
    """
    # التحقق من صلاحية المستخدم على المستودع إذا تم تحديده
    if warehouse_id and not check_warehouse_access(warehouse_id):
        return False

    # جميع الأدوار يمكنها الاطلاع
    return True

def can_add(warehouse_id=None):
    """
    التحقق من صلاحية المستخدم للإضافة
    """
    # التحقق من صلاحية المستخدم على المستودع إذا تم تحديده
    if warehouse_id and not check_warehouse_access(warehouse_id):
        return False

    # المراقب لا يمكنه الإضافة
    if current_user.is_monitor:
        return False

    # مدير النظام ومدير المستودع ومسؤول المخزون يمكنهم الإضافة
    return True

def can_edit(warehouse_id=None):
    """
    التحقق من صلاحية المستخدم للتعديل
    """
    # التحقق من صلاحية المستخدم على المستودع إذا تم تحديده
    if warehouse_id and not check_warehouse_access(warehouse_id):
        return False

    # المراقب لا يمكنه التعديل
    if current_user.is_monitor:
        return False

    # مدير النظام ومدير المستودع يمكنهم التعديل
    if current_user.is_admin_role or current_user.is_warehouse_manager:
        return True

    # مسؤول المخزون يمكنه تعديل المخزون فقط (سيتم التحقق في المسارات)
    return current_user.is_inventory_manager

def can_delete(warehouse_id=None):
    """
    التحقق من صلاحية المستخدم للحذف
    """
    # التحقق من صلاحية المستخدم على المستودع إذا تم تحديده
    if warehouse_id and not check_warehouse_access(warehouse_id):
        return False

    # المراقب لا يمكنه الحذف
    if current_user.is_monitor:
        return False

    # مدير النظام ومدير المستودع ومسؤول المخزون يمكنهم الحذف
    return current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager
