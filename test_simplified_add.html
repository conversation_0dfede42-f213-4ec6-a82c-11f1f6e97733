<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإضافة المبسطة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .fix-section {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(40, 167, 69, 0.8); 
            border-left-color: #28a745;
        }
        .error { 
            background: rgba(220, 53, 69, 0.8); 
            border-left-color: #dc3545;
        }
        .warning { 
            background: rgba(255, 193, 7, 0.8); 
            color: #000; 
            border-left-color: #ffc107;
        }
        .info { 
            background: rgba(23, 162, 184, 0.8); 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }
        .log {
            background: rgba(0,0,0,0.4);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .code-block {
            background: rgba(0,0,0,0.4);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            border-left: 3px solid #28a745;
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .emoji {
            font-size: 1.5em;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 إصلاح دوال الإضافة - نسخة مبسطة</h1>
        <p>تم تبسيط دوال إضافة الصفوف والأعمدة لحل مشكلة عدم العمل</p>
        
        <div class="fix-section">
            <h3>✅ الإصلاحات المطبقة</h3>
            <div class="status success">
                <strong>تم تبسيط دوال الإضافة:</strong>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li>🔧 إزالة دالة saveCurrentTableData المعقدة</li>
                    <li>🔧 إزالة دالة restoreTableDataSimple المعقدة</li>
                    <li>🔧 إضافة مباشرة للمصفوفات</li>
                    <li>🔧 إعادة إنشاء الجدول فوراً</li>
                    <li>🔧 حفظ في قاعدة البيانات بعد 500ms</li>
                </ul>
            </div>
        </div>
        
        <div class="fix-section">
            <h3>🔧 التغييرات في الكود</h3>
            <div class="code-block">
<strong>دالة addRowAfter - قبل الإصلاح:</strong>
❌ saveCurrentTableData(); // معقدة وقد تفشل
❌ restoreTableDataSimple(); // معقدة ومتأخرة
❌ setTimeout مضاعف ومعقد

<strong>دالة addRowAfter - بعد الإصلاح:</strong>
✅ إضافة مباشرة: dutyData.rows.splice(rowIndex + 1, 0, newRow);
✅ إعادة إنشاء فورية: generateTable();
✅ حفظ بسيط: saveDutyDataToServer();

<strong>دالة addColumnAfter - قبل الإصلاح:</strong>
❌ saveCurrentTableData(); // معقدة وقد تفشل
❌ restoreTableDataWithNewColumn(); // معقدة ومتأخرة
❌ setTimeout مضاعف ومعقد

<strong>دالة addColumnAfter - بعد الإصلاح:</strong>
✅ إضافة مباشرة للرأس: dutyData.headers.splice(columnIndex + 1, 0, 'عمود جديد');
✅ إضافة مباشرة للصفوف: row.splice(columnIndex + 1, 0, '');
✅ إعادة إنشاء فورية: generateTable();
✅ حفظ بسيط: saveDutyDataToServer();
            </div>
        </div>
        
        <div class="fix-section">
            <h3>🧪 اختبار الإصلاح</h3>
            <div>
                <button onclick="openDuties()" class="primary">📋 فتح كشف الواجبات</button>
                <button onclick="testAddRow()">➕ اختبار إضافة صف</button>
                <button onclick="testAddColumn()">📊 اختبار إضافة عمود</button>
                <button onclick="testBoth()">🔄 اختبار كلاهما</button>
            </div>
        </div>
        
        <div class="fix-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testStatus">
                <div class="status info">⏳ جاري انتظار بدء الاختبار...</div>
            </div>
        </div>
        
        <div class="fix-section">
            <h3>📋 سجل الاختبار</h3>
            <div id="testLog" class="log">
                جاري تحميل أدوات الاختبار المبسطة...
            </div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
        </div>
        
        <div class="fix-section">
            <h3>📖 تعليمات الاختبار</h3>
            <div class="code-block">
<strong>خطوات الاختبار:</strong>

1. افتح صفحة كشف الواجبات
2. افتح وحدة التحكم (F12) لمراقبة الرسائل
3. ابحث عن أيقونة + بجانب رقم أي صف
4. اضغط على أيقونة + لإضافة صف جديد
5. ابحث عن أيقونة + في رأس أي عمود
6. اضغط على أيقونة + لإضافة عمود جديد

<strong>النتيجة المتوقعة:</strong>
✅ رسالة "🔄 إضافة صف جديد..." في وحدة التحكم
✅ رسالة "➕ تم إضافة صف جديد في الموضع X"
✅ رسالة "🔄 تم إعادة إنشاء الجدول"
✅ رسالة "✅ تم إضافة الصف وحفظه في قاعدة البيانات"
✅ ظهور صف أو عمود جديد في الجدول

<strong>إذا لم يعمل:</strong>
❌ تأكد من وجود الأزرار (أيقونة +)
❌ تحقق من وجود أخطاء في وحدة التحكم
❌ جرب تحديث الصفحة والمحاولة مرة أخرى
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = `<div style="color: ${colors[type]}; margin: 3px 0; padding: 8px; border-left: 3px solid ${colors[type]}; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += entry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('testStatus');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function openDuties() {
            window.open('/duties/', '_blank');
            log('🔗 تم فتح صفحة كشف الواجبات', 'info');
            updateStatus('تم فتح صفحة كشف الواجبات - ابدأ الاختبار', 'info');
        }
        
        function testAddRow() {
            log('🧪 تعليمات اختبار إضافة الصف:', 'info');
            log('1. في صفحة كشف الواجبات، ابحث عن أيقونة + بجانب رقم أي صف', 'info');
            log('2. اضغط على أيقونة + لإضافة صف جديد', 'info');
            log('3. راقب وحدة التحكم للرسائل التالية:', 'info');
            log('   - "🔄 إضافة صف جديد..."', 'success');
            log('   - "➕ تم إضافة صف جديد في الموضع X"', 'success');
            log('   - "🔄 تم إعادة إنشاء الجدول"', 'success');
            log('   - "✅ تم إضافة الصف وحفظه في قاعدة البيانات"', 'success');
            updateStatus('جاري اختبار إضافة الصف...', 'warning');
        }
        
        function testAddColumn() {
            log('🧪 تعليمات اختبار إضافة العمود:', 'info');
            log('1. في صفحة كشف الواجبات، ابحث عن أيقونة + في رأس أي عمود', 'info');
            log('2. اضغط على أيقونة + لإضافة عمود جديد', 'info');
            log('3. راقب وحدة التحكم للرسائل التالية:', 'info');
            log('   - "🔄 إضافة عمود جديد..."', 'success');
            log('   - "➕ تم إضافة عمود جديد في الموضع X"', 'success');
            log('   - "🔄 تم إعادة إنشاء الجدول"', 'success');
            log('   - "✅ تم إضافة العمود وحفظه في قاعدة البيانات"', 'success');
            updateStatus('جاري اختبار إضافة العمود...', 'warning');
        }
        
        function testBoth() {
            log('🧪 اختبار شامل لإضافة الصفوف والأعمدة:', 'info');
            updateStatus('جاري الاختبار الشامل...', 'info');
            
            const steps = [
                'افتح صفحة كشف الواجبات',
                'افتح وحدة التحكم (F12)',
                'اضغط على أيقونة + بجانب صف لإضافة صف جديد',
                'تأكد من ظهور الصف الجديد',
                'اضغط على أيقونة + في رأس عمود لإضافة عمود جديد',
                'تأكد من ظهور العمود الجديد',
                'تحقق من عدم وجود أخطاء في وحدة التحكم'
            ];
            
            steps.forEach((step, index) => {
                setTimeout(() => {
                    log(`${index + 1}. ${step}`, 'info');
                    
                    if (index === steps.length - 1) {
                        setTimeout(() => {
                            log('✅ تم إكمال الاختبار الشامل', 'success');
                            updateStatus('تم إكمال الاختبار الشامل', 'success');
                        }, 1000);
                    }
                }, (index + 1) * 1500);
            });
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('🧹 تم مسح السجل', 'info');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل أداة اختبار الإضافة المبسطة', 'success');
            updateStatus('أداة الاختبار جاهزة', 'info');
            
            log('🔧 تم تبسيط دوال الإضافة:', 'info');
            log('✅ إزالة دالة saveCurrentTableData المعقدة', 'success');
            log('✅ إزالة دالة restoreTableDataSimple المعقدة', 'success');
            log('✅ إضافة مباشرة للمصفوفات', 'success');
            log('✅ إعادة إنشاء فورية للجدول', 'success');
            log('✅ حفظ مبسط في قاعدة البيانات', 'success');
            
            log('📋 الآن يجب أن تعمل أزرار الإضافة بشكل صحيح!', 'info');
            log('🧪 ابدأ الاختبار للتأكد من عمل الإصلاح', 'warning');
        });
    </script>
</body>
</html>
