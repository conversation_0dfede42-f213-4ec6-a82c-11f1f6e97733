#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خدمة جدولة النسخ الاحتياطي
Backup Scheduler Service

هذه الخدمة تعمل في الخلفية لتشغيل النسخ الاحتياطية المجدولة تلقائياً.
يمكن تشغيلها كخدمة Windows أو كمهمة cron في Linux.
"""

import os
import sys
import time
import logging
import signal
import threading
from datetime import datetime, timedelta
from pathlib import Path

# إضافة المسار الحالي للبحث عن الوحدات
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BackupSchedulerService:
    """خدمة جدولة النسخ الاحتياطي"""
    
    def __init__(self, check_interval=60):
        """
        تهيئة الخدمة
        
        Args:
            check_interval (int): فترة فحص الجدولة بالثواني (افتراضي: 60 ثانية)
        """
        self.check_interval = check_interval
        self.running = False
        self.thread = None
        
        # إعداد معالج إشارات الإيقاف
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("🚀 تم تهيئة خدمة جدولة النسخ الاحتياطي")
    
    def _signal_handler(self, signum, frame):
        """معالج إشارات الإيقاف"""
        logger.info(f"📡 تم استلام إشارة الإيقاف: {signum}")
        self.stop()
    
    def start(self):
        """بدء تشغيل الخدمة"""
        if self.running:
            logger.warning("⚠️  الخدمة تعمل بالفعل")
            return
        
        logger.info("🟢 بدء تشغيل خدمة جدولة النسخ الاحتياطي")
        self.running = True
        
        # تشغيل الخدمة في thread منفصل
        self.thread = threading.Thread(target=self._run_service, daemon=True)
        self.thread.start()
        
        logger.info(f"⏰ الخدمة تفحص الجدولة كل {self.check_interval} ثانية")
    
    def stop(self):
        """إيقاف الخدمة"""
        if not self.running:
            logger.warning("⚠️  الخدمة متوقفة بالفعل")
            return
        
        logger.info("🔴 إيقاف خدمة جدولة النسخ الاحتياطي")
        self.running = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        logger.info("✅ تم إيقاف الخدمة بنجاح")
    
    def _run_service(self):
        """تشغيل الخدمة الرئيسية"""
        logger.info("🔄 بدء حلقة فحص الجدولة")
        
        while self.running:
            try:
                # فحص وتشغيل النسخ المجدولة
                self._check_and_run_scheduled_backups()
                
                # انتظار الفترة المحددة
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"❌ خطأ في حلقة الخدمة: {str(e)}")
                time.sleep(self.check_interval)
    
    def _check_and_run_scheduled_backups(self):
        """فحص وتشغيل النسخ الاحتياطية المجدولة"""
        try:
            # استيراد الوحدات المطلوبة
            from app import create_app
            from utils import run_scheduled_backups, get_due_backup_schedules
            
            # إنشاء سياق التطبيق
            app = create_app()
            with app.app_context():
                # فحص النسخ المستحقة
                due_schedules = get_due_backup_schedules()
                
                if not due_schedules:
                    logger.debug("📋 لا توجد نسخ احتياطية مستحقة للتشغيل")
                    return
                
                logger.info(f"📦 وجدت {len(due_schedules)} نسخة احتياطية مستحقة للتشغيل")
                
                # تشغيل النسخ المستحقة
                results = run_scheduled_backups()
                
                # تسجيل النتائج
                for result in results:
                    if result['success']:
                        logger.info(f"✅ نجح تشغيل النسخة الاحتياطية المجدولة {result['schedule_id']}: {result['backup_filename']}")
                    else:
                        logger.error(f"❌ فشل تشغيل النسخة الاحتياطية المجدولة {result['schedule_id']}: {result.get('error', 'خطأ غير معروف')}")
                
                logger.info(f"📊 تم تشغيل {len([r for r in results if r['success']])}/{len(results)} نسخة احتياطية بنجاح")
                
        except Exception as e:
            logger.error(f"❌ خطأ في فحص النسخ المجدولة: {str(e)}")
    
    def run_once(self):
        """تشغيل فحص واحد للنسخ المجدولة (للاختبار)"""
        logger.info("🧪 تشغيل فحص واحد للنسخ المجدولة")
        self._check_and_run_scheduled_backups()
    
    def get_status(self):
        """الحصول على حالة الخدمة"""
        return {
            'running': self.running,
            'check_interval': self.check_interval,
            'thread_alive': self.thread.is_alive() if self.thread else False
        }

def main():
    """الدالة الرئيسية"""
    import argparse
    
    parser = argparse.ArgumentParser(description='خدمة جدولة النسخ الاحتياطي')
    parser.add_argument('--interval', type=int, default=60, 
                       help='فترة فحص الجدولة بالثواني (افتراضي: 60)')
    parser.add_argument('--once', action='store_true', 
                       help='تشغيل فحص واحد فقط ثم الخروج')
    parser.add_argument('--daemon', action='store_true', 
                       help='تشغيل الخدمة كـ daemon')
    
    args = parser.parse_args()
    
    # إنشاء الخدمة
    service = BackupSchedulerService(check_interval=args.interval)
    
    if args.once:
        # تشغيل فحص واحد فقط
        logger.info("🔍 تشغيل فحص واحد للنسخ المجدولة")
        service.run_once()
        logger.info("✅ انتهى الفحص")
        return
    
    if args.daemon:
        # تشغيل كـ daemon (في الخلفية)
        logger.info("👻 تشغيل الخدمة كـ daemon")
        
        # إنشاء ملف PID
        pid_file = Path('scheduler_service.pid')
        with open(pid_file, 'w') as f:
            f.write(str(os.getpid()))
        
        try:
            # بدء الخدمة
            service.start()
            
            # انتظار إشارة الإيقاف
            while service.running:
                time.sleep(1)
                
        finally:
            # حذف ملف PID
            if pid_file.exists():
                pid_file.unlink()
    else:
        # تشغيل عادي
        logger.info("🖥️  تشغيل الخدمة في المقدمة")
        
        try:
            # بدء الخدمة
            service.start()
            
            # انتظار إشارة الإيقاف
            while service.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("⌨️  تم الضغط على Ctrl+C")
        finally:
            service.stop()

if __name__ == "__main__":
    main()
