{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>إضافة جهاز جديد</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('inventory.devices') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة الأجهزة
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-desktop"></i> بيانات الجهاز</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('inventory.create_device') }}">
            {{ form.hidden_tag() }}
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.name.label }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.type.label }}
                        {{ form.type(class="form-control") }}
                        {% if form.type.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.type.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.model.label }}
                        {{ form.model(class="form-control") }}
                        {% if form.model.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.model.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.serial_number.label }}
                        {{ form.serial_number(class="form-control") }}
                        {% if form.serial_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.serial_number.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.manufacturer.label }}
                        {{ form.manufacturer(class="form-control") }}
                        {% if form.manufacturer.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.manufacturer.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.status.label }}
                        {{ form.status(class="form-control") }}
                        {% if form.status.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.status.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.device_location.label }}
                        {{ form.device_location(class="form-control", placeholder="مثل: الإدارة، التموين، المكتب الرئيسي") }}
                        <small class="text-muted">أدخل موقع الجهاز</small>
                        {% if form.device_location.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.device_location.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <!-- تم إزالة حقل المستودع لأن الأجهزة أصبحت مستقلة عن المستودعات -->
            </div>
            <div class="form-group">
                {{ form.notes.label }}
                {{ form.notes(class="form-control", rows=4) }}
                {% if form.notes.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.notes.errors %}
                    <span>{{ error }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="form-group mt-4">
                {{ form.submit(class="btn btn-primary") }}
                <a href="{{ url_for('inventory.devices') }}" class="btn btn-outline-secondary mr-2">إلغاء</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إعادة تهيئة منتقي التاريخ بإعدادات خاصة لصفحة إضافة جهاز جديد
        $('.datepicker').datepicker('remove');
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            language: 'ar',
            rtl: true,
            clearBtn: true,
            todayBtn: 'linked',
            todayHighlight: true,
            zIndexOffset: 99999,
            container: 'body',
            orientation: 'auto',
            templates: {
                leftArrow: '<i class="fas fa-chevron-right"></i>',
                rightArrow: '<i class="fas fa-chevron-left"></i>'
            }
        });

        // إصلاح خاص لموضع منتقي التاريخ في صفحة إضافة جهاز جديد
        $('.datepicker').on('show', function(e) {
            const input = this;

            // تأخير قصير للتأكد من أن منتقي التاريخ قد تم إنشاؤه
            setTimeout(function() {
                const datepickerDropdown = document.querySelector('.datepicker-dropdown');
                if (!datepickerDropdown) return;

                // إضافة فئة خاصة لتحديد موضع منتقي التاريخ
                datepickerDropdown.classList.add('datepicker-position-fixed');

                // تعيين z-index عالي جدًا
                datepickerDropdown.style.zIndex = '99999';

                // وضع منتقي التاريخ في وسط الشاشة
                datepickerDropdown.style.position = 'fixed';
                datepickerDropdown.style.top = '50%';
                datepickerDropdown.style.left = '50%';
                datepickerDropdown.style.transform = 'translate(-50%, -50%)';

                // التأكد من أن منتقي التاريخ مرئي
                datepickerDropdown.style.display = 'block';
            }, 50);
        });
    });
</script>
{% endblock %}