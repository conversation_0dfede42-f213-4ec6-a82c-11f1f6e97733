{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>تعديل المستودع</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('warehouse.warehouse_detail', warehouse_id=warehouse.id) }}"
            class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل المستودع
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-warehouse"></i> بيانات المستودع</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('warehouse.edit_warehouse', warehouse_id=warehouse.id) }}">
            {{ form.hidden_tag() }}
            <div class="form-group">
                {{ form.name.label }}
                {{ form.name(class="form-control") }}
                {% if form.name.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.name.errors %}
                    <span>{{ error }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="form-group">
                {{ form.location.label }}
                {{ form.location(class="form-control") }}
                {% if form.location.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.location.errors %}
                    <span>{{ error }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="form-group">
                {{ form.capacity.label }}
                {{ form.capacity(class="form-control") }}
                {% if form.capacity.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.capacity.errors %}
                    <span>{{ error }}</span>
                    {% endfor %}
                </div>
                {% endif %}
                <small class="form-text text-muted">أدخل الطاقة الاستيعابية القصوى للمستودع (عدد الأسلحة)</small>
            </div>
            <div class="form-group">
                {{ form.description.label }}
                {{ form.description(class="form-control", rows=4) }}
                {% if form.description.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.description.errors %}
                    <span>{{ error }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="form-group mt-4">
                {{ form.submit(class="btn btn-primary") }}
                <a href="{{ url_for('warehouse.warehouse_detail', warehouse_id=warehouse.id) }}"
                    class="btn btn-outline-secondary ml-2">إلغاء</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}