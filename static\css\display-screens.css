/* ===== Display Screens Stylesheet ===== */
/* Author: Military Warehouse Management System
   Description: Enhanced CSS for warehouse display screens
*/

/* ===== Variables ===== */
:root {
  /* Display Screen Colors */
  --display-bg: #1d1d1d;
  --display-secondary: #242424;
  --display-tertiary: #2d2d2d;
  --display-accent: #3b82f6;
  --display-accent-hover: #2563eb;
  --display-success: #28a745; /* لون أخضر لحالة نشط */
  --display-warning: #ffc107; /* لون أصفر لحالة إجازة */
  --display-danger: #dc3545; /* لون أحمر لحالة دورة */
  --display-info: #06b6d4;
  --display-mission: #fd7e14; /* لون برتقالي لحالة مهمة */
  --display-cycle: #dc3545; /* لون أحمر لحالة دورة */
  --display-recipient: #0d6efd; /* لون أزرق لحالة مستلم */
  --display-maintenance: #FAAFBE; /* لون وردي لحالة صيانة */
  --display-vacant: #6c757d; /* لون رمادي لحالة شاغر */
  --display-shooting: #C8BBBE; /* لون Lavender Blush3 لحالة رماية */
  --display-text: #f8fafc;
  --display-text-secondary: #94a3b8;
  --display-border: #475569;
  --display-shadow: rgba(0, 0, 0, 0.25);
  --display-gradient-start: #242424;
  --display-gradient-end: #1d1d1d;

  /* Light Mode Colors */
  --display-light-bg: #eef2f7;
  --display-light-secondary: #ffffff;
  --display-light-tertiary: #f0f2f5;
  --display-light-text: #1a1a1a;
  --display-light-text-secondary: #64748b;
  --display-light-border: #cbd5e1;
  --display-light-shadow: rgba(0, 0, 0, 0.1);
  --display-light-gradient-start: #ffffff;
  --display-light-gradient-end: #eef2f7;
}

/* ===== Main Display Container ===== */
.display-screen {
  background-color: var(--display-bg);
  min-height: 100vh;
  padding: 0;
  color: var(--display-text);
  font-family: 'Cairo', sans-serif;
  overflow: hidden;
}

body.light-theme .display-screen {
  background-color: var(--display-light-bg);
  color: var(--display-light-text);
}

/* ===== Header Section ===== */
.display-header {
  background: linear-gradient(to bottom, var(--display-gradient-start), var(--display-gradient-end));
  padding: 1.5rem;
  border-bottom: 3px solid var(--display-accent);
  box-shadow: 0 4px 12px var(--display-shadow);
  position: relative;
}

body.light-theme .display-header {
  background: linear-gradient(to bottom, var(--display-light-gradient-start), var(--display-light-gradient-end));
  border-bottom: 3px solid var(--display-accent);
  box-shadow: 0 4px 12px var(--display-light-shadow);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.logo {
  width: 80px;
  height: auto;
  filter: drop-shadow(0 4px 6px var(--display-shadow));
}

.logo-section h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--display-text);
  text-shadow: 0 2px 4px var(--display-shadow);
}

body.light-theme .logo-section h1 {
  color: var(--display-light-text);
  text-shadow: 0 2px 4px var(--display-light-shadow);
}

.time-section {
  text-align: left;
}

.current-time {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--display-accent);
  text-shadow: 0 2px 4px var(--display-shadow);
}

.current-date {
  font-size: 1.2rem;
  color: var(--display-text-secondary);
}

.status-banner {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem;
  border-radius: 10px;
  margin-top: 1rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px var(--display-shadow);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.1rem;
}

.status-item i {
  font-size: 1.5rem;
  color: var(--display-accent);
  filter: drop-shadow(0 2px 4px var(--display-shadow));
}

/* ===== Content Section ===== */
.display-content {
  padding: 1.5rem;
}

.stat-panel {
  background: var(--display-secondary);
  border-radius: 15px;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  box-shadow: 0 4px 12px var(--display-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-left: 5px solid var(--display-accent);
  height: 100%;
}

body.light-theme .stat-panel {
  background: var(--display-light-secondary);
  box-shadow: 0 4px 12px var(--display-light-shadow);
}

.weapons-panel {
  border-left-color: var(--display-success);
}

.personnel-panel {
  border-left-color: var(--display-info);
}

.transactions-panel {
  border-left-color: var(--display-warning);
}

.stat-panel:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px var(--display-shadow);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--display-border);
  padding-bottom: 1rem;
}

.panel-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
}

body.light-theme .panel-header h2 {
  color: var(--display-light-text);
}

.weapons-panel .panel-header h2 i {
  color: var(--display-success);
}

.personnel-panel .panel-header h2 i {
  color: var(--display-info);
}

.transactions-panel .panel-header h2 i {
  color: var(--display-warning);
}

.panel-header .count {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--display-text);
  text-shadow: 0 2px 4px var(--display-shadow);
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem 1rem;
  border-radius: 50px;
}

body.light-theme .panel-header .count {
  color: var(--display-light-text);
  text-shadow: 0 2px 4px var(--display-light-shadow);
  background: rgba(0, 0, 0, 0.05);
}

/* ===== Status Bars ===== */
.status-bars {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-bar .label {
  width: 100px;
  font-weight: 600;
  color: var(--display-text);
}

.status-bar .progress {
  flex-grow: 1;
  height: 12px;
  background-color: var(--display-tertiary);
  border-radius: 6px;
  overflow: hidden;
}

body.light-theme .status-bar .progress {
  background-color: var(--display-light-tertiary);
}

.status-bar .progress-bar {
  height: 100%;
  border-radius: 6px;
  transition: width 1s ease-in-out;
}

.progress-bar.bg-shooting {
  background-color: var(--display-shooting);
}

.status-bar .count {
  width: 60px;
  text-align: left;
  font-weight: 600;
  color: var(--display-text);
}

/* ===== Charts ===== */
.chart-container {
  height: 250px;
  margin-top: 1rem;
  position: relative;
}

/* ===== Transactions List ===== */
.transactions-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.transactions-list::-webkit-scrollbar {
  width: 6px;
}

.transactions-list::-webkit-scrollbar-track {
  background: var(--display-tertiary);
  border-radius: 3px;
}

.transactions-list::-webkit-scrollbar-thumb {
  background: var(--display-accent);
  border-radius: 3px;
}

.transaction-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 10px;
  background: var(--display-tertiary);
  margin-bottom: 0.75rem;
  transition: transform 0.2s ease, background 0.2s ease;
}

.transaction-item:hover {
  transform: translateX(-5px);
  background: rgba(59, 130, 246, 0.1);
}

.transaction-icon {
  background: var(--display-accent);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.transaction-icon i {
  color: white;
  font-size: 1.25rem;
}

.transaction-content {
  flex-grow: 1;
}

.transaction-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.transaction-details {
  color: var(--display-text-secondary);
  font-size: 0.9rem;
}

.no-data {
  text-align: center;
  padding: 2rem;
  color: var(--display-text-secondary);
  font-style: italic;
}

/* ===== Footer ===== */
.display-footer {
  background: linear-gradient(to top, var(--display-gradient-start), var(--display-gradient-end));
  padding: 1rem;
  text-align: center;
  border-top: 1px solid var(--display-border);
  position: absolute;
  bottom: 0;
  width: 100%;
}

.footer-info {
  display: flex;
  justify-content: space-between;
  color: var(--display-text-secondary);
  font-size: 0.9rem;
}

/* ===== Animations ===== */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 992px) {
  .logo-section h1 {
    font-size: 2rem;
  }

  .current-time {
    font-size: 2rem;
  }

  .status-item {
    font-size: 1rem;
  }

  .panel-header h2 {
    font-size: 1.5rem;
  }

  .panel-header .count {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .logo-section {
    flex-direction: column;
    text-align: center;
  }

  .time-section {
    text-align: center;
  }

  .status-banner {
    flex-direction: column;
    gap: 1rem;
  }

  .status-item {
    justify-content: center;
  }

  .status-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .status-bar .label {
    width: 100%;
  }

  .status-bar .count {
    width: 100%;
    text-align: right;
  }
}
