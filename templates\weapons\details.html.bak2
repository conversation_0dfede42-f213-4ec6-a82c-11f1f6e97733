{% extends 'base.html' %}

{% block title %}تفاصيل السلاح - {{ weapon.name }}{% endblock %}

{% block page_title %}
تفاصيل السلاح
<div class="float-right">
    <div class="btn-toolbar">
        <div class="btn-group mr-2">
            <a href="{{ url_for('weapons.edit', weapon_id=weapon.id) }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-edit"></i> تعديل
            </a>

            {% if current_user.is_admin %}
            <a href="#" class="btn btn-sm btn-outline-danger" onclick="alert('لم يتم تنفيذ هذه الوظيفة بعد'); return false;">
                <i class="fas fa-trash"></i> حذف
            </a>
            {% endif %}
        </div>

        <div class="btn-group mr-2">
            <button type="button" class="btn btn-sm btn-outline-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-qrcode"></i> الباركود / QR
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="{{ url_for('weapons.get_qr_code', weapon_id=weapon.id) }}">QR Code</a>
                <a class="dropdown-item" href="{{ url_for('weapons.barcode', weapon_id=weapon.id) }}">Barcode</a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{{ url_for('weapons.get_qr_code', weapon_id=weapon.id, download=1) }}">تنزيل QR Code</a>
                <a class="dropdown-item" href="{{ url_for('weapons.barcode', weapon_id=weapon.id, download=1) }}">تنزيل الباركود</a>
            </div>
        </div>

        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-tasks"></i> إدارة
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="#" onclick="$('#checkoutModal').modal('show'); return false;">تسليم السلاح</a>
                <a class="dropdown-item" href="#" onclick="$('#transferModal').modal('show'); return false;">نقل السلاح</a>
                <a class="dropdown-item" href="#" onclick="$('#maintenanceModal').modal('show'); return false;">إضافة سجل صيانة</a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='نشط') }}">تعيين كنشط</a>
                <a class="dropdown-item" href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='إجازة') }}">تعيين في إجازة</a>
                <a class="dropdown-item" href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='مهمة') }}">تعيين في مهمة</a>
                <a class="dropdown-item" href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='صيانة') }}">تعيين في صيانة</a>
                <a class="dropdown-item" href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='تالف') }}">تعيين كتالف</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header">
                <span class="badge badge-{{ weapon.status|status_color }}">{{ weapon.status }}</span>
                <span class="ml-2">{{ weapon.name }}</span>
                <small class="text-muted">{{ weapon.type }}</small>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <th style="width: 150px;">اسم السلاح</th>
                                <td>{{ weapon.name }}</td>
                            </tr>
                            <tr>
                                <th>رقم الحفظ</th>
                                <td>{{ weapon.weapon_number or 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <th>الرقم التسلسلي</th>
                                <td>{{ weapon.serial_number }}</td>
                            </tr>
                            <tr>
                                <th>النوع</th>
                                <td>{{ weapon.type }}</td>
                            </tr>
                            <tr>
                                <th>الحالة</th>
                                <td>
                                    <span class="badge badge-{{ weapon.status|status_color }}">{{ weapon.status }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th>المستودع</th>
                                <td>{{ weapon.warehouse.name }}</td>
                            </tr>
                            {% if weapon.condition %}
                            <tr>
                                <th>الحالة الفنية</th>
                                <td>{{ weapon.condition }}</td>
                            </tr>
                            {% endif %}
                            {% if weapon.notes %}
                            <tr>
                                <th>ملاحظات</th>
                                <td>{{ weapon.notes }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <div id="qrcode"></div>
                            <p class="mt-2">{{ weapon.serial_number }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions History Card -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-exchange-alt"></i> سجل المعاملات
            </div>
            <div class="card-body">
                {% if transactions %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الشخص</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>بواسطة</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span class="badge {% if transaction.transaction_type == 'checkout' %}badge-warning{% elif transaction.transaction_type == 'return' %}badge-success{% else %}badge-info{% endif %}">
                                        {% if transaction.transaction_type == 'checkout' %}
                                            صرف
                                        {% elif transaction.transaction_type == 'return' %}
                                            إعادة
                                        {% elif transaction.transaction_type == 'transfer' %}
                                            نقل
                                        {% endif %}
                                    </span>
                                </td>
                                <td>{{ transaction.personnel.name }}</td>
                                <td>{{ transaction.source_warehouse.name }}</td>
                                <td>{{ transaction.target_warehouse.name if transaction.target_warehouse else 'غير محدد' }}</td>
                                <td>{{ transaction.user.username }}</td>
                                <td>
                                    {% if transaction.transaction_type == 'checkout' and current_user.is_admin %}
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                data-toggle="modal"
                                                data-target="#returnModal{{ transaction.id }}">
                                            استلام
                                        </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد معاملات لهذا السلاح.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Maintenance History Card -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tools"></i> سجل الصيانة
            </div>
            <div class="card-body">
                {% if maintenance_records %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الحالة</th>
                                <th>الوصف</th>
                                <th>بواسطة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in maintenance_records %}
                            <tr>
                                <td>{{ record.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ record.maintenance_type }}</td>
                                <td>
                                    <span class="badge {% if record.status == 'ongoing' %}badge-warning{% elif record.status == 'completed' %}badge-success{% else %}badge-danger{% endif %}">
                                        {% if record.status == 'ongoing' %}
                                            جارية
                                        {% elif record.status == 'completed' %}
                                            مكتملة
                                        {% elif record.status == 'cancelled' %}
                                            ملغاة
                                        {% endif %}
                                    </span>
                                </td>
                                <td>{{ record.description }}</td>
                                <td>{{ record.user.username }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد سجلات صيانة لهذا السلاح.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Checkout Modal -->
<div class="modal fade" id="checkoutModal" tabindex="-1" aria-labelledby="checkoutModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('weapons.checkout', weapon_id=weapon.id) }}">
                {{ checkout_form.hidden_tag() }}
                <div class="modal-header">
                    <h5 class="modal-title" id="checkoutModalLabel">تسليم السلاح</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="personnel_id" class="form-label">{{ checkout_form.personnel_id.label }}</label>
                        {{ checkout_form.personnel_id(class="form-control") }}
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">{{ checkout_form.notes.label }}</label>
                        {{ checkout_form.notes(class="form-control") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    {{ checkout_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div class="modal fade" id="transferModal" tabindex="-1" aria-labelledby="transferModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('weapons.transfer', weapon_id=weapon.id) }}">
                {{ transfer_form.hidden_tag() }}
                <div class="modal-header">
                    <h5 class="modal-title" id="transferModalLabel">نقل السلاح</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="target_warehouse_id" class="form-label">{{ transfer_form.target_warehouse_id.label }}</label>
                        {{ transfer_form.target_warehouse_id(class="form-control") }}
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">{{ transfer_form.notes.label }}</label>
                        {{ transfer_form.notes(class="form-control") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    {{ transfer_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Maintenance Modal -->
<div class="modal fade" id="maintenanceModal" tabindex="-1" aria-labelledby="maintenanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('weapons.add_maintenance', weapon_id=weapon.id) }}">
                {{ maintenance_form.hidden_tag() }}
                <div class="modal-header">
                    <h5 class="modal-title" id="maintenanceModalLabel">إضافة سجل صيانة</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="maintenance_type" class="form-label">{{ maintenance_form.maintenance_type.label }}</label>
                        {{ maintenance_form.maintenance_type(class="form-control") }}
                    </div>
                    <div class="form-group">
                        <label for="description" class="form-label">{{ maintenance_form.description.label }}</label>
                        {{ maintenance_form.description(class="form-control", rows="3") }}
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="start_date" class="form-label">{{ maintenance_form.start_date.label }}</label>
                                {{ maintenance_form.start_date(class="form-control datepicker") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="end_date" class="form-label">{{ maintenance_form.end_date.label }}</label>
                                {{ maintenance_form.end_date(class="form-control datepicker") }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status" class="form-label">{{ maintenance_form.status.label }}</label>
                                {{ maintenance_form.status(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="cost" class="form-label">{{ maintenance_form.cost.label }}</label>
                                {{ maintenance_form.cost(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">{{ maintenance_form.notes.label }}</label>
                        {{ maintenance_form.notes(class="form-control", rows="3") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    {{ maintenance_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Return Modals for each checkout transaction -->
{% for transaction in transactions %}
    {% if transaction.transaction_type == 'checkout' %}
    <div class="modal fade" id="returnModal{{ transaction.id }}" tabindex="-1" aria-labelledby="returnModalLabel{{ transaction.id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="{{ url_for('weapons.return_weapon', weapon_id=weapon.id, transaction_id=transaction.id) }}">
                    {{ return_form.hidden_tag() }}
                    <input type="hidden" name="transaction_id" value="{{ transaction.id }}">
                    <div class="modal-header">
                        <h5 class="modal-title" id="returnModalLabel{{ transaction.id }}">استلام السلاح من {{ transaction.personnel.name }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="condition" class="form-label">{{ return_form.condition.label }}</label>
                            {{ return_form.condition(class="form-control") }}
                        </div>
                        <div class="form-group">
                            <label for="notes" class="form-label">{{ return_form.notes.label }}</label>
                            {{ return_form.notes(class="form-control", rows="3") }}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                        {{ return_form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endif %}
{% endfor %}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Generate QR code
        var qrcode = new QRCode(document.getElementById("qrcode"), {
            text: "{{ weapon.serial_number }}",
            width: 128,
            height: 128,
            colorDark : "#000000",
            colorLight : "#ffffff",
            correctLevel : QRCode.CorrectLevel.H
        });
    });
</script>
{% endblock %}
