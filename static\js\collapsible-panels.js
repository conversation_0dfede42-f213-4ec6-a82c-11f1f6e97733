/**
 * Collapsible Panels JavaScript
 * يتعامل مع وظائف طي وفتح اللوحات في واجهة المستخدم
 */
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قصير لضمان تحميل جميع العناصر
    setTimeout(function() {
        initCollapsiblePanels();
    }, 100);
});

// تهيئة أزرار الطي
function initCollapsiblePanels() {
    // معالجة أزرار طي وفتح اللوحات
    const toggleButtons = document.querySelectorAll('.panel-toggle-btn');

    toggleButtons.forEach(function(button) {
        // إزالة مستمعات الأحداث السابقة لتجنب التكرار
        button.removeEventListener('click', handlePanelToggle);
        // إضافة مستمع حدث جديد
        button.addEventListener('click', handlePanelToggle);
    });

    // استعادة حالة الطي من التخزين المحلي عند تحميل الصفحة
    document.querySelectorAll('[id$="-body"]').forEach(function(panel) {
        const panelId = panel.id;
        const isCollapsed = localStorage.getItem(panelId + '-collapsed') === 'true';

        if (isCollapsed) {
            panel.style.display = 'none';

            // تحديث أيقونة الزر المرتبط
            const toggleButton = document.querySelector(`[data-target="${panelId}"]`);
            if (toggleButton) {
                toggleButton.innerHTML = '<i class="fas fa-chevron-down"></i>';
                toggleButton.setAttribute('title', 'فتح');
            }
        }
    });
}

// معالج حدث النقر على زر الطي
function handlePanelToggle() {
    // الحصول على الهدف من سمة data-target
    const targetId = this.getAttribute('data-target');
    const targetPanel = document.getElementById(targetId);

    if (targetPanel) {
        // تبديل حالة العرض
        if (targetPanel.style.display === 'none') {
            // فتح اللوحة
            targetPanel.style.display = 'block';
            // تغيير أيقونة الزر
            this.innerHTML = '<i class="fas fa-chevron-up"></i>';
            // تغيير العنوان التلميحي
            this.setAttribute('title', 'طي');
        } else {
            // طي اللوحة
            targetPanel.style.display = 'none';
            // تغيير أيقونة الزر
            this.innerHTML = '<i class="fas fa-chevron-down"></i>';
            // تغيير العنوان التلميحي
            this.setAttribute('title', 'فتح');
        }

        // حفظ الحالة في التخزين المحلي
        localStorage.setItem(targetId + '-collapsed', targetPanel.style.display === 'none');
    }
}
