<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاح السريع</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .fix-section {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(40, 167, 69, 0.8); 
            border-left-color: #28a745;
        }
        .error { 
            background: rgba(220, 53, 69, 0.8); 
            border-left-color: #dc3545;
        }
        .warning { 
            background: rgba(255, 193, 7, 0.8); 
            color: #000; 
            border-left-color: #ffc107;
        }
        .info { 
            background: rgba(23, 162, 184, 0.8); 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }
        .log {
            background: rgba(0,0,0,0.4);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .code-block {
            background: rgba(0,0,0,0.4);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            border-left: 3px solid #28a745;
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح سريع - خطأ المسارات</h1>
        <p>تم إصلاح خطأ المسارات في دالة حفظ البيانات</p>
        
        <div class="fix-section">
            <h3>❌ الخطأ الذي تم إصلاحه</h3>
            <div class="status error">
                <strong>خطأ:</strong> TypeError: Cannot set properties of undefined (setting '0')<br>
                <strong>السبب:</strong> مسارات خاطئة في دالة saveCurrentTableData
            </div>
        </div>
        
        <div class="fix-section">
            <h3>✅ الإصلاح المطبق</h3>
            <div class="code-block">
<strong>قبل الإصلاح - مسارات خاطئة:</strong>
❌ tempTableData.patrolLocations[rowIndex] = locationSelect.value;
❌ tempTableData.patrolPersonnel[`${rowIndex}-${colIndex}`] = select.value;
❌ tempTableData.shiftsLocations[rowIndex] = locationSelect.value;
❌ tempTableData.shiftsPersonnel[`${rowIndex}-${colIndex}`] = select.value;

<strong>بعد الإصلاح - مسارات صحيحة:</strong>
✅ tempTableData.domData.patrolLocations[rowIndex] = locationSelect.value;
✅ tempTableData.domData.patrolPersonnel[`${rowIndex}-${colIndex}`] = select.value;
✅ tempTableData.domData.shiftsLocations[rowIndex] = locationSelect.value;
✅ tempTableData.domData.shiftsPersonnel[`${rowIndex}-${colIndex}`] = select.value;
            </div>
        </div>
        
        <div class="fix-section">
            <h3>🧪 اختبار الإصلاح</h3>
            <div>
                <button onclick="openDuties()" class="primary">📋 فتح كشف الواجبات</button>
                <button onclick="testAddRow()">➕ اختبار إضافة صف</button>
                <button onclick="showInstructions()">📖 تعليمات الاختبار</button>
            </div>
        </div>
        
        <div class="fix-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testStatus">
                <div class="status info">⏳ جاري انتظار بدء الاختبار...</div>
            </div>
        </div>
        
        <div class="fix-section">
            <h3>📋 سجل الاختبار</h3>
            <div id="testLog" class="log">
                جاري تحميل أدوات الاختبار...
            </div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
        </div>
        
        <div class="fix-section" id="instructions" style="display: none;">
            <h3>📖 تعليمات الاختبار المفصلة</h3>
            <div class="code-block">
<strong>خطوات الاختبار:</strong>

1. افتح صفحة كشف الواجبات
2. افتح وحدة التحكم (F12) لمراقبة الأخطاء
3. املأ بعض البيانات في الجدول الرئيسي:
   - اختر موقع
   - اختر أفراد
   - اكتب ملاحظة

4. اضغط على زر "إضافة صف" (أيقونة + بجانب رقم الصف)

<strong>النتيجة المتوقعة:</strong>
✅ لا توجد رسائل خطأ في وحدة التحكم
✅ يتم إضافة صف جديد
✅ البيانات الموجودة تبقى كما هي
✅ يمكن اختيار موقع وأفراد في الصف الجديد

<strong>إذا رأيت أي أخطاء:</strong>
❌ انسخ رسالة الخطأ وأرسلها للمطور
❌ تأكد من تحديث الصفحة قبل الاختبار
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = `<div style="color: ${colors[type]}; margin: 3px 0; padding: 8px; border-left: 3px solid ${colors[type]}; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += entry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('testStatus');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function openDuties() {
            window.open('/duties/', '_blank');
            log('🔗 تم فتح صفحة كشف الواجبات', 'info');
            updateStatus('تم فتح صفحة كشف الواجبات - ابدأ الاختبار', 'info');
        }
        
        function testAddRow() {
            log('🧪 تعليمات اختبار إضافة الصف:', 'info');
            log('1. افتح صفحة كشف الواجبات', 'info');
            log('2. افتح وحدة التحكم (F12)', 'info');
            log('3. املأ بعض البيانات في الصف الأول', 'info');
            log('4. اضغط على زر "إضافة صف" (أيقونة + بجانب رقم الصف)', 'info');
            log('5. راقب وحدة التحكم - يجب ألا ترى أي أخطاء', 'info');
            updateStatus('جاري اختبار إضافة الصف...', 'warning');
        }
        
        function showInstructions() {
            const instructions = document.getElementById('instructions');
            if (instructions.style.display === 'none') {
                instructions.style.display = 'block';
                log('📖 تم عرض التعليمات المفصلة', 'info');
            } else {
                instructions.style.display = 'none';
                log('📖 تم إخفاء التعليمات', 'info');
            }
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('🧹 تم مسح السجل', 'info');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل أداة اختبار الإصلاح السريع', 'success');
            updateStatus('أداة الاختبار جاهزة', 'info');
            
            log('🔧 تم إصلاح خطأ المسارات في دالة saveCurrentTableData', 'success');
            log('✅ تم تصحيح جميع المراجع إلى tempTableData.domData', 'success');
            log('✅ الآن يجب أن تعمل إضافة الصفوف بدون أخطاء', 'success');
            
            log('📋 جاهز للاختبار!', 'info');
            log('⚠️ تأكد من فتح وحدة التحكم (F12) لمراقبة الأخطاء', 'warning');
        });
    </script>
</body>
</html>
