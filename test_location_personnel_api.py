#!/usr/bin/env python3
"""
اختبار API جلب أفراد الموقع
"""

from app import app
from models import Location, Personnel, LocationPersonnel
import requests
import json

def test_location_personnel_api():
    """اختبار API جلب أفراد الموقع"""
    
    with app.app_context():
        print("🔍 البحث عن الموقع M16A4...")
        
        # البحث عن الموقع بالاسم
        location = Location.query.filter_by(name='M16A4').first()
        
        if not location:
            print("❌ لم يتم العثور على الموقع M16A4")
            
            # البحث عن مواقع مشابهة
            similar_locations = Location.query.filter(Location.name.ilike('%M16%')).all()
            if similar_locations:
                print("🔍 مواقع مشابهة:")
                for loc in similar_locations:
                    print(f"  - ID: {loc.id}, Name: {loc.name}")
            
            # البحث عن جميع المواقع التي تحتوي على أفراد
            locations_with_personnel = Location.query.join(LocationPersonnel).filter(
                LocationPersonnel.is_active == True
            ).distinct().all()
            
            print(f"\n📊 المواقع التي تحتوي على أفراد ({len(locations_with_personnel)}):")
            for loc in locations_with_personnel[:10]:  # أول 10 مواقع
                personnel_count = LocationPersonnel.query.filter_by(
                    location_id=loc.id, 
                    is_active=True
                ).count()
                print(f"  - ID: {loc.id}, Name: {loc.name}, Personnel: {personnel_count}")
            
            return
        
        print(f"✅ تم العثور على الموقع: ID={location.id}, Name={location.name}")
        
        # التحقق من الأفراد المرتبطين بالموقع
        assignments = LocationPersonnel.query.filter_by(
            location_id=location.id,
            is_active=True
        ).all()
        
        print(f"👥 عدد الأفراد المرتبطين بالموقع: {len(assignments)}")
        
        if assignments:
            print("📋 قائمة الأفراد:")
            for assignment in assignments:
                personnel = assignment.personnel
                print(f"  - ID: {personnel.id}, Name: {personnel.name}, Rank: {personnel.rank}")
        
        # اختبار API endpoint
        print(f"\n🌐 اختبار API endpoint: /locations/{location.id}/personnel/list")
        
        try:
            # محاكاة طلب HTTP
            with app.test_client() as client:
                # تسجيل دخول (إذا كان مطلوباً)
                response = client.get(f'/locations/{location.id}/personnel/list')
                
                print(f"📡 Response Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.get_json()
                    print(f"✅ API Response: {json.dumps(data, ensure_ascii=False, indent=2)}")
                else:
                    print(f"❌ API Error: {response.data.decode()}")
                    
        except Exception as e:
            print(f"❌ خطأ في اختبار API: {str(e)}")

def test_javascript_location_selection():
    """اختبار آلية اختيار الموقع في JavaScript"""
    
    print("\n" + "="*50)
    print("🔧 اختبار آلية اختيار الموقع في JavaScript")
    print("="*50)
    
    with app.app_context():
        # البحث عن المواقع المتاحة
        locations = Location.query.all()
        
        print(f"📊 إجمالي المواقع في النظام: {len(locations)}")
        
        # البحث عن المواقع التي تحتوي على أفراد
        locations_with_personnel = []
        
        for location in locations:
            personnel_count = LocationPersonnel.query.filter_by(
                location_id=location.id,
                is_active=True
            ).count()
            
            if personnel_count > 0:
                locations_with_personnel.append({
                    'id': location.id,
                    'name': location.name,
                    'personnel_count': personnel_count
                })
        
        print(f"👥 المواقع التي تحتوي على أفراد: {len(locations_with_personnel)}")
        
        # عرض أول 10 مواقع
        print("\n📋 أول 10 مواقع تحتوي على أفراد:")
        for loc in locations_with_personnel[:10]:
            print(f"  - ID: {loc['id']}, Name: {loc['name']}, Personnel: {loc['personnel_count']}")
        
        # البحث عن M16A4 تحديداً
        m16a4_location = next((loc for loc in locations_with_personnel if 'M16A4' in loc['name']), None)
        
        if m16a4_location:
            print(f"\n✅ تم العثور على M16A4: {m16a4_location}")
            
            # اختبار الـ API لهذا الموقع
            location_id = m16a4_location['id']
            
            with app.test_client() as client:
                response = client.get(f'/locations/{location_id}/personnel/list')
                
                if response.status_code == 200:
                    data = response.get_json()
                    print(f"✅ API يعمل بشكل صحيح للموقع M16A4")
                    print(f"📊 عدد الأفراد المُرجع: {len(data.get('personnel', []))}")
                else:
                    print(f"❌ API لا يعمل للموقع M16A4: {response.status_code}")
        else:
            print("❌ لم يتم العثور على موقع M16A4 يحتوي على أفراد")

if __name__ == '__main__':
    print("🧪 اختبار API جلب أفراد الموقع")
    print("="*50)
    
    test_location_personnel_api()
    test_javascript_location_selection()
    
    print("\n" + "="*50)
    print("✅ انتهى الاختبار")
