#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import json
import os
import sys

def search_locations_in_database():
    """البحث عن المواقع في قاعدة البيانات"""
    try:
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cur = conn.cursor()
        
        print("🔍 البحث عن المواقع في قاعدة البيانات...")
        print("=" * 50)
        
        # فحص جدول locations
        cur.execute("SELECT COUNT(*) FROM locations")
        locations_count = cur.fetchone()[0]
        print(f"📊 عدد المواقع في جدول locations: {locations_count}")
        
        if locations_count > 0:
            cur.execute("SELECT * FROM locations")
            locations = cur.fetchall()
            
            # الحصول على أسماء الأعمدة
            cur.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'locations'
                ORDER BY ordinal_position
            """)
            columns = [col[0] for col in cur.fetchall()]
            
            print(f"📋 الأعمدة في جدول locations: {', '.join(columns)}")
            print("\n📍 المواقع الموجودة:")
            print("-" * 30)
            
            for location in locations:
                location_dict = dict(zip(columns, location))
                print(f"🏢 الموقع {location_dict.get('id', 'غير محدد')}:")
                for col, val in location_dict.items():
                    print(f"   {col}: {val}")
                print()
        
        # البحث في جداول أخرى قد تحتوي على معلومات المواقع
        related_tables = [
            'location_personnel', 'location_equipment', 'patrol_locations',
            'receipts_patrol_locations', 'receipts_shifts_locations', 'shifts_locations'
        ]
        
        print("\n🔍 البحث في الجداول المرتبطة:")
        print("-" * 30)
        
        for table in related_tables:
            try:
                cur.execute(f"SELECT COUNT(*) FROM {table}")
                count = cur.fetchone()[0]
                print(f"📊 {table}: {count} سجل")
                
                if count > 0:
                    cur.execute(f"SELECT * FROM {table} LIMIT 5")
                    records = cur.fetchall()
                    
                    # الحصول على أسماء الأعمدة
                    cur.execute(f"""
                        SELECT column_name 
                        FROM information_schema.columns 
                        WHERE table_name = '{table}'
                        ORDER BY ordinal_position
                    """)
                    columns = [col[0] for col in cur.fetchall()]
                    
                    print(f"   الأعمدة: {', '.join(columns)}")
                    for record in records:
                        record_dict = dict(zip(columns, record))
                        print(f"   📄 سجل: {record_dict}")
                    print()
                    
            except Exception as e:
                print(f"   ❌ خطأ في قراءة {table}: {e}")
        
        # البحث عن أي جداول أخرى تحتوي على كلمة location
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%location%'
        """)
        location_tables = cur.fetchall()
        
        print(f"\n📋 جميع الجداول التي تحتوي على 'location': {[t[0] for t in location_tables]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في البحث في قاعدة البيانات: {e}")
        return False

def search_locations_in_file():
    """البحث عن المواقع في ملف iMQS1"""
    try:
        # البحث في مجلد سطح المكتب
        desktop_path = os.path.expanduser("~/Desktop")
        imqs1_path = os.path.join(desktop_path, "iMQS1")
        
        print(f"\n🔍 البحث عن ملف iMQS1 في: {desktop_path}")
        print("=" * 50)
        
        if os.path.exists(imqs1_path):
            print(f"✅ تم العثور على: {imqs1_path}")
            
            # فحص نوع الملف
            if os.path.isfile(imqs1_path):
                print("📄 iMQS1 هو ملف")
                
                # محاولة قراءة الملف كـ JSON
                try:
                    with open(imqs1_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    print(f"📊 حجم الملف: {len(content)} حرف")
                    
                    # محاولة تحليل JSON
                    try:
                        data = json.loads(content)
                        print("✅ الملف بصيغة JSON صحيحة")
                        
                        # البحث عن المواقع في البيانات
                        locations_found = []
                        
                        def search_locations_recursive(obj, path=""):
                            if isinstance(obj, dict):
                                for key, value in obj.items():
                                    current_path = f"{path}.{key}" if path else key
                                    if 'location' in key.lower() or 'موقع' in str(value):
                                        locations_found.append({
                                            'path': current_path,
                                            'key': key,
                                            'value': value
                                        })
                                    search_locations_recursive(value, current_path)
                            elif isinstance(obj, list):
                                for i, item in enumerate(obj):
                                    search_locations_recursive(item, f"{path}[{i}]")
                        
                        search_locations_recursive(data)
                        
                        if locations_found:
                            print(f"\n📍 تم العثور على {len(locations_found)} موقع في الملف:")
                            print("-" * 30)
                            for loc in locations_found:
                                print(f"🏢 المسار: {loc['path']}")
                                print(f"   المفتاح: {loc['key']}")
                                print(f"   القيمة: {loc['value']}")
                                print()
                        else:
                            print("❌ لم يتم العثور على مواقع في الملف")
                            
                        # عرض بنية البيانات العامة
                        print(f"\n📋 بنية البيانات في الملف:")
                        if isinstance(data, dict):
                            print(f"   المفاتيح الرئيسية: {list(data.keys())}")
                        elif isinstance(data, list):
                            print(f"   عدد العناصر: {len(data)}")
                            if data and isinstance(data[0], dict):
                                print(f"   مفاتيح العنصر الأول: {list(data[0].keys())}")
                        
                    except json.JSONDecodeError:
                        print("⚠️ الملف ليس بصيغة JSON، محاولة قراءة كنص عادي...")
                        
                        # البحث عن كلمات مفتاحية في النص
                        location_keywords = ['location', 'موقع', 'مكان', 'عنوان', 'address']
                        found_lines = []
                        
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            for keyword in location_keywords:
                                if keyword.lower() in line.lower():
                                    found_lines.append(f"السطر {i+1}: {line.strip()}")
                        
                        if found_lines:
                            print(f"📍 تم العثور على {len(found_lines)} سطر يحتوي على كلمات مفتاحية:")
                            for line in found_lines[:10]:  # عرض أول 10 أسطر
                                print(f"   {line}")
                        else:
                            print("❌ لم يتم العثور على كلمات مفتاحية للمواقع")
                        
                except Exception as e:
                    print(f"❌ خطأ في قراءة الملف: {e}")
                    
            elif os.path.isdir(imqs1_path):
                print("📁 iMQS1 هو مجلد")
                
                # البحث في ملفات المجلد
                files = os.listdir(imqs1_path)
                print(f"📊 عدد الملفات: {len(files)}")
                
                location_files = []
                for file in files:
                    if any(keyword in file.lower() for keyword in ['location', 'موقع', 'مكان']):
                        location_files.append(file)
                
                if location_files:
                    print(f"📍 ملفات قد تحتوي على مواقع: {location_files}")
                else:
                    print("📋 جميع الملفات:")
                    for file in files[:20]:  # عرض أول 20 ملف
                        print(f"   📄 {file}")
        else:
            print(f"❌ لم يتم العثور على iMQS1 في: {desktop_path}")
            
            # البحث في مجلدات أخرى محتملة
            other_paths = [
                os.path.join(desktop_path, "iMQS1.json"),
                os.path.join(desktop_path, "iMQS1.txt"),
                os.path.join(os.path.expanduser("~"), "iMQS1"),
                os.path.join("C:\\Users\\<USER>\\Desktop", "iMQS1"),
            ]
            
            print("\n🔍 البحث في مسارات أخرى:")
            for path in other_paths:
                if os.path.exists(path):
                    print(f"✅ تم العثور على: {path}")
                    return path
                else:
                    print(f"❌ غير موجود: {path}")
        
        return None
        
    except Exception as e:
        print(f"❌ خطأ في البحث عن الملف: {e}")
        return None

if __name__ == "__main__":
    print("🔍 البحث عن المواقع المفقودة")
    print("=" * 50)
    
    # البحث في قاعدة البيانات
    db_success = search_locations_in_database()
    
    # البحث في الملف
    file_path = search_locations_in_file()
    
    print("\n" + "=" * 50)
    print("📋 ملخص النتائج:")
    print(f"   قاعدة البيانات: {'✅ تم الفحص' if db_success else '❌ فشل الفحص'}")
    print(f"   ملف iMQS1: {'✅ تم العثور عليه' if file_path else '❌ لم يتم العثور عليه'}")
