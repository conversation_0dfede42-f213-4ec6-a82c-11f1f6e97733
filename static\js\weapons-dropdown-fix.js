/**
 * إصلاح خاص للقوائم المنسدلة في صفحة قائمة الأسلحة
 */
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق الإصلاح فقط في صفحة قائمة الأسلحة
    if (document.querySelector('.table-responsive')) {
        // الحصول على جميع أزرار القوائم المنسدلة في الجدول
        const dropdownButtons = document.querySelectorAll('.table .dropdown-toggle');
        
        dropdownButtons.forEach(function(button) {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                
                // إغلاق جميع القوائم المنسدلة الأخرى
                document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                    menu.classList.remove('show');
                });
                
                // الحصول على القائمة المنسدلة المرتبطة بالزر
                const parent = button.closest('.btn-block');
                if (parent) {
                    const menu = parent.querySelector('.dropdown-menu');
                    if (menu) {
                        // تبديل حالة القائمة المنسدلة
                        menu.classList.toggle('show');
                        
                        // تحديد موضع القائمة المنسدلة بناءً على موضع الزر
                        const buttonRect = button.getBoundingClientRect();
                        
                        // تعيين موضع القائمة المنسدلة
                        menu.style.position = 'fixed';
                        menu.style.top = (buttonRect.bottom + 5) + 'px';
                        menu.style.left = (buttonRect.left - 100) + 'px'; // تعديل هذه القيمة حسب الحاجة
                        menu.style.right = 'auto';
                        menu.style.zIndex = '9999';
                        
                        // إضافة فئة مخصصة للقائمة المنسدلة
                        menu.classList.add('weapons-dropdown');
                        
                        // إغلاق القائمة عند النقر في أي مكان آخر
                        const closeMenu = function(e) {
                            if (!button.contains(e.target) && !menu.contains(e.target)) {
                                menu.classList.remove('show');
                                document.removeEventListener('click', closeMenu);
                            }
                        };
                        
                        // إضافة معالج الحدث بعد تأخير قصير لتجنب إغلاق القائمة فوراً
                        setTimeout(function() {
                            document.addEventListener('click', closeMenu);
                        }, 10);
                    }
                }
            });
        });
    }
});
