"""
نظام المخزون المستقل - مستقل تماماً عن المستودعات
يتيح إضافة وإدارة الملبوسات والمعدات والذخيرة بشكل مبسط
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, SelectField, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Optional, Length, NumberRange
from sqlalchemy import or_, and_
# استيراد النماذج والأدوات
import models
from datetime_utils import get_saudi_now

# إنشاء Blueprint للمخزون المستقل
standalone_inventory_bp = Blueprint('standalone_inventory', __name__, url_prefix='/standalone-inventory')

# نماذج مخصصة لكل فئة
class ClothingForm(FlaskForm):
    """نموذج إضافة الملبوسات"""
    item_code = StringField('كود الملبوس/الباركود', validators=[DataRequired(), Length(max=100)])
    name = StringField('اسم الملبوس', validators=[DataRequired(), Length(max=200)])
    subcategory = SelectField('نوع الملبوس', choices=[
        ('زي رسمي', 'زي رسمي'),
        ('زي تدريب', 'زي تدريب'),
        ('زي صيفي', 'زي صيفي'),
        ('زي شتوي', 'زي شتوي'),
        ('أحذية', 'أحذية'),
        ('قبعات', 'قبعات'),
        ('أحزمة', 'أحزمة'),
        ('جوارب', 'جوارب'),
        ('ملابس داخلية', 'ملابس داخلية'),
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])
    size = SelectField('المقاس', choices=[
        ('XS', 'صغير جداً (XS)'),
        ('S', 'صغير (S)'),
        ('M', 'متوسط (M)'),
        ('L', 'كبير (L)'),
        ('XL', 'كبير جداً (XL)'),
        ('XXL', 'كبير جداً جداً (XXL)'),
        ('36', '36'),
        ('38', '38'),
        ('40', '40'),
        ('42', '42'),
        ('44', '44'),
        ('46', '46'),
        ('48', '48'),
        ('50', '50')
    ], validators=[Optional()])
    color = SelectField('اللون', choices=[
        ('أخضر', 'أخضر'),
        ('بني', 'بني'),
        ('أسود', 'أسود'),
        ('رمادي', 'رمادي'),
        ('كاكي', 'كاكي'),
        ('أزرق', 'أزرق'),
        ('أبيض', 'أبيض')
    ], validators=[Optional()])
    material = StringField('المادة', validators=[Optional(), Length(max=100)])
    quantity = IntegerField('الكمية', validators=[DataRequired(), NumberRange(min=0)], default=0)
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('إضافة الملبوس')

class EquipmentForm(FlaskForm):
    """نموذج إضافة المعدات"""
    item_code = StringField('كود المعدة/الباركود', validators=[DataRequired(), Length(max=100)])
    name = StringField('اسم المعدة', validators=[DataRequired(), Length(max=200)])
    subcategory = SelectField('نوع المعدة', choices=[
        ('خوذة', 'خوذة'),
        ('سترة واقية', 'سترة واقية'),
        ('نظارات واقية', 'نظارات واقية'),
        ('قفازات', 'قفازات'),
        ('معدات اتصال', 'معدات اتصال'),
        ('معدات طبية', 'معدات طبية'),
        ('معدات تقنية', 'معدات تقنية'),
        ('أدوات', 'أدوات'),
        ('حقائب', 'حقائب'),
        ('معدات تدريب', 'معدات تدريب'),
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])
    brand = StringField('الماركة/الشركة المصنعة', validators=[Optional(), Length(max=100)])
    model = StringField('الموديل', validators=[Optional(), Length(max=100)])
    serial_number = StringField('الرقم التسلسلي', validators=[Optional(), Length(max=100)])
    condition = SelectField('الحالة', choices=[
        ('جديد', 'جديد'),
        ('مستعمل - ممتاز', 'مستعمل - ممتاز'),
        ('مستعمل - جيد', 'مستعمل - جيد'),
        ('يحتاج صيانة', 'يحتاج صيانة')
    ], validators=[Optional()])
    quantity = IntegerField('الكمية', validators=[DataRequired(), NumberRange(min=0)], default=0)
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('إضافة المعدة')

class AmmunitionForm(FlaskForm):
    """نموذج إضافة الذخيرة"""
    item_code = StringField('كود الذخيرة/الباركود', validators=[DataRequired(), Length(max=100)])
    name = StringField('اسم الذخيرة', validators=[DataRequired(), Length(max=200)])
    subcategory = SelectField('نوع الذخيرة', choices=[
        ('رصاص حي', 'رصاص حي'),
        ('رصاص تدريب', 'رصاص تدريب'),
        ('رصاص مطاطي', 'رصاص مطاطي'),
        ('قنابل دخان', 'قنابل دخان'),
        ('قنابل صوت', 'قنابل صوت'),
        ('قنابل غاز', 'قنابل غاز'),
        ('متفجرات', 'متفجرات'),
        ('صواريخ', 'صواريخ'),
        ('قذائف', 'قذائف'),
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])
    caliber = StringField('العيار', validators=[Optional(), Length(max=50)])
    batch_number = StringField('رقم الدفعة', validators=[Optional(), Length(max=100)])
    manufacture_date = StringField('تاريخ التصنيع', validators=[Optional(), Length(max=20)])
    expiry_date = StringField('تاريخ انتهاء الصلاحية', validators=[Optional(), Length(max=20)])
    danger_level = SelectField('مستوى الخطورة', choices=[
        ('منخفض', 'منخفض'),
        ('متوسط', 'متوسط'),
        ('عالي', 'عالي'),
        ('عالي جداً', 'عالي جداً')
    ], validators=[Optional()])
    quantity = IntegerField('الكمية', validators=[DataRequired(), NumberRange(min=0)], default=0)
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('إضافة الذخيرة')

# نموذج البحث
class SearchForm(FlaskForm):
    """نموذج البحث في المخزون"""
    search = StringField('البحث', validators=[Optional()])
    category = SelectField('الفئة', choices=[
        ('', 'جميع الفئات'),
        ('ملبوسات', 'ملبوسات'),
        ('معدات', 'معدات'),
        ('ذخيرة', 'ذخيرة')
    ], validators=[Optional()])
    submit = SubmitField('بحث')

def log_activity(action, details):
    """تسجيل نشاط في سجل الأنشطة"""
    try:
        activity = models.ActivityLog(
            user_id=current_user.id,
            action=action,
            details=details,
            timestamp=get_saudi_now()
        )
        models.db.session.add(activity)
        models.db.session.commit()
    except Exception as e:
        print(f"Error logging activity: {e}")

@standalone_inventory_bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية للمخزون المستقل"""
    
    # إحصائيات عامة
    total_items = models.InventoryItem.query.count()

    # إحصائيات حسب الفئة
    categories_stats = {}
    for category in ['ملبوسات', 'معدات', 'ذخيرة']:
        count = models.InventoryItem.query.filter(models.InventoryItem.category == category).count()
        categories_stats[category] = count

    # آخر الأصناف المضافة
    recent_items = models.InventoryItem.query.order_by(models.InventoryItem.created_at.desc()).limit(5).all()
    
    return render_template('standalone_inventory/index.html',
                         total_items=total_items,
                         categories_stats=categories_stats,
                         recent_items=recent_items)

@standalone_inventory_bp.route('/items')
@login_required
def items():
    """قائمة الأصناف مع البحث"""
    
    # فلاتر البحث
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    
    # بناء الاستعلام
    query = models.InventoryItem.query

    if search:
        # البحث في الاسم، الكود، الباركود، أو أي معرف آخر
        query = query.filter(or_(
            models.InventoryItem.name.contains(search),
            models.InventoryItem.item_code.contains(search),
            models.InventoryItem.item_code == search,  # بحث دقيق للباركود
            models.InventoryItem.subcategory.contains(search),
            models.InventoryItem.notes.contains(search)
        ))

    if category:
        query = query.filter(models.InventoryItem.category == category)

    # ترتيب النتائج
    items = query.order_by(models.InventoryItem.name).all()
    
    return render_template('standalone_inventory/items.html',
                         items=items,
                         search=search,
                         category=category)

# دالة مساعدة لإنشاء الأصناف
def create_item_helper(form, category, item_type_name):
    """دالة مساعدة لإنشاء الأصناف"""
    try:
        # الحصول على المستودع الافتراضي أو إنشاء واحد
        default_warehouse = models.Warehouse.query.first()
        if not default_warehouse:
            default_warehouse = models.Warehouse(
                name='المخزون العام',
                location='المقر الرئيسي',
                description='مستودع افتراضي للمخزون العام'
            )
            models.db.session.add(default_warehouse)
            models.db.session.flush()

        # إنشاء الصنف الجديد
        item_data = {
            'item_code': form.item_code.data,
            'name': form.name.data,
            'category': category,
            'subcategory': form.subcategory.data,
            'quantity_in_stock': form.quantity.data,
            'quantity_issued': 0,
            'minimum_stock': 0,
            'maximum_stock': 0,
            'status': 'متوفر',
            'notes': form.notes.data,
            'warehouse_id': default_warehouse.id
        }

        # إضافة حقول خاصة بكل فئة
        if hasattr(form, 'size'):
            item_data['size'] = form.size.data
        if hasattr(form, 'color'):
            item_data['color'] = form.color.data
        if hasattr(form, 'material'):
            item_data['material'] = form.material.data
        if hasattr(form, 'brand'):
            item_data['brand'] = form.brand.data
        if hasattr(form, 'model'):
            item_data['model'] = form.model.data
        if hasattr(form, 'serial_number'):
            item_data['serial_numbers'] = form.serial_number.data
        if hasattr(form, 'batch_number'):
            item_data['batch_number'] = form.batch_number.data
        if hasattr(form, 'caliber'):
            item_data['specifications'] = f"العيار: {form.caliber.data}" if form.caliber.data else ""

        item = models.InventoryItem(**item_data)
        models.db.session.add(item)
        models.db.session.commit()

        # تسجيل النشاط
        log_activity(
            f'إضافة {item_type_name}',
            f'تم إضافة {item_type_name}: {item.name} (كود: {item.item_code})'
        )

        flash(f'تم إضافة {item_type_name} "{item.name}" بنجاح', 'success')
        return True

    except Exception as e:
        models.db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة {item_type_name}: {str(e)}', 'error')
        return False

@standalone_inventory_bp.route('/add-clothing', methods=['GET', 'POST'])
@login_required
def add_clothing():
    """إضافة ملبوسات جديدة"""
    form = ClothingForm()

    if form.validate_on_submit():
        if create_item_helper(form, 'ملبوسات', 'الملبوس'):
            return redirect(url_for('standalone_inventory.items', category='ملبوسات'))

    return render_template('standalone_inventory/add_clothing.html', form=form)

@standalone_inventory_bp.route('/add-equipment', methods=['GET', 'POST'])
@login_required
def add_equipment():
    """إضافة معدات جديدة"""
    form = EquipmentForm()

    if form.validate_on_submit():
        if create_item_helper(form, 'معدات', 'المعدة'):
            return redirect(url_for('standalone_inventory.items', category='معدات'))

    return render_template('standalone_inventory/add_equipment.html', form=form)

@standalone_inventory_bp.route('/add-ammunition', methods=['GET', 'POST'])
@login_required
def add_ammunition():
    """إضافة ذخيرة جديدة"""
    form = AmmunitionForm()

    if form.validate_on_submit():
        if create_item_helper(form, 'ذخيرة', 'الذخيرة'):
            return redirect(url_for('standalone_inventory.items', category='ذخيرة'))

    return render_template('standalone_inventory/add_ammunition.html', form=form)

@standalone_inventory_bp.route('/add-item')
@login_required
def add_item():
    """صفحة اختيار نوع الصنف المراد إضافته"""
    return render_template('standalone_inventory/add_item.html')

@standalone_inventory_bp.route('/item/<int:item_id>')
@login_required
def item_details(item_id):
    """تفاصيل صنف"""
    item = models.InventoryItem.query.get_or_404(item_id)
    return render_template('standalone_inventory/item_details.html', item=item)

@standalone_inventory_bp.route('/edit/<int:item_id>', methods=['GET', 'POST'])
@login_required
def edit_item(item_id):
    """تعديل صنف"""
    item = models.InventoryItem.query.get_or_404(item_id)

    # اختيار النموذج المناسب حسب الفئة
    if item.category == 'ملبوسات':
        form = ClothingForm(obj=item)
    elif item.category == 'معدات':
        form = EquipmentForm(obj=item)
    elif item.category == 'ذخيرة':
        form = AmmunitionForm(obj=item)
    else:
        flash('فئة غير معروفة', 'error')
        return redirect(url_for('standalone_inventory.items'))

    if form.validate_on_submit():
        try:
            # تحديث البيانات الأساسية
            item.item_code = form.item_code.data
            item.name = form.name.data
            item.subcategory = form.subcategory.data
            item.quantity_in_stock = form.quantity.data
            item.notes = form.notes.data

            # تحديث الحقول الخاصة بكل فئة
            if hasattr(form, 'size'):
                item.size = form.size.data
            if hasattr(form, 'color'):
                item.color = form.color.data
            if hasattr(form, 'material'):
                item.material = form.material.data
            if hasattr(form, 'brand'):
                item.brand = form.brand.data
            if hasattr(form, 'model'):
                item.model = form.model.data
            if hasattr(form, 'serial_number'):
                item.serial_numbers = form.serial_number.data
            if hasattr(form, 'batch_number'):
                item.batch_number = form.batch_number.data
            if hasattr(form, 'caliber'):
                item.specifications = f"العيار: {form.caliber.data}" if form.caliber.data else ""

            models.db.session.commit()

            # تسجيل النشاط
            log_activity(
                f'تعديل صنف',
                f'تم تعديل الصنف: {item.name} (كود: {item.item_code})'
            )

            flash(f'تم تعديل الصنف "{item.name}" بنجاح', 'success')
            return redirect(url_for('standalone_inventory.item_details', item_id=item.id))

        except Exception as e:
            models.db.session.rollback()
            flash(f'حدث خطأ أثناء تعديل الصنف: {str(e)}', 'error')

    return render_template('standalone_inventory/edit_item.html', form=form, item=item)

@standalone_inventory_bp.route('/delete/<int:item_id>', methods=['POST'])
@login_required
def delete_item(item_id):
    """حذف صنف"""
    item = models.InventoryItem.query.get_or_404(item_id)

    try:
        item_name = item.name
        item_code = item.item_code

        models.db.session.delete(item)
        models.db.session.commit()

        # تسجيل النشاط
        log_activity(
            f'حذف صنف',
            f'تم حذف الصنف: {item_name} (كود: {item_code})'
        )

        flash(f'تم حذف الصنف "{item_name}" بنجاح', 'success')

    except Exception as e:
        models.db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الصنف: {str(e)}', 'error')

    return redirect(url_for('standalone_inventory.items'))

@standalone_inventory_bp.route('/search_barcode')
@login_required
def search_barcode():
    """البحث بالباركود عبر AJAX"""
    barcode = request.args.get('barcode', '')
    
    if not barcode:
        return jsonify({'error': 'لم يتم تمرير الباركود'})
    
    # البحث عن الصنف
    item = models.InventoryItem.query.filter_by(item_code=barcode).first()
    
    if item:
        return jsonify({
            'found': True,
            'item': {
                'id': item.id,
                'name': item.name,
                'category': item.category,
                'subcategory': item.subcategory,
                'quantity': item.quantity_in_stock,
                'size': item.size,
                'color': item.color,
                'notes': item.notes
            }
        })
    else:
        return jsonify({'found': False})

@standalone_inventory_bp.route('/categories/<category>')
@login_required
def category_items(category):
    """عرض أصناف فئة معينة"""
    items = models.InventoryItem.query.filter_by(category=category).order_by(models.InventoryItem.name).all()
    return render_template('standalone_inventory/category_items.html',
                         items=items,
                         category=category)

# دالة مساعدة لتسجيل النشاط
def log_activity(action, description):
    """تسجيل النشاط في النظام"""
    try:
        # يمكن إضافة تسجيل النشاط هنا إذا كان هناك نظام لوجز
        # مؤقتاً سنتجاهل هذا
        pass
    except:
        pass

# إضافة البلوبرينت إلى التطبيق الرئيسي
def register_standalone_inventory(app):
    """تسجيل البلوبرينت في التطبيق الرئيسي"""
    app.register_blueprint(standalone_inventory_bp)
