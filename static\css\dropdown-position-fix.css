/* إصلاح موضع القوائم المنسدلة في صفحة قائمة الأسلحة */

/* إصلاح عام لجميع القوائم المنسدلة - تم تعطيله لصالح الإصلاح الحاسم */
/*
.dropdown-menu {
    position: absolute !important;
    inset: auto !important;
    transform: none !important;
    margin: 0 !important;
    z-index: 9999 !important;
}
*/

/* تأكد من أن القائمة المنسدلة تظهر بجوار الزر مباشرة */
.fix-right {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
    top: 100% !important;
}

/* إصلاح خاص لقائمة الإجراءات في صفحة قائمة الأسلحة */
.btn-block .dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
    top: 100% !important;
    transform: none !important;
    margin-top: 2px !important;
}

/* تأكد من أن الحاوية لها موضع نسبي لتحديد موضع القائمة المنسدلة بشكل صحيح */
.btn-block {
    position: relative !important;
    display: inline-block !important;
}

/* تأكد من أن القائمة المنسدلة تظهر فوق العناصر الأخرى */
.dropdown-menu.show {
    display: block !important;
}

/* إصلاح خاص للقوائم المنسدلة في الجداول */
.table td .dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
    top: 100% !important;
    transform: none !important;
}

/* إصلاح خاص للقوائم المنسدلة في الإجراءات */
.dropdown-toggle + .dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
    top: 100% !important;
    transform: none !important;
}

/* تأكد من أن القائمة المنسدلة لا تتجاوز حدود الشاشة */
@media (max-width: 768px) {
    .btn-block .dropdown-menu {
        position: fixed !important;
        top: auto !important;
        right: auto !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 80% !important;
        max-width: 300px !important;
        z-index: 9999 !important;
    }
}

/* أنماط خاصة للقائمة المنسدلة في صفحة قائمة الأسلحة */
.weapons-dropdown {
    min-width: 150px !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2) !important;
    border-radius: 4px !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    background-color: #fff !important;
    padding: 5px 0 !important;
    margin: 0 !important;
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    z-index: 9999 !important;
}

.weapons-dropdown .dropdown-item {
    padding: 8px 15px !important;
    font-size: 14px !important;
    color: #333 !important;
    display: flex !important;
    align-items: center !important;
}

.weapons-dropdown .dropdown-item i {
    margin-left: 8px !important;
    width: 16px !important;
    text-align: center !important;
}

.weapons-dropdown .dropdown-item:hover {
    background-color: #f8f9fa !important;
    color: #000 !important;
}

.weapons-dropdown .dropdown-divider {
    margin: 5px 0 !important;
    border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
}
