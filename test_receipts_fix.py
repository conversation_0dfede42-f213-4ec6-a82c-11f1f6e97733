#!/usr/bin/env python3
"""
سكريبت اختبار إصلاح صفحة كشف الاستلامات
"""

import os
import sys
import json
from datetime import datetime

def test_receipts_endpoints():
    """اختبار endpoints صفحة كشف الاستلامات"""
    
    print("🔍 اختبار endpoints صفحة كشف الاستلامات...")
    
    try:
        from app import create_app
        from models import ReceiptData, ReceiptLocations, PatrolData, ShiftsData, db
        
        # إنشاء application context
        app = create_app()
        with app.app_context():
            
            # اختبار حفظ بيانات الكشف
            print("\n📋 اختبار حفظ بيانات الكشف...")
            
            test_receipt_data = {
                'hijriDate': '19 محرم 1447هـ',
                'gregorianDate': '2025-07-15',
                'receiptNumber': 'TEST-001',
                'rows': [
                    ['الجندي أحمد', 'الموقع الأول', 'ملاحظة'],
                    ['الجندي محمد', 'الموقع الثاني', 'ملاحظة أخرى']
                ]
            }
            
            # حذف البيانات السابقة
            ReceiptData.query.delete()
            
            # إضافة بيانات جديدة
            receipt_data = ReceiptData(
                user_id=1,
                receipt_data=json.dumps(test_receipt_data, ensure_ascii=False)
            )
            db.session.add(receipt_data)
            db.session.commit()
            
            print("   ✅ تم حفظ بيانات الكشف بنجاح")
            
            # اختبار استرجاع البيانات
            saved_data = ReceiptData.query.first()
            if saved_data:
                loaded_data = json.loads(saved_data.receipt_data)
                print(f"   ✅ تم استرجاع البيانات: {loaded_data['receiptNumber']}")
            else:
                print("   ❌ فشل في استرجاع البيانات")
                return False
            
            # اختبار حفظ مواقع الكشف
            print("\n📍 اختبار حفظ مواقع الكشف...")
            
            # حذف المواقع السابقة
            ReceiptLocations.query.delete()
            
            # إضافة مواقع جديدة
            test_locations = [
                {'row_index': 0, 'location_id': 'LOC001', 'timestamp': datetime.now().isoformat()},
                {'row_index': 1, 'location_id': 'LOC002', 'timestamp': datetime.now().isoformat()}
            ]
            
            for loc_data in test_locations:
                location = ReceiptLocations(
                    row_index=loc_data['row_index'],
                    location_id=loc_data['location_id'],
                    timestamp=loc_data['timestamp'],
                    created_by=1
                )
                db.session.add(location)
            
            db.session.commit()
            print("   ✅ تم حفظ المواقع بنجاح")
            
            # اختبار استرجاع المواقع
            saved_locations = ReceiptLocations.query.all()
            print(f"   ✅ تم استرجاع {len(saved_locations)} موقع")
            
            # اختبار حفظ بيانات الدوريات
            print("\n🚶 اختبار حفظ بيانات الدوريات...")
            
            PatrolData.query.delete()
            
            test_patrol_data = {
                'patrols': [
                    {'time': '08:00', 'location': 'الموقع الأول', 'notes': 'دورية صباحية'},
                    {'time': '14:00', 'location': 'الموقع الثاني', 'notes': 'دورية مسائية'}
                ]
            }
            
            patrol_data = PatrolData(
                user_id=1,
                patrol_data=json.dumps(test_patrol_data, ensure_ascii=False)
            )
            db.session.add(patrol_data)
            db.session.commit()
            
            print("   ✅ تم حفظ بيانات الدوريات بنجاح")
            
            # اختبار حفظ بيانات المناوبات
            print("\n👥 اختبار حفظ بيانات المناوبات...")
            
            ShiftsData.query.delete()
            
            test_shifts_data = {
                'shifts': [
                    {'name': 'الجندي علي', 'shift': 'صباحي', 'location': 'البوابة الرئيسية'},
                    {'name': 'الجندي سالم', 'shift': 'مسائي', 'location': 'المستودع الأول'}
                ]
            }
            
            shifts_data = ShiftsData(
                user_id=1,
                shifts_data=json.dumps(test_shifts_data, ensure_ascii=False)
            )
            db.session.add(shifts_data)
            db.session.commit()
            
            print("   ✅ تم حفظ بيانات المناوبات بنجاح")
            
            # التحقق من إجمالي البيانات
            print("\n📊 إحصائيات البيانات المحفوظة:")
            print(f"   - بيانات الكشوفات: {ReceiptData.query.count()}")
            print(f"   - مواقع الكشوفات: {ReceiptLocations.query.count()}")
            print(f"   - بيانات الدوريات: {PatrolData.query.count()}")
            print(f"   - بيانات المناوبات: {ShiftsData.query.count()}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def test_data_persistence():
    """اختبار استمرارية البيانات"""
    
    print("\n🔄 اختبار استمرارية البيانات...")
    
    try:
        from app import create_app
        from models import ReceiptData, ReceiptLocations, PatrolData, ShiftsData
        
        app = create_app()
        with app.app_context():
            
            # التحقق من وجود البيانات بعد إعادة التشغيل
            receipt_count = ReceiptData.query.count()
            location_count = ReceiptLocations.query.count()
            patrol_count = PatrolData.query.count()
            shifts_count = ShiftsData.query.count()
            
            print(f"   📋 بيانات الكشوفات: {receipt_count}")
            print(f"   📍 مواقع الكشوفات: {location_count}")
            print(f"   🚶 بيانات الدوريات: {patrol_count}")
            print(f"   👥 بيانات المناوبات: {shifts_count}")
            
            if receipt_count > 0 and location_count > 0:
                print("   ✅ البيانات محفوظة ومستمرة في PostgreSQL")
                return True
            else:
                print("   ❌ البيانات غير محفوظة")
                return False
                
    except Exception as e:
        print(f"   ❌ خطأ في فحص استمرارية البيانات: {str(e)}")
        return False

def test_api_endpoints():
    """اختبار API endpoints"""
    
    print("\n🌐 اختبار API endpoints...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # اختبار endpoint حفظ البيانات
            test_data = {
                'hijriDate': '19 محرم 1447هـ',
                'gregorianDate': '2025-07-15',
                'receiptNumber': 'API-TEST-001'
            }
            
            response = client.post('/receipts/api/save-receipt-data',
                                 json=test_data,
                                 content_type='application/json')
            
            if response.status_code == 200:
                result = response.get_json()
                if result.get('success'):
                    print("   ✅ API حفظ البيانات يعمل بشكل صحيح")
                else:
                    print(f"   ❌ فشل API حفظ البيانات: {result.get('error')}")
                    return False
            else:
                print(f"   ❌ خطأ في API: {response.status_code}")
                return False
            
            # اختبار endpoint استرجاع البيانات
            response = client.get('/receipts/api/get-receipt-data')
            
            if response.status_code == 200:
                result = response.get_json()
                if result.get('success'):
                    print("   ✅ API استرجاع البيانات يعمل بشكل صحيح")
                else:
                    print(f"   ❌ فشل API استرجاع البيانات: {result.get('message')}")
            else:
                print(f"   ❌ خطأ في API: {response.status_code}")
                return False
            
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار API: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح صفحة كشف الاستلامات")
    print("="*60)
    
    tests = [
        ("اختبار endpoints", test_receipts_endpoints),
        ("اختبار استمرارية البيانات", test_data_persistence),
        ("اختبار API", test_api_endpoints)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'='*60}")
    print(f"📊 نتائج الاختبار:")
    print(f"   ✅ نجح: {passed_tests}/{total_tests}")
    print(f"   ❌ فشل: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! صفحة كشف الاستلامات تعمل بشكل صحيح.")
        print("💾 البيانات الآن محفوظة في PostgreSQL ولن تختفي عند حذف سجلات المتصفح.")
        return 0
    else:
        print(f"\n⚠️ فشل {total_tests - passed_tests} اختبار. يرجى مراجعة الأخطاء.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
