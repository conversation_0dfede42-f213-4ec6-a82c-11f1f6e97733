<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار كشف الاستلامات (الدوريات)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/receipts.css">
    <style>
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-secondary);
        }
        
        .test-title {
            color: #007bff;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .patrol-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .patrol-table th,
        .patrol-table td {
            border: 1px solid var(--border-color);
            padding: 8px;
            text-align: center;
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .patrol-table th {
            background-color: var(--bg-secondary);
            font-weight: bold;
        }
        
        .patrol-table input {
            width: 100%;
            border: none;
            background: transparent;
            color: var(--text-primary);
            text-align: center;
            padding: 4px;
        }
        
        .patrol-table input:focus {
            outline: 2px solid #007bff;
            background-color: var(--bg-tertiary);
        }
        
        .row-number-cell {
            background-color: #3a3a3a !important;
            color: white !important;
            font-weight: bold;
            width: 60px;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <h1 class="text-center mb-4">اختبار كشف الاستلامات (الدوريات)</h1>
        <p class="text-center text-muted">كشف الاستلامات منفصل عن كشف الواجبات ولا يحتوي على مواقع أو أفراد</p>
        
        <!-- معلومات الكشف -->
        <div class="test-section">
            <div class="test-title">معلومات الكشف</div>
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">اليوم:</label>
                    <input type="text" class="form-control" id="dayName" value="الأحد" readonly>
                </div>
                <div class="col-md-3">
                    <label class="form-label">التاريخ الهجري:</label>
                    <input type="text" class="form-control" id="hijriDate" value="15/02/1446" readonly>
                </div>
                <div class="col-md-3">
                    <label class="form-label">التاريخ الميلادي:</label>
                    <input type="date" class="form-control" id="gregorianDate">
                </div>
                <div class="col-md-3">
                    <label class="form-label">رقم الكشف:</label>
                    <input type="text" class="form-control" id="receiptNumber" value="P20240807001">
                </div>
            </div>
        </div>
        
        <!-- جدول كشف الاستلامات -->
        <div class="test-section">
            <div class="test-title">كشف استلامات الدوريات</div>
            <div class="d-flex justify-content-between mb-3">
                <div>
                    <button class="btn btn-success btn-sm" onclick="addPatrolRow()">إضافة صف</button>
                    <button class="btn btn-warning btn-sm" onclick="clearPatrolTable()">تفريغ الجدول</button>
                </div>
                <div>
                    <button class="btn btn-primary btn-sm" onclick="savePatrolData()">حفظ البيانات</button>
                    <button class="btn btn-info btn-sm" onclick="loadPatrolData()">تحميل البيانات</button>
                </div>
            </div>
            
            <table class="patrol-table receipts-table" id="patrolTable">
                <thead>
                    <tr id="patrolHeaders">
                        <!-- سيتم إنشاء العناوين بواسطة JavaScript -->
                    </tr>
                </thead>
                <tbody id="patrolBody">
                    <!-- سيتم إنشاء الصفوف بواسطة JavaScript -->
                </tbody>
            </table>
        </div>
        
        <!-- أزرار التحكم -->
        <div class="test-section">
            <div class="test-title">أزرار التحكم</div>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-outline-primary w-100" onclick="printPatrolTable()">طباعة الكشف</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-success w-100" onclick="exportPatrolToExcel()">تصدير إلى Excel</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-danger w-100" onclick="resetAll()">إعادة تعيين الكل</button>
                </div>
            </div>
        </div>
        
        <!-- سجل العمليات -->
        <div class="test-section">
            <div class="test-title">سجل العمليات</div>
            <div id="operationLog" style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                <!-- سيتم إضافة العمليات هنا -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // بيانات كشف الاستلامات
        let patrolData = {
            headers: ['الرقم', 'ملاحظات', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات إضافية'],
            rows: []
        };
        
        // دالة لإضافة رسالة إلى السجل
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logElement = document.getElementById('operationLog');
            const messageElement = document.createElement('div');
            messageElement.className = `text-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'}`;
            messageElement.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(messageElement);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // تهيئة الجدول
        function initializePatrolTable() {
            // إنشاء صفوف افتراضية
            for (let i = 0; i < 6; i++) {
                patrolData.rows.push(Array(patrolData.headers.length).fill(''));
            }
            
            generatePatrolTable();
            addToLog('تم تهيئة جدول كشف الاستلامات', 'success');
        }
        
        // إنشاء الجدول
        function generatePatrolTable() {
            const headersRow = document.getElementById('patrolHeaders');
            const tbody = document.getElementById('patrolBody');
            
            // مسح المحتوى الحالي
            headersRow.innerHTML = '';
            tbody.innerHTML = '';
            
            // إنشاء العناوين
            patrolData.headers.forEach((header, index) => {
                const th = document.createElement('th');
                th.textContent = header;
                if (index === 0) {
                    th.className = 'row-number-cell';
                }
                headersRow.appendChild(th);
            });
            
            // إنشاء الصفوف
            patrolData.rows.forEach((row, rowIndex) => {
                const tr = document.createElement('tr');
                
                row.forEach((cell, cellIndex) => {
                    const td = document.createElement('td');
                    
                    if (cellIndex === 0) {
                        // عمود الرقم
                        td.className = 'row-number-cell';
                        td.textContent = rowIndex + 1;
                    } else {
                        // خلايا قابلة للتحرير
                        const input = document.createElement('input');
                        input.type = 'text';
                        input.value = cell || '';
                        input.addEventListener('input', function() {
                            patrolData.rows[rowIndex][cellIndex] = this.value;
                            autoSave();
                        });
                        td.appendChild(input);
                    }
                    
                    tr.appendChild(td);
                });
                
                tbody.appendChild(tr);
            });
        }
        
        // إضافة صف جديد
        function addPatrolRow() {
            const newRow = Array(patrolData.headers.length).fill('');
            patrolData.rows.push(newRow);
            generatePatrolTable();
            addToLog(`تم إضافة صف جديد (المجموع: ${patrolData.rows.length} صف)`, 'success');
        }
        
        // تفريغ الجدول
        function clearPatrolTable() {
            if (confirm('هل أنت متأكد من تفريغ جدول كشف الاستلامات؟')) {
                patrolData.rows = [];
                // إضافة صفوف افتراضية
                for (let i = 0; i < 6; i++) {
                    patrolData.rows.push(Array(patrolData.headers.length).fill(''));
                }
                generatePatrolTable();
                addToLog('تم تفريغ جدول كشف الاستلامات', 'info');
            }
        }
        
        // حفظ تلقائي
        function autoSave() {
            localStorage.setItem('patrolReceiptData', JSON.stringify(patrolData));
        }
        
        // حفظ البيانات
        function savePatrolData() {
            const dataToSave = {
                ...patrolData,
                dayName: document.getElementById('dayName').value,
                hijriDate: document.getElementById('hijriDate').value,
                gregorianDate: document.getElementById('gregorianDate').value,
                receiptNumber: document.getElementById('receiptNumber').value,
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('patrolReceiptData', JSON.stringify(dataToSave));
            addToLog('تم حفظ بيانات كشف الاستلامات', 'success');
        }
        
        // تحميل البيانات
        function loadPatrolData() {
            const savedData = localStorage.getItem('patrolReceiptData');
            if (savedData) {
                const data = JSON.parse(savedData);
                patrolData = {
                    headers: data.headers || patrolData.headers,
                    rows: data.rows || []
                };
                
                if (data.dayName) document.getElementById('dayName').value = data.dayName;
                if (data.hijriDate) document.getElementById('hijriDate').value = data.hijriDate;
                if (data.gregorianDate) document.getElementById('gregorianDate').value = data.gregorianDate;
                if (data.receiptNumber) document.getElementById('receiptNumber').value = data.receiptNumber;
                
                generatePatrolTable();
                addToLog('تم تحميل بيانات كشف الاستلامات المحفوظة', 'success');
            } else {
                addToLog('لا توجد بيانات محفوظة', 'info');
            }
        }
        
        // طباعة الكشف
        function printPatrolTable() {
            window.print();
            addToLog('تم إرسال الكشف للطباعة', 'info');
        }
        
        // تصدير إلى Excel
        function exportPatrolToExcel() {
            // محاكاة التصدير
            const data = JSON.stringify(patrolData, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `كشف_الاستلامات_${new Date().toISOString().slice(0, 10)}.json`;
            a.click();
            URL.revokeObjectURL(url);
            addToLog('تم تصدير كشف الاستلامات', 'success');
        }
        
        // إعادة تعيين الكل
        function resetAll() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                localStorage.removeItem('patrolReceiptData');
                patrolData = {
                    headers: ['الرقم', 'ملاحظات', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات إضافية'],
                    rows: []
                };
                initializePatrolTable();
                addToLog('تم إعادة تعيين جميع البيانات', 'info');
            }
        }
        
        // تهيئة التاريخ الحالي
        function initializeDate() {
            const today = new Date();
            document.getElementById('gregorianDate').value = today.toISOString().split('T')[0];
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addToLog('بدء تهيئة صفحة كشف الاستلامات', 'info');
            initializeDate();
            initializePatrolTable();
            loadPatrolData(); // تحميل البيانات المحفوظة إن وجدت
            addToLog('تم تحميل صفحة كشف الاستلامات بنجاح', 'success');
        });
    </script>
</body>
</html>
