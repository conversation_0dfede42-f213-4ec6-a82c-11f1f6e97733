{% extends "base.html" %}

{% block title %}تفاصيل الصنف - {{ item.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-info-circle text-primary"></i>
                    تفاصيل الصنف
                </h2>
                <div>
                    <a href="{{ url_for('standalone_inventory.edit_item', item_id=item.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> تعديل الصنف
                    </a>
                    <button class="btn btn-danger" onclick="confirmDelete({{ item.id }}, '{{ item.name }}')">
                        <i class="fas fa-trash"></i> حذف الصنف
                    </button>
                    <a href="{{ url_for('standalone_inventory.items') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                    <a href="{{ url_for('standalone_inventory.add_item') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus"></i> إضافة صنف جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Item Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box"></i>
                        معلومات الصنف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">كود الصنف:</label>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary fs-6 me-2">{{ item.item_code }}</span>
                                <button class="btn btn-sm btn-outline-secondary" 
                                        onclick="copyToClipboard('{{ item.item_code }}')" title="نسخ الكود">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">اسم الصنف:</label>
                            <p class="mb-0">{{ item.name }}</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">الفئة:</label>
                            <div>
                                <span class="badge 
                                    {% if item.category == 'ملبوسات' %}bg-success
                                    {% elif item.category == 'معدات' %}bg-info
                                    {% elif item.category == 'ذخيرة' %}bg-danger
                                    {% else %}bg-secondary{% endif %} fs-6">
                                    {{ item.category }}
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">الفئة الفرعية:</label>
                            <p class="mb-0">{{ item.subcategory or '-' }}</p>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">الكمية المتوفرة:</label>
                            <p class="mb-0">
                                <span class="badge bg-primary fs-6">{{ item.quantity_in_stock }}</span>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">المقاس:</label>
                            <p class="mb-0">{{ item.size or '-' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">اللون:</label>
                            <p class="mb-0">{{ item.color or '-' }}</p>
                        </div>
                    </div>

                    {% if item.notes %}
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">ملاحظات:</label>
                            <div class="alert alert-light">
                                {{ item.notes }}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تاريخ الإضافة:</label>
                            <p class="mb-0">{{ item.created_at | format_saudi_time }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">آخر تحديث:</label>
                            <p class="mb-0">{{ item.updated_at | format_saudi_time }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="copyToClipboard('{{ item.item_code }}')">
                            <i class="fas fa-copy"></i> نسخ كود الصنف
                        </button>
                        <button class="btn btn-outline-info" onclick="printItemDetails()">
                            <i class="fas fa-print"></i> طباعة التفاصيل
                        </button>
                        <a href="{{ url_for('standalone_inventory.items', search=item.category) }}" 
                           class="btn btn-outline-secondary">
                            <i class="fas fa-list"></i> أصناف مشابهة
                        </a>
                    </div>
                </div>
            </div>

            <!-- Barcode Display -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-barcode"></i>
                        الباركود
                    </h6>
                </div>
                <div class="card-body text-center">
                    <canvas id="barcode" class="mb-3"></canvas>
                    <div id="barcode-fallback" class="mb-3" style="display: none;">
                        <div class="alert alert-warning">لا يمكن إنشاء الباركود</div>
                    </div>
                    <small class="text-muted">{{ item.item_code }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Similar Items -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-search"></i>
                        أصناف مشابهة ({{ item.category }})
                    </h6>
                </div>
                <div class="card-body">
                    <div id="similar-items">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- JsBarcode Library for generating barcodes -->
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>

<script>
$(document).ready(function() {
    // إنشاء الباركود
    try {
        // التحقق من وجود مكتبة JsBarcode
        if (typeof JsBarcode !== 'undefined') {
            JsBarcode("#barcode", "{{ item.item_code }}", {
                format: "CODE128",
                width: 2,
                height: 100,
                displayValue: false,
                background: "#ffffff",
                lineColor: "#000000"
            });
            console.log('✅ تم إنشاء الباركود بنجاح للكود: {{ item.item_code }}');
        } else {
            throw new Error('مكتبة JsBarcode غير متوفرة');
        }
    } catch (e) {
        console.error('❌ خطأ في إنشاء الباركود:', e);
        $('#barcode').hide();
        $('#barcode-fallback').show();
    }

    // تحميل الأصناف المشابهة
    loadSimilarItems();
});

// نسخ النص إلى الحافظة
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('تم نسخ الكود: ' + text, 'success');
    }, function(err) {
        console.error('Could not copy text: ', err);
        showToast('فشل في نسخ الكود', 'error');
    });
}

// طباعة تفاصيل الصنف
function printItemDetails() {
    var printContent = `
        <div style="text-align: center; font-family: Arial, sans-serif;">
            <h2>تفاصيل الصنف</h2>
            <hr>
            <table style="width: 100%; border-collapse: collapse;">
                <tr><td style="font-weight: bold;">كود الصنف:</td><td>{{ item.item_code }}</td></tr>
                <tr><td style="font-weight: bold;">اسم الصنف:</td><td>{{ item.name }}</td></tr>
                <tr><td style="font-weight: bold;">الفئة:</td><td>{{ item.category }}</td></tr>
                <tr><td style="font-weight: bold;">الفئة الفرعية:</td><td>{{ item.subcategory or '-' }}</td></tr>
                <tr><td style="font-weight: bold;">المقاس:</td><td>{{ item.size or '-' }}</td></tr>
                <tr><td style="font-weight: bold;">اللون:</td><td>{{ item.color or '-' }}</td></tr>
                <tr><td style="font-weight: bold;">الكمية:</td><td>{{ item.quantity_in_stock }}</td></tr>
                {% if item.notes %}<tr><td style="font-weight: bold;">ملاحظات:</td><td>{{ item.notes }}</td></tr>{% endif %}
            </table>
            <div style="margin-top: 20px;">
                <div id="print-barcode"></div>
            </div>
        </div>
    `;
    
    var printWindow = window.open('', '_blank');
    printWindow.document.write('<html><head><title>طباعة تفاصيل الصنف</title></head><body>');
    printWindow.document.write(printContent);
    printWindow.document.write('</body></html>');
    printWindow.document.close();
    
    // إضافة الباركود للطباعة
    printWindow.onload = function() {
        try {
            JsBarcode(printWindow.document.getElementById("print-barcode"), "{{ item.item_code }}", {
                format: "CODE128",
                width: 2,
                height: 100,
                displayValue: true
            });
        } catch (e) {
            console.log('Could not generate barcode for print');
        }
        printWindow.print();
    };
}

// تحميل الأصناف المشابهة
function loadSimilarItems() {
    $.get('{{ url_for("standalone_inventory.items") }}', {
        category: '{{ item.category }}',
        format: 'json'
    })
    .done(function(data) {
        // هذا مجرد مثال - يمكن تحسينه لاحقاً
        $('#similar-items').html('<p class="text-muted">يمكنك <a href="{{ url_for("standalone_inventory.items", category=item.category) }}">عرض جميع أصناف {{ item.category }}</a></p>');
    })
    .fail(function() {
        $('#similar-items').html('<p class="text-muted">لا يمكن تحميل الأصناف المشابهة</p>');
    });
}

// تأكيد الحذف
function confirmDelete(itemId, itemName) {
    if (confirm('هل أنت متأكد من حذف الصنف "' + itemName + '"؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // إنشاء نموذج مخفي لإرسال طلب الحذف
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("standalone_inventory.delete_item", item_id=0) }}'.replace('0', itemId);

        // إضافة CSRF token إذا كان متوفراً
        var csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            var csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// إظهار رسالة Toast
function showToast(message, type) {
    var toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
    var toastHtml = '<div class="toast align-items-center text-white ' + toastClass + ' border-0" role="alert">' +
                   '<div class="d-flex">' +
                   '<div class="toast-body">' + message + '</div>' +
                   '<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>' +
                   '</div></div>';

    if ($('#toast-container').length === 0) {
        $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }

    $('#toast-container').append(toastHtml);
    var toast = new bootstrap.Toast($('.toast').last());
    toast.show();
}
</script>
{% endblock %}
