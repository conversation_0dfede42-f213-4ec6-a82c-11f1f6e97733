"""Add temporary data tables to PostgreSQL

Revision ID: add_temp_data_tables
Revises: 8727fcabb738
Create Date: 2025-07-14 23:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_temp_data_tables'
down_revision = '8727fcabb738'
branch_labels = None
depends_on = None


def upgrade():
    """إضافة الجداول المؤقتة إلى PostgreSQL"""
    
    # إنشاء جدول receipt_data
    op.create_table('receipt_data',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('receipt_data', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # إنشاء جدول receipt_locations
    op.create_table('receipt_locations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('row_index', sa.Integer(), nullable=False),
        sa.Column('location_id', sa.String(length=50), nullable=False),
        sa.Column('timestamp', sa.String(length=50), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # إنشاء جدول patrol_data
    op.create_table('patrol_data',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('patrol_data', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # إنشاء جدول patrol_locations
    op.create_table('patrol_locations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('row_index', sa.Integer(), nullable=False),
        sa.Column('location_id', sa.String(length=50), nullable=False),
        sa.Column('timestamp', sa.String(length=50), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # إنشاء جدول shifts_data
    op.create_table('shifts_data',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('shifts_data', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # إنشاء جدول shifts_locations
    op.create_table('shifts_locations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('row_index', sa.Integer(), nullable=False),
        sa.Column('location_id', sa.String(length=50), nullable=False),
        sa.Column('timestamp', sa.String(length=50), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # إنشاء فهارس للبحث السريع
    op.create_index('idx_receipt_locations_row', 'receipt_locations', ['row_index'])
    op.create_index('idx_patrol_locations_row', 'patrol_locations', ['row_index'])
    op.create_index('idx_shifts_locations_row', 'shifts_locations', ['row_index'])


def downgrade():
    """حذف الجداول المؤقتة"""
    
    # حذف الفهارس
    op.drop_index('idx_shifts_locations_row', table_name='shifts_locations')
    op.drop_index('idx_patrol_locations_row', table_name='patrol_locations')
    op.drop_index('idx_receipt_locations_row', table_name='receipt_locations')
    
    # حذف الجداول
    op.drop_table('shifts_locations')
    op.drop_table('shifts_data')
    op.drop_table('patrol_locations')
    op.drop_table('patrol_data')
    op.drop_table('receipt_locations')
    op.drop_table('receipt_data')
