{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            تفاصيل الجرد: {{ audit.audit_date.strftime('%Y-%m-%d') }}
        </h3>
    </div>
    <div class="col-md-4 text-right">
        {% if audit.status == 'in-progress' and (current_user.is_admin_role or current_user.is_warehouse_manager) %}
        <form action="{{ url_for('inventory.complete_audit', audit_id=audit.id) }}" method="post"
            style="display: inline;">
            {{ form.csrf_token }}
            <button type="submit" class="btn btn-success">
                <i class="fas fa-check"></i> إنهاء الجرد
            </button>
        </form>
        <form action="{{ url_for('inventory.cancel_audit', audit_id=audit.id) }}" method="post"
            style="display: inline;">
            {{ form.csrf_token }}
            <button type="submit" class="btn btn-danger ml-2">
                <i class="fas fa-times"></i> إلغاء الجرد
            </button>
        </form>
        {% endif %}
        <a href="{{ url_for('inventory.audits') }}" class="btn btn-outline-secondary ml-2">
            <i class="fas fa-arrow-right"></i> العودة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> معلومات الجرد
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tr>
                        <th style="width: 30%">المستودع</th>
                        <td>{{ audit.warehouse.name }}</td>
                    </tr>
                    <tr>
                        <th>تاريخ الجرد</th>
                        <td>{{ audit.audit_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <th>الحالة</th>
                        <td>
                            {% if audit.status == 'in-progress' %}
                            <span class="badge badge-warning">قيد التنفيذ</span>
                            {% elif audit.status == 'completed' %}
                            <span class="badge badge-success">مكتمل</span>
                            {% elif audit.status == 'cancelled' %}
                            <span class="badge badge-danger">ملغي</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>تاريخ الإنشاء</th>
                        <td>{{ audit.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    <tr>
                        <th>المستخدم</th>
                        <td>{{ audit.user.username }}</td>
                    </tr>
                    <tr>
                        <th>تاريخ الاكتمال</th>
                        <td>{% if audit.completed_at %}{{ audit.completed_at.strftime('%Y-%m-%d %H:%M') }}{% else %}-{% endif %}</td>
                    </tr>
                    {% if audit.status == 'completed' %}
                    <tr>
                        <th>تاريخ الجرد القادم (تقديري)</th>
                        <td>
                            <span class="badge badge-info">{{ next_audit_date.strftime('%Y-%m-%d') }}</span>
                            {% if days_remaining > 0 %}
                                <small class="text-muted">(متبقي {{ days_remaining }} يوم)</small>
                            {% elif days_remaining == 0 %}
                                <span class="badge badge-warning">اليوم</span>
                            {% else %}
                                <span class="badge badge-danger">متأخر {{ days_remaining|abs }} يوم</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clipboard-list"></i> الوصف والملاحظات
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-muted">الوصف</h6>
                    <p>{{ audit.description }}</p>
                </div>
                <div>
                    <h6 class="text-muted">ملاحظات</h6>
                    <p>{{ audit.notes or 'لا توجد ملاحظات' }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> ملخص الجرد
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col">
                        <div class="display-4 mb-0">{{ audit_items|length }}</div>
                        <div class="text-muted">إجمالي العناصر</div>
                    </div>
                    <div class="col">
                        <div class="display-4 mb-0 text-success">{{ found_count }}</div>
                        <div class="text-muted">سليم</div>
                    </div>
                    <div class="col">
                        <div class="display-4 mb-0 text-danger">{{ missing_count }}</div>
                        <div class="text-muted">مفقود</div>
                    </div>
                    <div class="col">
                        <div class="display-4 mb-0" style="color: #FAAFBE;">{{ damaged_count }}</div>
                        <div class="text-muted">صيانة</div>
                    </div>
                </div>

                <div class="chart-container mt-4" style="position: relative; height: 200px;">
                    <canvas id="auditSummaryChart"></canvas>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> العناصر التي تم جردها
                </h5>
                <span class="badge badge-primary">{{ audit_items|length }} / {{ total_weapons }}</span>
            </div>
            <div class="card-body p-0">
                <div class="progress" style="height: 5px; border-radius: 0;">
                    <div class="progress-bar bg-primary" role="progressbar"
                        style="width: {{ (audit_items|length / total_weapons * 100) if total_weapons > 0 else 0 }}%"
                        aria-valuenow="{{ audit_items|length }}" aria-valuemin="0" aria-valuemax="{{ total_weapons }}">
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">رقم الحفظ</th>
                                <th scope="col">الرقم التسلسلي</th>
                                <th scope="col">الاسم</th>
                                <th scope="col">رقم السلاح</th>
                                <th scope="col">الفرد المرتبط</th>
                                <th scope="col">الحالة</th>
                                <th scope="col">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in audit_items %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ item.weapon.weapon_number or '-' }}</td>
                                <td>{{ item.weapon.serial_number }}</td>
                                <td>{{ item.weapon.name }}</td>
                                <td>
                                    {% if item.weapon.type == 'pistol' %}
                                    مسدس
                                    {% elif item.weapon.type == 'rifle' %}
                                    بندقية
                                    {% elif item.weapon.type == 'sniper' %}
                                    قناص
                                    {% elif item.weapon.type == 'machine_gun' %}
                                    رشاش
                                    {% else %}
                                    {{ item.weapon.type }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.weapon.personnel %}
                                        {% for person in item.weapon.personnel %}
                                            <span class="badge badge-info">{{ person.name }} ({{ person.personnel_id }})</span>
                                            {% if not loop.last %}<br>{% endif %}
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.status == 'found' %}
                                    <span class="badge badge-success">سليم</span>
                                    {% elif item.status == 'missing' %}
                                    <span class="badge badge-danger">مفقود</span>
                                    {% elif item.status == 'damaged' %}
                                    <span class="badge" style="background-color: #FAAFBE;">صيانة</span>
                                    {% if item.maintenance_reason %}
                                    <i class="fas fa-info-circle ml-1" data-toggle="tooltip" title="{{ item.maintenance_reason }}"></i>
                                    {% endif %}
                                    {% if item.maintenance_record %}
                                    <i class="fas fa-tools ml-1" data-toggle="tooltip" title="تم إنشاء سجل صيانة"></i>
                                    {% endif %}
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('weapons.details', weapon_id=item.weapon.id) }}"
                                        class="btn btn-sm btn-outline-info" title="عرض تفاصيل السلاح">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if audit.status == 'in-progress' and (current_user.is_admin_role or current_user.is_warehouse_manager) %}
                                    <a href="{{ url_for('inventory.audit_weapon', audit_id=audit.id, weapon_id=item.weapon.id) }}"
                                        class="btn btn-sm btn-outline-primary" title="تعديل حالة الجرد">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center py-3">
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle"></i> لا توجد عناصر مجرودة حتى الآن
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% if audit.status == 'in-progress' and (current_user.is_admin_role or current_user.is_warehouse_manager) %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt"></i> الأسلحة المتبقية للجرد
                </h5>
                <span class="badge badge-secondary">{{ remaining_weapons|length }}</span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">رقم الحفظ</th>
                                <th scope="col">الرقم التسلسلي</th>
                                <th scope="col">الاسم</th>
                                <th scope="col">رقم السلاح</th>
                                <th scope="col">الفرد المرتبط</th>
                                <th scope="col">الحالة</th>
                                <th scope="col">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for weapon in remaining_weapons %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ weapon.weapon_number or '-' }}</td>
                                <td>{{ weapon.serial_number }}</td>
                                <td>{{ weapon.name }}</td>
                                <td>
                                    {% if weapon.type == 'pistol' %}
                                    مسدس
                                    {% elif weapon.type == 'rifle' %}
                                    بندقية
                                    {% elif weapon.type == 'sniper' %}
                                    قناص
                                    {% elif weapon.type == 'machine_gun' %}
                                    رشاش
                                    {% else %}
                                    {{ weapon.type }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if weapon.personnel %}
                                        {% for person in weapon.personnel %}
                                            <span class="badge badge-info">{{ person.name }} ({{ person.personnel_id }})</span>
                                            {% if not loop.last %}<br>{% endif %}
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if weapon.status == 'نشط' %}
                                    <span class="badge badge-success">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'إجازة' %}
                                    <span class="badge badge-warning">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'مهمة' %}
                                    <span class="badge badge-mission">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'صيانة' %}
                                    <span class="badge" style="background-color: #FAAFBE;">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'تالف' %}
                                    <span class="badge" style="background-color: #FAAFBE;">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'دورة' %}
                                    <span class="badge badge-danger">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'شاغر' %}
                                    <span class="badge badge-dark">{{ weapon.status }}</span>
                                    {% else %}
                                    <span class="badge badge-secondary">{{ weapon.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('weapons.details', weapon_id=weapon.id) }}"
                                        class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('inventory.audit_weapon', audit_id=audit.id, weapon_id=weapon.id) }}"
                                        class="btn btn-sm btn-outline-primary" title="إضافة للجرد">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center py-3">
                                    <div class="alert alert-success mb-0">
                                        <i class="fas fa-check-circle"></i> تم جرد جميع الأسلحة في المستودع
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // تفعيل tooltips
        $('[data-toggle="tooltip"]').tooltip();

        // Create summary chart
        const ctx = document.getElementById('auditSummaryChart').getContext('2d');
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['سليم', 'مفقود', 'صيانة'],
                datasets: [{
                    data: [{{ found_count }}, {{ missing_count }}, {{ damaged_count }}],
                    backgroundColor: [
                        '#28a745', // سليم
                        '#dc3545', // مفقود
                        '#FAAFBE'  // صيانة
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Cairo, Tajawal, sans-serif'
                            },
                            color: document.body.classList.contains('light-theme') ? '#212529' : '#f0f0f0'
                        }
                    }
                }
            }
        });

        {% if audit.status == 'completed' %}
        // إضافة تذكير بالجرد القادم إذا كان قريبًا (أقل من 7 أيام)
        {% if days_remaining >= 0 and days_remaining <= 7 %}
        // إظهار تنبيه بالجرد القادم
        const alertHtml = `
            <div class="alert alert-warning alert-dismissible fade show mt-3" role="alert">
                <strong>تنبيه!</strong> الجرد القادم بعد {{ days_remaining }} يوم{% if days_remaining == 0 %} (اليوم){% endif %}.
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
        // استخدام querySelector مع نص محدد
        const summaryHeader = document.querySelector('.card-header');
        if (summaryHeader) {
            summaryHeader.closest('.card').insertAdjacentHTML('beforebegin', alertHtml);
        }
        {% endif %}
        {% endif %}
    });
</script>
{% endblock %}
