#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import psycopg2
import os
from datetime import datetime

def restore_location_equipment():
    """استعادة معدات المواقع من ملف locations.db إلى قاعدة البيانات الرئيسية"""
    
    # مسار ملف النسخة الاحتياطية
    backup_db_path = r'C:\Users\<USER>\Desktop\iMQS1\locations.db'
    
    if not os.path.exists(backup_db_path):
        print(f"❌ ملف النسخة الاحتياطية غير موجود: {backup_db_path}")
        return False
    
    try:
        # الاتصال بملف SQLite
        sqlite_conn = sqlite3.connect(backup_db_path)
        sqlite_cursor = sqlite_conn.cursor()
        
        # الاتصال بقاعدة البيانات الرئيسية PostgreSQL
        pg_conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        pg_cursor = pg_conn.cursor()
        
        print("✅ تم الاتصال بقواعد البيانات بنجاح")
        print("=" * 60)
        
        # التحقق من وجود جدول location_equipment في PostgreSQL
        pg_cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'location_equipment'
            )
        """)
        
        table_exists = pg_cursor.fetchone()[0]
        
        if not table_exists:
            print("🔧 إنشاء جدول location_equipment...")
            create_table_query = """
                CREATE TABLE location_equipment (
                    id SERIAL PRIMARY KEY,
                    location_id INTEGER NOT NULL,
                    equipment_name VARCHAR(255) NOT NULL,
                    equipment_type VARCHAR(100),
                    serial_number VARCHAR(100),
                    quantity INTEGER DEFAULT 1,
                    condition_status VARCHAR(50) DEFAULT 'جيد',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE
                )
            """
            pg_cursor.execute(create_table_query)
            print("✅ تم إنشاء جدول location_equipment")
        
        # قراءة المعدات من SQLite
        sqlite_cursor.execute("SELECT * FROM location_equipment ORDER BY id")
        equipment_list = sqlite_cursor.fetchall()
        
        # الحصول على أسماء الأعمدة
        sqlite_cursor.execute("PRAGMA table_info(location_equipment)")
        columns_info = sqlite_cursor.fetchall()
        column_names = [col[1] for col in columns_info]
        
        print(f"📊 تم العثور على {len(equipment_list)} قطعة معدات في النسخة الاحتياطية")
        print(f"📋 الأعمدة: {column_names}")
        print("-" * 60)
        
        # التحقق من المعدات الموجودة في PostgreSQL
        pg_cursor.execute("SELECT COUNT(*) FROM location_equipment")
        existing_count = pg_cursor.fetchone()[0]
        print(f"📊 المعدات الموجودة حالياً في PostgreSQL: {existing_count}")
        
        if existing_count > 0:
            response = input("⚠️  يوجد معدات في قاعدة البيانات. هل تريد حذفها واستبدالها؟ (y/n): ")
            if response.lower() != 'y':
                print("❌ تم إلغاء العملية")
                return False
            
            # حذف المعدات الموجودة
            pg_cursor.execute("DELETE FROM location_equipment")
            print("🗑️  تم حذف المعدات الموجودة")
        
        # إنشاء خريطة لربط معرفات المواقع القديمة بالجديدة
        print("🔗 إنشاء خريطة ربط المواقع...")
        
        # الحصول على المواقع من SQLite
        sqlite_cursor.execute("SELECT id, name, serial_number FROM locations")
        sqlite_locations = sqlite_cursor.fetchall()
        
        # الحصول على المواقع من PostgreSQL
        pg_cursor.execute("SELECT id, name, serial_number FROM locations")
        pg_locations = pg_cursor.fetchall()
        
        # إنشاء خريطة الربط
        location_mapping = {}
        for sqlite_loc in sqlite_locations:
            sqlite_id, sqlite_name, sqlite_serial = sqlite_loc
            
            # البحث عن الموقع المطابق في PostgreSQL
            for pg_loc in pg_locations:
                pg_id, pg_name, pg_serial = pg_loc
                
                if (sqlite_name == pg_name and sqlite_serial == pg_serial) or \
                   (sqlite_name == pg_name and not sqlite_serial and not pg_serial):
                    location_mapping[sqlite_id] = pg_id
                    break
        
        print(f"🔗 تم ربط {len(location_mapping)} موقع")
        
        # نقل المعدات
        restored_count = 0
        failed_count = 0
        skipped_count = 0
        
        for equipment in equipment_list:
            try:
                # تحويل البيانات إلى قاموس
                equipment_dict = dict(zip(column_names, equipment))
                
                # الحصول على معرف الموقع القديم
                old_location_id = equipment_dict.get('location_id')
                
                # التحقق من وجود الموقع في الخريطة
                if old_location_id not in location_mapping:
                    print(f"⚠️  تم تخطي معدة: {equipment_dict.get('equipment_name', 'غير محدد')} - الموقع غير موجود (ID: {old_location_id})")
                    skipped_count += 1
                    continue
                
                # الحصول على معرف الموقع الجديد
                new_location_id = location_mapping[old_location_id]
                
                # تحضير البيانات للإدراج
                equipment_name = equipment_dict.get('equipment_name', '')
                equipment_type = equipment_dict.get('equipment_type', '')
                serial_number = equipment_dict.get('serial_number', '')
                quantity = equipment_dict.get('quantity', 1)
                condition_status = equipment_dict.get('condition_status', 'جيد')
                notes = equipment_dict.get('notes', '')
                
                # تحويل التواريخ
                created_at = equipment_dict.get('created_at', datetime.now().isoformat())
                updated_at = equipment_dict.get('updated_at', datetime.now().isoformat())
                
                # إدراج المعدة في PostgreSQL
                insert_query = """
                    INSERT INTO location_equipment 
                    (location_id, equipment_name, equipment_type, serial_number, 
                     quantity, condition_status, notes, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                """
                
                pg_cursor.execute(insert_query, (
                    new_location_id, equipment_name, equipment_type, serial_number,
                    quantity, condition_status, notes, created_at, updated_at
                ))
                
                new_equipment_id = pg_cursor.fetchone()[0]
                restored_count += 1
                
                if restored_count <= 10:  # عرض أول 10 معدات فقط لتجنب الإزعاج
                    print(f"✅ تم استعادة المعدة: {equipment_name} (ID: {new_equipment_id}) - الموقع: {new_location_id}")
                elif restored_count == 11:
                    print("... (يتم استعادة باقي المعدات)")
                
            except Exception as e:
                failed_count += 1
                print(f"❌ فشل في استعادة المعدة {equipment_dict.get('equipment_name', 'غير محدد')}: {e}")
        
        # حفظ التغييرات
        pg_conn.commit()
        
        print("\n" + "=" * 60)
        print("📊 ملخص العملية:")
        print(f"   ✅ تم استعادة: {restored_count} قطعة معدات")
        print(f"   ❌ فشل في: {failed_count} قطعة معدات")
        print(f"   ⚠️  تم تخطي: {skipped_count} قطعة معدات (مواقع غير موجودة)")
        print(f"   📍 إجمالي المعدات المعالجة: {restored_count + failed_count + skipped_count}")
        
        # التحقق من النتيجة النهائية
        pg_cursor.execute("SELECT COUNT(*) FROM location_equipment")
        total_equipment = pg_cursor.fetchone()[0]
        print(f"   📋 إجمالي المعدات في قاعدة البيانات الآن: {total_equipment}")
        
        # إغلاق الاتصالات
        sqlite_conn.close()
        pg_conn.close()
        
        print("\n🎉 تم استعادة معدات المواقع بنجاح!")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة بيانات SQLite: {e}")
        return False
    except psycopg2.Error as e:
        print(f"❌ خطأ في قاعدة بيانات PostgreSQL: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def verify_equipment_restoration():
    """التحقق من استعادة معدات المواقع"""
    try:
        pg_conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        pg_cursor = pg_conn.cursor()
        
        print("\n🔍 التحقق من معدات المواقع المستعادة:")
        print("=" * 60)
        
        # إحصائيات عامة
        pg_cursor.execute("SELECT COUNT(*) FROM location_equipment")
        total_equipment = pg_cursor.fetchone()[0]
        
        pg_cursor.execute("SELECT COUNT(DISTINCT location_id) FROM location_equipment")
        locations_with_equipment = pg_cursor.fetchone()[0]
        
        print(f"📊 إجمالي المعدات: {total_equipment}")
        print(f"📍 المواقع التي تحتوي على معدات: {locations_with_equipment}")
        print("-" * 60)
        
        # عرض إحصائيات حسب الموقع
        pg_cursor.execute("""
            SELECT l.name, COUNT(le.id) as equipment_count
            FROM locations l
            LEFT JOIN location_equipment le ON l.id = le.location_id
            WHERE le.id IS NOT NULL
            GROUP BY l.id, l.name
            ORDER BY equipment_count DESC
            LIMIT 10
        """)
        
        location_stats = pg_cursor.fetchall()
        
        print("🏆 أكثر 10 مواقع احتواءً على معدات:")
        for i, (location_name, count) in enumerate(location_stats, 1):
            print(f"{i:2d}. {location_name}: {count} قطعة معدات")
        
        # عرض أنواع المعدات
        print(f"\n📋 أنواع المعدات:")
        pg_cursor.execute("""
            SELECT equipment_type, COUNT(*) as count
            FROM location_equipment
            WHERE equipment_type IS NOT NULL AND equipment_type != ''
            GROUP BY equipment_type
            ORDER BY count DESC
        """)
        
        equipment_types = pg_cursor.fetchall()
        for equipment_type, count in equipment_types:
            print(f"   - {equipment_type}: {count} قطعة")
        
        # عرض حالات المعدات
        print(f"\n📊 حالات المعدات:")
        pg_cursor.execute("""
            SELECT condition_status, COUNT(*) as count
            FROM location_equipment
            GROUP BY condition_status
            ORDER BY count DESC
        """)
        
        conditions = pg_cursor.fetchall()
        for condition, count in conditions:
            print(f"   - {condition}: {count} قطعة")
        
        pg_conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

if __name__ == "__main__":
    print("🔄 استعادة معدات المواقع")
    print("=" * 60)
    
    # استعادة المعدات
    success = restore_location_equipment()
    
    if success:
        # التحقق من النتائج
        verify_equipment_restoration()
        
        print("\n" + "=" * 60)
        print("✅ تم استعادة جميع معدات المواقع بنجاح!")
        print("🌐 يمكنك الآن الوصول إلى إدارة المواقع والمعدات في الموقع")
        print("📍 الرابط: http://localhost:5000/locations/")
    else:
        print("\n❌ فشل في استعادة معدات المواقع")
