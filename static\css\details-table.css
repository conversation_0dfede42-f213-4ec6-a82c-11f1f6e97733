/* تنسيقات جدول التفاصيل */
.table-details {
    width: 100%;
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table-details th,
.table-details td {
    padding: 12px 15px;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table-details th {
    width: 30%;
    font-weight: 600;
    color: var(--text-secondary);
    text-align: right;
}

.table-details td {
    width: 70%;
    color: var(--text-primary);
}

.table-details tr:last-child th,
.table-details tr:last-child td {
    border-bottom: none;
}

/* تحسين مظهر الجدول في الوضع الليلي */
body:not(.light-theme) .table-details th {
    background-color: rgba(0, 0, 0, 0.1);
}

/* تحسين مظهر الجدول في الوضع النهاري */
body.light-theme .table-details th {
    background-color: rgba(0, 0, 0, 0.03);
}

/* تأثير التحويم */
.table-details tr:hover td,
.table-details tr:hover th {
    background-color: rgba(var(--accent-color-rgb, 13, 110, 253), 0.05);
}

/* تنسيق الشارات */
.table-details .badge {
    font-size: 0.85rem;
    padding: 0.4em 0.8em;
}

/* تحسين المسافات في الشاشات الصغيرة */
@media (max-width: 768px) {
    .table-details th,
    .table-details td {
        padding: 10px;
    }
    
    .table-details th {
        width: 40%;
    }
    
    .table-details td {
        width: 60%;
    }
}
