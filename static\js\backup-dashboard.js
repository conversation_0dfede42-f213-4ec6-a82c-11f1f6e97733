/**
 * Backup Dashboard JavaScript
 * Handles interactive features for the backup dashboard
 */
document.addEventListener("DOMContentLoaded", function () {
  // تبديل عرض الخيارات المتقدمة
  const advancedToggle = document.getElementById("advanced-options-toggle");
  const advancedOptions = document.getElementById("advanced-options");

  if (advancedToggle && advancedOptions) {
    advancedToggle.addEventListener("click", function () {
      if (advancedOptions.style.display === "none") {
        advancedOptions.style.display = "block";
        advancedToggle.innerHTML = '<i class="fas fa-chevron-up"></i> إخفاء الخيارات المتقدمة';
      } else {
        advancedOptions.style.display = "none";
        advancedToggle.innerHTML = '<i class="fas fa-chevron-down"></i> عرض الخيارات المتقدمة';
      }
    });
  }

  // تحديث حقول الجدولة بناءً على نوع الجدولة المختار
  const scheduleType = document.getElementById("schedule_type");
  const dayOfWeekGroup = document.getElementById("day_of_week_group");
  const dayOfMonthGroup = document.getElementById("day_of_month_group");

  if (scheduleType && dayOfWeekGroup && dayOfMonthGroup) {
    scheduleType.addEventListener("change", function () {
      if (this.value === "weekly") {
        dayOfWeekGroup.style.display = "block";
        dayOfMonthGroup.style.display = "none";
      } else if (this.value === "monthly") {
        dayOfWeekGroup.style.display = "none";
        dayOfMonthGroup.style.display = "block";
      } else {
        dayOfWeekGroup.style.display = "none";
        dayOfMonthGroup.style.display = "none";
      }
    });

    // تطبيق الحالة الأولية
    if (scheduleType.value === "weekly") {
      dayOfWeekGroup.style.display = "block";
      dayOfMonthGroup.style.display = "none";
    } else if (scheduleType.value === "monthly") {
      dayOfWeekGroup.style.display = "none";
      dayOfMonthGroup.style.display = "block";
    } else {
      dayOfWeekGroup.style.display = "none";
      dayOfMonthGroup.style.display = "none";
    }
  }

  // محاكاة شريط التقدم للنسخ الاحتياطي
  const backupButton = document.getElementById("create-backup-btn");
  const backupProgress = document.getElementById("backup-progress");
  const progressBar = document.querySelector("#backup-progress .progress-bar");
  const backupForm = document.getElementById("backup-form");

  // لا نقوم بإضافة مستمع الحدث للزر
  // لنترك السلوك الافتراضي للنموذج يعمل بشكل طبيعي

  // محاكاة شريط التقدم للاستعادة
  const restoreButtons = document.querySelectorAll(".restore-btn");
  const restoreProgress = document.getElementById("restore-progress");
  const restoreProgressBar = document.querySelector("#restore-progress .progress-bar");

  if (restoreButtons.length > 0 && restoreProgress && restoreProgressBar) {
    restoreButtons.forEach(function (button) {
      button.addEventListener("click", function () {
        // التحقق من تأكيد المستخدم قبل الاستعادة
        if (confirm("هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.")) {
          // عرض شريط التقدم
          restoreProgress.style.display = "block";

          // تعطيل جميع أزرار الاستعادة أثناء العملية
          restoreButtons.forEach(btn => btn.disabled = true);

          // محاكاة تقدم عملية الاستعادة
          let progress = 0;
          const interval = setInterval(function () {
            progress += 4;
            restoreProgressBar.style.width = progress + "%";
            restoreProgressBar.setAttribute("aria-valuenow", progress);

            if (progress >= 100) {
              clearInterval(interval);
              // إعادة تفعيل الأزرار بعد اكتمال العملية
              restoreButtons.forEach(btn => btn.disabled = false);

              // إخفاء شريط التقدم بعد فترة قصيرة
              setTimeout(function () {
                restoreProgress.style.display = "none";
                // إعادة تعيين شريط التقدم
                restoreProgressBar.style.width = "0%";
                restoreProgressBar.setAttribute("aria-valuenow", 0);
              }, 1000);
            }
          }, 100);
        }
      });
    });
  }

  // تحديث حجم النسخة الاحتياطية المقدر بناءً على الخيارات المحددة
  const backupOptions = document.querySelectorAll(".backup-option");
  const estimatedSize = document.getElementById("estimated-size");

  if (backupOptions.length > 0 && estimatedSize) {
    backupOptions.forEach(function (option) {
      option.addEventListener("change", updateEstimatedSize);
    });

    function updateEstimatedSize() {
      // حساب الحجم المقدر بناءً على الخيارات المحددة
      let baseSize = 2; // الحجم الأساسي بالميجابايت

      // زيادة الحجم إذا تم تحديد خيارات إضافية
      backupOptions.forEach(function (option) {
        if (option.checked) {
          if (option.id === "option-weapons") baseSize += 0.5;
          if (option.id === "option-personnel") baseSize += 0.3;
          if (option.id === "option-transactions") baseSize += 1.2;
          if (option.id === "option-logs") baseSize += 0.8;
        }
      });

      // تقليل الحجم إذا تم تحديد خيار الضغط
      const compressionOption = document.getElementById("option-compression");
      if (compressionOption && compressionOption.checked) {
        baseSize = baseSize * 0.6; // تقليل الحجم بنسبة 40%
      }

      // تحديث النص
      estimatedSize.textContent = baseSize.toFixed(1) + " ميجابايت";
    }

    // تطبيق التقدير الأولي
    updateEstimatedSize();
  }

  // تحديث عدد النسخ الاحتياطية المحتفظ بها
  const retentionSlider = document.getElementById("retention-slider");
  const retentionValue = document.getElementById("retention-value");

  if (retentionSlider && retentionValue) {
    retentionSlider.addEventListener("input", function () {
      retentionValue.textContent = this.value;
    });
  }
});
