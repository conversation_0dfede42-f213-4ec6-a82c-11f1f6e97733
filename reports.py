import os
import pandas as pd
import logging
import os
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)
from flask import Blueprint, render_template, redirect, url_for, flash, request, Response, send_file, current_app, jsonify, make_response
from werkzeug.utils import secure_filename
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SubmitField, SelectField, DateField, BooleanField, IntegerField, TimeField
from wtforms.fields.simple import FileField
from wtforms.validators import DataRequired, Optional, NumberRange

from db import db
from models import Warehouse, Weapon, Personnel, ActivityLog, WeaponTransaction, MaintenanceRecord, Audit, AuditItem, BackupRecord, BackupSchedule, User
from utils import (
    get_warehouse_summary, get_transaction_stats, get_maintenance_stats,
    get_audit_stats, export_to_excel, export_to_pdf,
    get_weapons_excel, get_personnel_excel, get_transactions_excel, get_audit_excel, get_devices_excel, get_maintenance_excel,
    get_device_maintenance_excel, backup_database, restore_database, create_backup_schedule, update_backup_schedule,
    delete_backup_schedule, calculate_next_run, format_datetime_12h, format_time_12h,
    get_hijri_date, get_week_range, format_weekly_report_title, run_scheduled_backups, get_due_backup_schedules
)

# Create the blueprint
reports_bp = Blueprint('reports', __name__, url_prefix='/reports')

# Forms
class ReportForm(FlaskForm):
    report_type = SelectField('نوع التقرير', choices=[
        ('warehouse_summary', 'ملخص المستودع'),
        ('weapons', 'الأسلحة'),
        ('personnel', 'الأفراد'),
        ('transactions', 'المعاملات'),
        ('maintenance', 'سجلات الصيانة'),
        ('audits', 'عمليات الجرد')
    ])
    warehouse_id = SelectField('المستودع', coerce=int)
    start_date = DateField('تاريخ البداية', format='%Y-%m-%d', validators=[Optional()])
    end_date = DateField('تاريخ النهاية', format='%Y-%m-%d', validators=[Optional()])
    include_all_warehouses = BooleanField('تضمين جميع المستودعات')
    export_format = SelectField('صيغة التصدير', choices=[
        ('pdf', 'PDF'),
        ('excel', 'Excel')
    ])
    submit = SubmitField('إنشاء التقرير')

    def __init__(self, *args, **kwargs):
        super(ReportForm, self).__init__(*args, **kwargs)
        # Only show warehouses that the current user has access to
        if current_user.is_authenticated:
            # Si el usuario es administrador, mostrar todos los almacenes
            if current_user.is_admin_role:
                self.warehouse_id.choices = [(w.id, w.name) for w in Warehouse.query.all()]
            else:
                self.warehouse_id.choices = [(w.id, w.name) for w in current_user.warehouses]

# Routes
@reports_bp.route('/')
@login_required
def index():
    # Get warehouses the user has access to
    if current_user.is_admin_role:
        # Si el usuario es administrador, mostrar todos los almacenes
        warehouses = Warehouse.query.all()
    else:
        warehouses = current_user.warehouses

    # Initialize the report form
    form = ReportForm()

    return render_template('reports/index.html',
                          warehouses=warehouses,
                          form=form,
                          title='التقارير')

@reports_bp.route('/generate', methods=['GET', 'POST'])
@login_required
def generate():
    form = ReportForm()

    if form.validate_on_submit():
        report_type = form.report_type.data
        warehouse_id = form.warehouse_id.data if not form.include_all_warehouses.data else None
        start_date = form.start_date.data
        end_date = form.end_date.data
        export_format = form.export_format.data

        # Set end date to today if not provided
        if not end_date:
            end_date = datetime.now().date()

        # Set start date to 30 days before end date if not provided
        if not start_date:
            start_date = end_date - timedelta(days=30)

        # Check if end date is after start date
        if start_date and end_date and start_date > end_date:
            flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'danger')
            return redirect(url_for('reports.index'))

        # Generate the report based on the selected type
        if report_type == 'warehouse_summary':
            return generate_warehouse_summary(warehouse_id, export_format)
        elif report_type == 'weapons':
            return generate_weapons_report(warehouse_id, export_format)
        elif report_type == 'personnel':
            return generate_personnel_report(warehouse_id, export_format)
        elif report_type == 'transactions':
            return generate_transactions_report(warehouse_id, start_date, end_date, export_format)
        elif report_type == 'maintenance':
            return generate_maintenance_report(warehouse_id, start_date, end_date, export_format)
        elif report_type == 'audits':
            return generate_audits_report(warehouse_id, start_date, end_date, export_format)

    return redirect(url_for('reports.index'))

@reports_bp.route('/view/<report_type>/<int:warehouse_id>')
@login_required
def view(report_type, warehouse_id):
    # Check if user has access to this warehouse
    warehouse = Warehouse.query.get_or_404(warehouse_id)
    if not current_user.is_admin_role and warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('reports.index'))

    # Generate the report data based on the selected type
    if report_type == 'warehouse_summary':
        data = get_warehouse_summary(warehouse_id)
        return render_template('reports/view.html',
                            report_type=report_type,
                            warehouse=warehouse,
                            data=data,
                            title=f'ملخص المستودع: {warehouse.name}')

    elif report_type == 'weapons':
        weapons = Weapon.query.filter_by(warehouse_id=warehouse_id).all()
        return render_template('reports/view.html',
                            report_type=report_type,
                            warehouse=warehouse,
                            weapons=weapons,
                            title=f'تقرير الأسلحة: {warehouse.name}')

    elif report_type == 'personnel':
        personnel_list = Personnel.query.filter_by(warehouse_id=warehouse_id).all()
        return render_template('reports/view.html',
                            report_type=report_type,
                            warehouse=warehouse,
                            personnel_list=personnel_list,
                            title=f'تقرير الأفراد: {warehouse.name}')

    elif report_type == 'transactions':
        start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')

        transactions = WeaponTransaction.query.filter(
            (WeaponTransaction.source_warehouse_id == warehouse_id) |
            (WeaponTransaction.target_warehouse_id == warehouse_id)
        )

        if start_date:
            transactions = transactions.filter(WeaponTransaction.timestamp >= start_date)
        if end_date:
            transactions = transactions.filter(WeaponTransaction.timestamp <= end_date)

        transactions = transactions.order_by(WeaponTransaction.timestamp.desc()).all()

        return render_template('reports/view.html',
                            report_type=report_type,
                            warehouse=warehouse,
                            transactions=transactions,
                            start_date=start_date,
                            end_date=end_date,
                            title=f'تقرير المعاملات: {warehouse.name}')

    elif report_type == 'maintenance':
        start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')

        # Get weapon IDs in this warehouse
        weapon_ids = [w.id for w in Weapon.query.filter_by(warehouse_id=warehouse_id).all()]

        maintenance_records = MaintenanceRecord.query.filter(
            MaintenanceRecord.weapon_id.in_(weapon_ids)
        )

        if start_date:
            maintenance_records = maintenance_records.filter(MaintenanceRecord.start_date >= start_date)
        if end_date:
            maintenance_records = maintenance_records.filter(MaintenanceRecord.start_date <= end_date)

        maintenance_records = maintenance_records.order_by(MaintenanceRecord.start_date.desc()).all()

        return render_template('reports/view.html',
                            report_type=report_type,
                            warehouse=warehouse,
                            maintenance_records=maintenance_records,
                            start_date=start_date,
                            end_date=end_date,
                            title=f'تقرير الصيانة: {warehouse.name}')

    elif report_type == 'audits':
        start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')

        audits = Audit.query.filter_by(warehouse_id=warehouse_id)

        if start_date:
            audits = audits.filter(Audit.audit_date >= start_date)
        if end_date:
            audits = audits.filter(Audit.audit_date <= end_date)

        audits = audits.order_by(Audit.audit_date.desc()).all()

        return render_template('reports/view.html',
                            report_type=report_type,
                            warehouse=warehouse,
                            audits=audits,
                            start_date=start_date,
                            end_date=end_date,
                            title=f'تقرير الجرد: {warehouse.name}')

    return redirect(url_for('reports.index'))

@reports_bp.route('/audit/<int:audit_id>')
@login_required
def view_audit(audit_id):
    audit = Audit.query.get_or_404(audit_id)

    # Check if user has access to this warehouse
    if not current_user.is_admin_role and audit.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا الجرد', 'danger')
        return redirect(url_for('reports.index'))

    # Get all items in this audit
    audit_items = AuditItem.query.filter_by(audit_id=audit.id).all()

    # Group items by status
    items_by_status = {
        'found': [],
        'missing': [],
        'damaged': []
    }

    for item in audit_items:
        weapon = Weapon.query.get(item.weapon_id)
        if weapon:
            items_by_status[item.status].append({
                'item': item,
                'weapon': weapon
            })

    return render_template('reports/audit_details.html',
                          audit=audit,
                          items_by_status=items_by_status,
                          title=f'تفاصيل الجرد: {audit.audit_date.strftime("%Y-%m-%d")}')

@reports_bp.route('/export/<export_type>/', defaults={'warehouse_id': 0})
@reports_bp.route('/export/<export_type>/<int:warehouse_id>')
@login_required
def export(export_type, warehouse_id=None):
    """Export data to Excel format."""
    # Log the export request for debugging
    print(f"Debug: Export request - Type: {export_type}, Warehouse: {warehouse_id}")
    print(f"Debug: All request args: {dict(request.args)}")
    print(f"Debug: Request method: {request.method}")
    print(f"Debug: Request URL: {request.url}")

    # Special case for device_maintenance which doesn't need warehouse_id
    if export_type == 'device_maintenance':
        pass  # No need to check warehouse_id for device maintenance
    # Check if warehouse_id is 0, which means all warehouses
    elif warehouse_id == 0:
        warehouse_id = None
    elif warehouse_id:
        # Check if user has access to this warehouse
        warehouse = Warehouse.query.get_or_404(warehouse_id)
        if not current_user.is_admin_role and warehouse not in current_user.warehouses:
            flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
            return redirect(url_for('reports.index'))

    # Get date range if provided
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Get status filter if provided
    status = request.args.get('status')

    if start_date:
        start_date = datetime.strptime(start_date, '%Y-%m-%d')
    if end_date:
        end_date = datetime.strptime(end_date, '%Y-%m-%d')

    # Generate Excel file based on the export type
    try:
        if export_type == 'weapons':
            df = get_weapons_excel(warehouse_id, status)

            # Create report title based on status
            report_title = "تقرير الأسلحة"
            if status:
                report_title += f" - {status}"
                filename = f"weapons_{status}_{datetime.now().strftime('%Y%m%d')}"
            else:
                filename = f"weapons_{datetime.now().strftime('%Y%m%d')}"

            # Check if DataFrame is empty - but still create Excel file
            if df.empty:
                print(f"Debug: No weapons found for export. Status: {status}, Warehouse: {warehouse_id}")
                print("Debug: Creating empty Excel file with 'No data' message")

            # Create Excel file
            excel_file = export_to_excel({'الأسلحة': df}, filename, report_title)

            # Check if Excel file creation failed
            if excel_file is None:
                print(f"Debug: Excel file creation failed for weapons export")
                flash('فشل في إنشاء ملف Excel', 'danger')
                return redirect(url_for('reports.index'))

            # Log the export
            warehouse_name = "جميع المستودعات" if warehouse_id is None else Warehouse.query.get(warehouse_id).name
            # Get first available warehouse for logging if not specified
            default_warehouse_id = Warehouse.query.first().id if warehouse_id is None else warehouse_id

            status_desc = f" بحالة {status}" if status else ""

            log = ActivityLog(
                action="تصدير تقرير",
                description=f"تم تصدير تقرير الأسلحة{status_desc} لـ {warehouse_name}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=default_warehouse_id
            )
            db.session.add(log)
            db.session.commit()

            return send_file(
                excel_file,
                download_name=f"{filename}.xlsx",
                as_attachment=True,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

        elif export_type == 'personnel':
            # تحديد ما إذا كان التقرير أسبوعي
            weekly = request.args.get('weekly', 'false').lower() == 'true'

            # الحصول على التواريخ المخصصة إذا تم تحديدها
            # استخدام التواريخ المرسلة من النموذج أو من المعاملات العامة
            custom_start_date = None
            custom_end_date = None

            if weekly:
                start_date_str = request.args.get('start_date')
                end_date_str = request.args.get('end_date')

                if start_date_str and end_date_str:
                    try:
                        custom_start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                        custom_end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                        print(f"Debug: Using custom dates - Start: {custom_start_date}, End: {custom_end_date}")
                    except ValueError:
                        print(f"Debug: Invalid date format. start_date: {start_date_str}, end_date: {end_date_str}")
                else:
                    print(f"Debug: No custom dates provided. start_date: {start_date_str}, end_date: {end_date_str}")

            df = get_personnel_excel(warehouse_id, status, weekly=weekly, start_date=custom_start_date, end_date=custom_end_date)

            # Create report title based on status and type
            if weekly:
                if custom_start_date and custom_end_date:
                    report_title = f"تقرير تغييرات حالات الأفراد من {custom_start_date.strftime('%Y-%m-%d')} إلى {custom_end_date.strftime('%Y-%m-%d')}"
                    filename = f"personnel_custom_{custom_start_date.strftime('%Y%m%d')}_{custom_end_date.strftime('%Y%m%d')}"
                else:
                    report_title = format_weekly_report_title()
                    filename = f"personnel_weekly_{datetime.now().strftime('%Y%m%d_%H%M')}"

                if status:
                    report_title += f" - {status}"
                    filename += f"_{status}"
            else:
                report_title = "تقرير الأفراد"
                if status:
                    report_title += f" - {status}"
                    filename = f"personnel_{status}_{datetime.now().strftime('%Y%m%d')}"
                else:
                    filename = f"personnel_{datetime.now().strftime('%Y%m%d')}"

            # Check if DataFrame is empty - but still create Excel file
            if df.empty:
                print(f"Debug: No personnel found for export. Status: {status}, Weekly: {weekly}, Warehouse: {warehouse_id}")
                print("Debug: Creating empty Excel file with 'No data' message")

                # Don't replace with current status - keep the original request parameters
                # The user specifically requested a weekly/custom report, so we should honor that

            # Create Excel file
            excel_file = export_to_excel({'الأفراد': df}, filename, report_title)

            # Check if Excel file creation failed
            if excel_file is None:
                print(f"Debug: Excel file creation failed for personnel export")
                flash('فشل في إنشاء ملف Excel', 'danger')
                return redirect(url_for('reports.index'))

            # Log the export
            warehouse_name = "جميع المستودعات" if warehouse_id is None else Warehouse.query.get(warehouse_id).name
            # Get first available warehouse for logging if not specified
            default_warehouse_id = Warehouse.query.first().id if warehouse_id is None else warehouse_id

            status_desc = f" بحالة {status}" if status else ""
            report_type_desc = "الأسبوعي" if weekly else ""

            log = ActivityLog(
                action="تصدير تقرير",
                description=f"تم تصدير تقرير الأفراد {report_type_desc}{status_desc} لـ {warehouse_name}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=default_warehouse_id
            )
            db.session.add(log)
            db.session.commit()

            return send_file(
                excel_file,
                download_name=f"{filename}.xlsx",
                as_attachment=True,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

        elif export_type == 'transactions':
            df = get_transactions_excel(warehouse_id, start_date, end_date)
            filename = f"transactions_{datetime.now().strftime('%Y%m%d')}"

            # Create Excel file
            excel_file = export_to_excel({'المعاملات': df}, filename)

            # Log the export
            warehouse_name = "جميع المستودعات" if warehouse_id is None else Warehouse.query.get(warehouse_id).name
            # Get first available warehouse for logging if not specified
            default_warehouse_id = Warehouse.query.first().id if warehouse_id is None else warehouse_id

            log = ActivityLog(
                action="تصدير تقرير",
                description=f"تم تصدير تقرير المعاملات لـ {warehouse_name}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=default_warehouse_id
            )
            db.session.add(log)
            db.session.commit()

            return send_file(
                excel_file,
                download_name=f"{filename}.xlsx",
                as_attachment=True,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

        elif export_type == 'audit':
            audit_id = request.args.get('audit_id')
            if not audit_id:
                flash('رقم الجرد مطلوب', 'danger')
                return redirect(url_for('reports.index'))

            df = get_audit_excel(audit_id)
            if df is None:
                flash('الجرد غير موجود', 'danger')
                return redirect(url_for('reports.index'))

            audit = Audit.query.get(audit_id)
            filename = f"audit_{audit.audit_date.strftime('%Y%m%d')}"

            # Create Excel file
            excel_file = export_to_excel({'عناصر الجرد': df}, filename, "تقرير الجرد")

            # Log the export
            log = ActivityLog(
                action="تصدير تقرير",
                description=f"تم تصدير تقرير الجرد لتاريخ {audit.audit_date.strftime('%Y-%m-%d')}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=audit.warehouse_id
            )
            db.session.add(log)
            db.session.commit()

            return send_file(
                excel_file,
                download_name=f"{filename}.xlsx",
                as_attachment=True,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

        elif export_type == 'devices':
            df = get_devices_excel(warehouse_id)
            filename = f"devices_{datetime.now().strftime('%Y%m%d')}"

            # Create Excel file
            excel_file = export_to_excel({'الأجهزة': df}, filename, "تقرير الأجهزة")

            # Check if Excel file creation failed
            if excel_file is None:
                flash('فشل في إنشاء ملف Excel', 'danger')
                return redirect(url_for('reports.index'))

            # Log the export
            warehouse_name = "جميع المستودعات" if warehouse_id is None else Warehouse.query.get(warehouse_id).name
            # Get first available warehouse for logging if not specified
            default_warehouse_id = Warehouse.query.first().id if warehouse_id is None else warehouse_id

            log = ActivityLog(
                action="تصدير تقرير",
                description=f"تم تصدير تقرير الأجهزة لـ {warehouse_name}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=default_warehouse_id
            )
            db.session.add(log)
            db.session.commit()

            return send_file(
                excel_file,
                download_name=f"{filename}.xlsx",
                as_attachment=True,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

        elif export_type == 'maintenance':
            df = get_maintenance_excel(warehouse_id)
            filename = f"maintenance_{datetime.now().strftime('%Y%m%d')}"

            # Check if DataFrame is empty - but still create Excel file
            if df.empty:
                print("Debug: No maintenance records found, creating empty Excel file")

            # Create Excel file
            excel_file = export_to_excel({'سجلات الصيانة': df}, filename, "تقرير الصيانة")

            # Log the export
            warehouse_name = "جميع المستودعات" if warehouse_id is None else Warehouse.query.get(warehouse_id).name
            # Get first available warehouse for logging if not specified
            default_warehouse_id = Warehouse.query.first().id if warehouse_id is None else warehouse_id

            log = ActivityLog(
                action="تصدير تقرير",
                description=f"تم تصدير تقرير الإصلاحات لـ {warehouse_name}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=default_warehouse_id
            )
            db.session.add(log)
            db.session.commit()

            return send_file(
                excel_file,
                download_name=f"{filename}.xlsx",
                as_attachment=True,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

        elif export_type == 'device_maintenance':
            df = get_device_maintenance_excel()
            filename = f"device_maintenance_{datetime.now().strftime('%Y%m%d')}"

            # Check if DataFrame is empty - but still create Excel file
            if df.empty:
                print("Debug: No device maintenance records found, creating empty Excel file")

            # Create Excel file
            excel_file = export_to_excel({'سجلات صيانة الأجهزة': df}, filename, "تقرير صيانة الأجهزة")

            # Log the export
            # Get first available warehouse for logging
            default_warehouse_id = Warehouse.query.first().id

            log = ActivityLog(
                action="تصدير تقرير",
                description="تم تصدير تقرير صيانة الأجهزة",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=default_warehouse_id
            )
            db.session.add(log)
            db.session.commit()

            return send_file(
                excel_file,
                download_name=f"{filename}.xlsx",
                as_attachment=True,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

        else:
            flash('نوع التصدير غير صالح', 'danger')
            return redirect(url_for('reports.index'))

    except Exception as e:
        print(f"خطأ عام في دالة التصدير: {e}")
        print(f"Export type: {export_type}, Warehouse: {warehouse_id}")
        import traceback
        traceback.print_exc()
        flash('حدث خطأ أثناء تصدير التقرير. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('reports.index'))

# Helper functions for report generation
def generate_warehouse_summary(warehouse_id, export_format):
    """Generate and export warehouse summary report."""
    if warehouse_id:
        # Single warehouse report
        warehouse = Warehouse.query.get_or_404(warehouse_id)
        if not current_user.is_admin_role and warehouse not in current_user.warehouses:
            flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
            return redirect(url_for('reports.index'))

        data = get_warehouse_summary(warehouse_id)

        if export_format == 'pdf':
            # Render the report template to HTML
            html_content = render_template('reports/exports/warehouse_summary.html',
                                          data=data,
                                          title=f'ملخص المستودع: {warehouse.name}')

            # Generate PDF from HTML
            pdf_file = export_to_pdf(html_content, f"warehouse_summary_{warehouse_id}")
            if pdf_file:
                # Log the export
                log = ActivityLog(
                    action="تصدير تقرير",
                    description=f"تم تصدير ملخص المستودع: {warehouse.name} بصيغة PDF",
                    ip_address=request.remote_addr,
                    user_id=current_user.id,
                    warehouse_id=warehouse_id
                )
                db.session.add(log)
                db.session.commit()

                return send_file(
                    pdf_file,
                    download_name=f"warehouse_summary_{warehouse_id}.pdf",
                    as_attachment=True,
                    mimetype='application/pdf'
                )
            else:
                flash('فشل إنشاء ملف PDF', 'danger')
                return redirect(url_for('reports.index'))

        elif export_format == 'excel':
            # Prepare Excel data
            summary_data = {
                'القيمة': [
                    data['weapons_count'],
                    data['personnel_count'],
                    data['devices_count'],
                    data['weapon_status'].get('نشط', 0),
                    data['weapon_status'].get('إجازة', 0),
                    data['weapon_status'].get('مهمة', 0),
                    data['weapon_status'].get('صيانة', 0),
                    data['weapon_status'].get('تالف', 0),
                    data['personnel_status'].get('نشط', 0),
                    data['personnel_status'].get('إجازة', 0),
                    data['personnel_status'].get('مهمة', 0),
                    data['personnel_status'].get('متقاعد', 0)
                ]
            }

            summary_df = pd.DataFrame(summary_data, index=[
                'إجمالي الأسلحة',
                'إجمالي الأفراد',
                'إجمالي الأجهزة',
                'أسلحة نشطة',
                'أسلحة في إجازة',
                'أسلحة في مهمة',
                'أسلحة في صيانة',
                'أسلحة تالفة',
                'أفراد نشطين',
                'أفراد في إجازة',
                'أفراد في مهمة',
                'أفراد متقاعدين'
            ])

            # Create Excel file
            filename = f"warehouse_summary_{warehouse_id}"
            excel_file = export_to_excel({'ملخص المستودع': summary_df}, filename)

            # Log the export
            log = ActivityLog(
                action="تصدير تقرير",
                description=f"تم تصدير ملخص المستودع: {warehouse.name} بصيغة Excel",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=warehouse_id
            )
            db.session.add(log)
            db.session.commit()

            return send_file(
                excel_file,
                download_name=f"{filename}.xlsx",
                as_attachment=True,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

    else:
        # All warehouses report
        warehouses = current_user.warehouses
        all_data = {}

        for warehouse in warehouses:
            all_data[warehouse.id] = get_warehouse_summary(warehouse.id)

        if export_format == 'pdf':
            # Render the report template to HTML
            html_content = render_template('reports/exports/all_warehouses_summary.html',
                                          all_data=all_data,
                                          title='ملخص جميع المستودعات')

            # Generate PDF from HTML
            pdf_file = export_to_pdf(html_content, "all_warehouses_summary")
            if pdf_file:
                # Log the export
                log = ActivityLog(
                    action="تصدير تقرير",
                    description=f"تم تصدير ملخص جميع المستودعات بصيغة PDF",
                    ip_address=request.remote_addr,
                    user_id=current_user.id,
                    warehouse_id=warehouses[0].id  # Use the first warehouse
                )
                db.session.add(log)
                db.session.commit()

                return send_file(
                    pdf_file,
                    download_name="all_warehouses_summary.pdf",
                    as_attachment=True,
                    mimetype='application/pdf'
                )
            else:
                flash('فشل إنشاء ملف PDF', 'danger')
                return redirect(url_for('reports.index'))

        elif export_format == 'excel':
            # Prepare Excel data
            summary_data = {
                'المستودع': [],
                'إجمالي الأسلحة': [],
                'إجمالي الأفراد': [],
                'إجمالي الأجهزة': [],
                'أسلحة نشطة': [],
                'أسلحة في إجازة': [],
                'أسلحة في مهمة': [],
                'أسلحة في صيانة': [],
                'أسلحة تالفة': [],
                'أفراد نشطين': [],
                'أفراد في إجازة': [],
                'أفراد في مهمة': [],
                'أفراد متقاعدين': []
            }

            for warehouse_id, data in all_data.items():
                warehouse = Warehouse.query.get(warehouse_id)
                summary_data['المستودع'].append(warehouse.name)
                summary_data['إجمالي الأسلحة'].append(data['weapons_count'])
                summary_data['إجمالي الأفراد'].append(data['personnel_count'])
                summary_data['إجمالي الأجهزة'].append(data['devices_count'])
                summary_data['أسلحة نشطة'].append(data['weapon_status'].get('نشط', 0))
                summary_data['أسلحة في إجازة'].append(data['weapon_status'].get('إجازة', 0))
                summary_data['أسلحة في مهمة'].append(data['weapon_status'].get('مهمة', 0))
                summary_data['أسلحة في صيانة'].append(data['weapon_status'].get('صيانة', 0))
                summary_data['أسلحة تالفة'].append(data['weapon_status'].get('تالف', 0))
                summary_data['أفراد نشطين'].append(data['personnel_status'].get('نشط', 0))
                summary_data['أفراد في إجازة'].append(data['personnel_status'].get('إجازة', 0))
                summary_data['أفراد في مهمة'].append(data['personnel_status'].get('مهمة', 0))
                summary_data['أفراد متقاعدين'].append(data['personnel_status'].get('مستلم', 0))

            summary_df = pd.DataFrame(summary_data)

            # Create Excel file
            filename = "all_warehouses_summary"
            excel_file = export_to_excel({'ملخص المستودعات': summary_df}, filename)

            # Log the export
            log = ActivityLog(
                action="تصدير تقرير",
                description=f"تم تصدير ملخص جميع المستودعات بصيغة Excel",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=warehouses[0].id  # Use the first warehouse
            )
            db.session.add(log)
            db.session.commit()

            return send_file(
                excel_file,
                download_name=f"{filename}.xlsx",
                as_attachment=True,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

    # Default redirect if export fails
    return redirect(url_for('reports.index'))

def generate_weapons_report(warehouse_id, export_format):
    """Generate and export weapons report."""
    return redirect(url_for('reports.export', export_type='weapons', warehouse_id=warehouse_id or 0))

def generate_personnel_report(warehouse_id, export_format):
    """Generate and export personnel report."""
    return redirect(url_for('reports.export', export_type='personnel', warehouse_id=warehouse_id or 0))

def generate_transactions_report(warehouse_id, start_date, end_date, export_format):
    """Generate and export transactions report."""
    start_date_str = start_date.strftime('%Y-%m-%d') if start_date else None
    end_date_str = end_date.strftime('%Y-%m-%d') if end_date else None

    return redirect(url_for('reports.export',
                          export_type='transactions',
                          warehouse_id=warehouse_id or 0,
                          start_date=start_date_str,
                          end_date=end_date_str))

def generate_maintenance_report(warehouse_id, start_date, end_date, export_format):
    """Generate and export maintenance report."""
    # This would be similar to the transactions report
    # For now, we'll redirect to view the report
    start_date_str = start_date.strftime('%Y-%m-%d') if start_date else None
    end_date_str = end_date.strftime('%Y-%m-%d') if end_date else None

    return redirect(url_for('reports.view',
                          report_type='maintenance',
                          warehouse_id=warehouse_id,
                          start_date=start_date_str,
                          end_date=end_date_str))

def generate_audits_report(warehouse_id, start_date, end_date, export_format):
    """Generate and export audits report."""
    # For now, we'll redirect to view the report
    start_date_str = start_date.strftime('%Y-%m-%d') if start_date else None
    end_date_str = end_date.strftime('%Y-%m-%d') if end_date else None

    return redirect(url_for('reports.view',
                          report_type='audits',
                          warehouse_id=warehouse_id,
                          start_date=start_date_str,
                          end_date=end_date_str))

@reports_bp.route('/statistics/<stat_type>')
@login_required
def statistics(stat_type):
    """Show statistical visualizations for various data types"""
    # Get warehouses the user has access to
    if current_user.is_admin_role:
        # Si el usuario es administrador, mostrar todos los almacenes
        user_warehouses = Warehouse.query.all()
    else:
        user_warehouses = current_user.warehouses

    # Initialize data variable
    data = {}

    if stat_type == 'warehouses':
        # Get summary stats for all warehouses user has access to
        warehouse_stats = []

        for warehouse in user_warehouses:
            weapons_count = Weapon.query.filter_by(warehouse_id=warehouse.id).count()
            personnel_count = Personnel.query.filter_by(warehouse_id=warehouse.id).count()

            # Get weapon status counts
            active_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
            maintenance_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='صيانة').count()
            leave_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
            mission_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
            damaged_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='تالف').count()

            # Add to stats
            warehouse_stats.append({
                'name': warehouse.name,
                'weapons_count': weapons_count,
                'personnel_count': personnel_count,
                'weapon_status': {
                    'active': active_weapons,
                    'maintenance': maintenance_weapons,
                    'leave': leave_weapons,
                    'mission': mission_weapons,
                    'damaged': damaged_weapons
                }
            })

        data['warehouse_stats'] = warehouse_stats

    elif stat_type == 'weapons':
        # Get weapon statistics
        weapon_counts_by_warehouse = []
        weapon_counts_by_type = {}
        weapon_counts_by_status = {
            'نشط': 0,
            'صيانة': 0,
            'إجازة': 0,
            'مهمة': 0,
            'تالف': 0,
            'أخرى': 0
        }

        # Get counts by warehouse
        for warehouse in user_warehouses:
            count = Weapon.query.filter_by(warehouse_id=warehouse.id).count()
            weapon_counts_by_warehouse.append({
                'name': warehouse.name,
                'count': count
            })

        # Get counts by type and status
        for warehouse in user_warehouses:
            weapons = Weapon.query.filter_by(warehouse_id=warehouse.id).all()
            for weapon in weapons:
                # Count by type
                if weapon.type not in weapon_counts_by_type:
                    weapon_counts_by_type[weapon.type] = 0
                weapon_counts_by_type[weapon.type] += 1

                # Count by status
                if weapon.status in weapon_counts_by_status:
                    weapon_counts_by_status[weapon.status] += 1

        data['weapon_counts_by_warehouse'] = weapon_counts_by_warehouse
        data['weapon_counts_by_type'] = [{'type': t, 'count': c} for t, c in weapon_counts_by_type.items()]
        data['weapon_counts_by_status'] = [{'status': s, 'count': c} for s, c in weapon_counts_by_status.items()]

    elif stat_type == 'personnel':
        # Get personnel statistics
        personnel_counts_by_warehouse = []
        personnel_counts_by_rank = {}
        personnel_counts_by_status = {
            'نشط': 0,
            'إجازة': 0,
            'مهمة': 0,
            'مستلم': 0
        }

        # Get counts by warehouse
        for warehouse in user_warehouses:
            count = Personnel.query.filter_by(warehouse_id=warehouse.id).count()
            personnel_counts_by_warehouse.append({
                'name': warehouse.name,
                'count': count
            })

        # Get counts by rank and status
        for warehouse in user_warehouses:
            personnel_list = Personnel.query.filter_by(warehouse_id=warehouse.id).all()
            for person in personnel_list:
                # Count by rank
                if person.rank and person.rank not in personnel_counts_by_rank:
                    personnel_counts_by_rank[person.rank] = 0
                if person.rank:
                    personnel_counts_by_rank[person.rank] += 1

                # Count by status
                if person.status in personnel_counts_by_status:
                    personnel_counts_by_status[person.status] += 1

        data['personnel_counts_by_warehouse'] = personnel_counts_by_warehouse
        data['personnel_counts_by_rank'] = [{'rank': r, 'count': c} for r, c in personnel_counts_by_rank.items()]
        data['personnel_counts_by_status'] = [{'status': s, 'count': c} for s, c in personnel_counts_by_status.items()]

    elif stat_type == 'devices':
        # Get device statistics
        from models import Device

        # تعريف أنواع الأجهزة وحالاتها
        device_counts_by_type = {
            'computer': 0,
            'laptop': 0,
            'printer': 0,
            'scanner': 0,
            'monitor': 0,
            'projector': 0,
            'server': 0,
            'network': 0,
            'camera': 0,
            'storage': 0,
            'ups': 0,
            'other': 0
        }

        device_counts_by_status = {
            'سليم': 0,
            'عطل بسيط': 0,
            'عطل جسيم': 0,
            'تحت الصيانة': 0,
            'خارج الخدمة': 0,
            'مفقود': 0
        }

        # الحصول على جميع الأجهزة بغض النظر عن المستودع
        devices = Device.query.all()
        total_devices = len(devices)

        # إحصائيات حسب الموقع (بدلاً من المستودع)
        device_locations = {}

        # حساب الإحصائيات
        for device in devices:
            # حساب حسب النوع
            if device.type in device_counts_by_type:
                device_counts_by_type[device.type] += 1
            else:
                device_counts_by_type['other'] += 1

            # حساب حسب الحالة
            if device.status in device_counts_by_status:
                device_counts_by_status[device.status] += 1

            # حساب حسب الموقع
            location = device.device_location if hasattr(device, 'device_location') and device.device_location else "غير محدد"
            if location not in device_locations:
                device_locations[location] = 0
            device_locations[location] += 1

        # تحويل إحصائيات المواقع إلى قائمة
        device_counts_by_location = [{'name': loc, 'count': count} for loc, count in device_locations.items()]

        # إضافة إجمالي عدد الأجهزة
        data['total_devices'] = total_devices
        data['device_counts_by_location'] = device_counts_by_location
        data['device_counts_by_type'] = [{'type': t, 'count': c} for t, c in device_counts_by_type.items() if c > 0]
        data['device_counts_by_status'] = [{'status': s, 'count': c} for s, c in device_counts_by_status.items() if c > 0]

    return render_template('reports/statistics.html',
                          stat_type=stat_type,
                          data=data,
                          title=f'إحصائيات {stat_type}')


# Form for backup schedule
class BackupScheduleForm(FlaskForm):
    schedule_type = SelectField('نوع الجدولة', choices=[
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري')
    ], validators=[DataRequired()])

    day_of_week = SelectField('يوم الأسبوع', choices=[
        (0, 'الإثنين'),
        (1, 'الثلاثاء'),
        (2, 'الأربعاء'),
        (3, 'الخميس'),
        (4, 'الجمعة'),
        (5, 'السبت'),
        (6, 'الأحد')
    ], coerce=int, validators=[Optional()])

    day_of_month = IntegerField('يوم الشهر (1-31)', validators=[Optional(), NumberRange(min=1, max=31)])

    hour = SelectField('الساعة', choices=[
        (0, '12 صباحًا'), (1, '1 صباحًا'), (2, '2 صباحًا'), (3, '3 صباحًا'),
        (4, '4 صباحًا'), (5, '5 صباحًا'), (6, '6 صباحًا'), (7, '7 صباحًا'),
        (8, '8 صباحًا'), (9, '9 صباحًا'), (10, '10 صباحًا'), (11, '11 صباحًا'),
        (12, '12 مساءً'), (13, '1 مساءً'), (14, '2 مساءً'), (15, '3 مساءً'),
        (16, '4 مساءً'), (17, '5 مساءً'), (18, '6 مساءً'), (19, '7 مساءً'),
        (20, '8 مساءً'), (21, '9 مساءً'), (22, '10 مساءً'), (23, '11 مساءً')
    ], coerce=int, validators=[DataRequired()])

    minute = SelectField('الدقيقة', choices=[
        (0, '00'), (5, '05'), (10, '10'), (15, '15'), (20, '20'),
        (25, '25'), (30, '30'), (35, '35'), (40, '40'), (45, '45'),
        (50, '50'), (55, '55')
    ], coerce=int, validators=[DataRequired()])

    backup_type = SelectField('نوع النسخة الاحتياطية', choices=[
        ('full', 'نسخة كاملة'),
        ('warehouse', 'مستودع محدد')
    ], validators=[DataRequired()])

    warehouse_id = SelectField('المستودع', coerce=int, validators=[Optional()])

    is_active = BooleanField('تفعيل الجدولة', default=True)

    submit = SubmitField('حفظ الجدولة')

    def __init__(self, *args, **kwargs):
        super(BackupScheduleForm, self).__init__(*args, **kwargs)
        # Only show warehouses that the current user has access to
        if current_user.is_authenticated:
            if current_user.is_admin_role:
                # إذا كان المستخدم إداري، أظهر جميع المستودعات
                self.warehouse_id.choices = [(0, 'جميع المستودعات')] + [(w.id, w.name) for w in Warehouse.query.all()]
            else:
                # إذا لم يكن المستخدم إداري، أظهر فقط المستودعات التي لديه صلاحية الوصول إليها
                self.warehouse_id.choices = [(0, 'جميع المستودعات')] + [(w.id, w.name) for w in current_user.warehouses]

@reports_bp.route('/backup', methods=['GET', 'POST'])
@login_required
def backup():
    """Handle database backup and restore functionality."""
    # Initialize the backup schedule form
    schedule_form = BackupScheduleForm()

    # Add upload form
    class UploadBackupForm(FlaskForm):
        backup_file = FileField('ملف النسخة الاحتياطية', validators=[DataRequired()])
        submit = SubmitField('رفع النسخة الاحتياطية')

    upload_form = UploadBackupForm()

    if request.method == 'POST':
        action = request.form.get('action')

        # Debug: Print form data
        logger.info(f"POST request received with action: {action}")
        logger.info(f"Form data: {dict(request.form)}")

        if action == 'backup':
            try:
                # Get warehouse ID from form
                warehouse_id = request.form.get('warehouse_id')
                if warehouse_id == '0':
                    warehouse_id = None
                else:
                    warehouse_id = int(warehouse_id)
                    # Check if user has access to this warehouse
                    if warehouse_id and not current_user.is_admin_role:
                        warehouse = Warehouse.query.get(warehouse_id)
                        if warehouse not in current_user.warehouses:
                            flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
                            return redirect(url_for('reports.backup'))

                # Create backup
                filename, file_path = backup_database(warehouse_id, current_user.id)
                if filename and file_path:
                    # Log the backup creation
                    warehouse_name = "جميع المستودعات" if warehouse_id is None else Warehouse.query.get(warehouse_id).name
                    log = ActivityLog(
                        action="إنشاء نسخة احتياطية",
                        description=f"تم إنشاء نسخة احتياطية لـ {warehouse_name}",
                        ip_address=request.remote_addr,
                        user_id=current_user.id,
                        warehouse_id=warehouse_id if warehouse_id else Warehouse.query.first().id
                    )
                    db.session.add(log)
                    db.session.commit()
                    pass  # تم إنشاء النسخة بنجاح
                else:
                    flash('فشل إنشاء النسخة الاحتياطية', 'danger')
            except Exception as e:
                flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'danger')
                logger.error(f'Backup creation error: {str(e)}')


                # Update backup records after successful backup
                update_backup_records()
            except Exception as e:
                flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'danger')
                logger.error(f'Backup creation error: {str(e)}')

        elif action == 'restore':
            backup_id = request.form.get('backup_id')
            if backup_id:
                backup_record = BackupRecord.query.get(backup_id)

                if backup_record:
                    file_path = os.path.join(current_app.config['BACKUP_DIR'], backup_record.filename)
                    success, message = restore_database(file_path, current_user.id)

                    if success:
                        flash(message, 'success')
                    else:
                        flash(message, 'danger')
                else:
                    flash('النسخة الاحتياطية غير موجودة', 'danger')

        elif action == 'download':
            backup_id = request.form.get('backup_id')
            if backup_id:
                backup_record = BackupRecord.query.get(backup_id)

                if backup_record:
                    file_path = os.path.join(current_app.config['BACKUP_DIR'], backup_record.filename)
                    if os.path.exists(file_path):
                        return send_file(file_path, as_attachment=True)
                    else:
                        flash('ملف النسخة الاحتياطية غير موجود على الخادم', 'danger')
                else:
                    flash('النسخة الاحتياطية غير موجودة', 'danger')
            else:
                flash('النسخة الاحتياطية غير موجودة', 'danger')

        elif action == 'delete_backup':
            backup_id = request.form.get('backup_id')
            if backup_id:
                backup_record = BackupRecord.query.get(backup_id)

                if backup_record:
                    # حذف الملف الفعلي
                    file_path = os.path.join(current_app.config['BACKUP_DIR'], backup_record.filename)
                    if os.path.exists(file_path):
                        try:
                            os.remove(file_path)
                        except Exception as e:
                            flash(f'حدث خطأ أثناء حذف ملف النسخة الاحتياطية: {str(e)}', 'danger')
                            return redirect(url_for('reports.backup'))

                    # حذف السجل من قاعدة البيانات
                    db.session.delete(backup_record)
                    db.session.commit()

                    # تسجيل العملية في سجل النشاط
                    log = ActivityLog(
                        action="حذف نسخة احتياطية",
                        description=f"تم حذف النسخة الاحتياطية {backup_record.filename}",
                        ip_address=request.remote_addr,
                        user_id=current_user.id,
                        warehouse_id=backup_record.warehouse_id if backup_record.warehouse_id else Warehouse.query.first().id
                    )
                    db.session.add(log)
                    db.session.commit()

                    flash('تم حذف النسخة الاحتياطية بنجاح', 'success')
                else:
                    flash('النسخة الاحتياطية غير موجودة', 'danger')
            else:
                flash('النسخة الاحتياطية غير موجودة', 'danger')

        elif action == 'create_schedule':
            try:
                # Get data from form directly (for simple form)
                schedule_type = request.form.get('schedule_type')
                backup_type = request.form.get('backup_type')
                hour = int(request.form.get('hour', 2))
                minute = int(request.form.get('minute', 0))
                warehouse_id = request.form.get('warehouse_id')

                # Convert warehouse_id
                if warehouse_id == '0' or not warehouse_id:
                    warehouse_id = None
                else:
                    warehouse_id = int(warehouse_id)

                # Validate required fields
                if not schedule_type or not backup_type:
                    flash('نوع الجدولة ونوع النسخة مطلوبان', 'danger')
                else:
                    # Get day_of_week or day_of_month based on schedule_type
                    day_of_week = None
                    day_of_month = None

                    if schedule_type == 'weekly':
                        day_of_week = int(request.form.get('day_of_week', 0))
                    elif schedule_type == 'monthly':
                        day_of_month = int(request.form.get('day_of_month', 1))

                    # Create the schedule
                    schedule = create_backup_schedule(
                        schedule_type=schedule_type,
                        backup_type=backup_type,
                        user_id=current_user.id,
                        warehouse_id=warehouse_id,
                        day_of_week=day_of_week,
                        day_of_month=day_of_month,
                        hour=hour,
                        minute=minute
                    )

                    if schedule:
                        pass  # تم إنشاء الجدولة بنجاح
                    else:
                        flash('فشل إنشاء جدولة النسخ الاحتياطي', 'danger')
            except Exception as e:
                flash(f'خطأ في إنشاء الجدولة: {str(e)}', 'danger')
                logger.error(f'Schedule creation error: {str(e)}')

        elif action == 'download':
            try:
                backup_id = int(request.form.get('backup_id'))
                backup_record = BackupRecord.query.get_or_404(backup_id)

                # إرسال الملف للتنزيل
                return send_file(
                    backup_record.file_path,
                    as_attachment=True,
                    download_name=backup_record.filename,
                    mimetype='application/json'
                )
            except Exception as e:
                flash(f'خطأ في تنزيل النسخة الاحتياطية: {str(e)}', 'danger')
                logger.error(f'Download error: {str(e)}')

        elif action == 'restore':
            try:
                backup_id = int(request.form.get('backup_id'))
                backup_record = BackupRecord.query.get_or_404(backup_id)
                
                backup_file_path = os.path.join(current_app.config['BACKUP_DIR'], backup_record.filename)
                if not os.path.exists(backup_file_path):
                    flash('ملف النسخة الاحتياطية غير موجود', 'danger')
                    return redirect(url_for('reports.backup'))

                # استعادة النسخة الاحتياطية باستخدام المستخدم الحالي
                success, message = restore_database(backup_file_path, current_user.id)

                if success:
                    # تسجيل العملية
                    log = ActivityLog(
                        action="استعادة نسخة احتياطية",
                        description=f"تم استعادة النسخة الاحتياطية: {backup_record.filename}",
                        ip_address=request.remote_addr,
                        user_id=current_user.id,
                        warehouse_id=Warehouse.query.first().id
                    )
                    db.session.add(log)
                    db.session.commit()

                    flash('تم استعادة النسخة الاحتياطية بنجاح', 'success')
                else:
                    flash(f'فشل في استعادة النسخة الاحتياطية: {message}', 'danger')

            except Exception as e:
                flash(f'خطأ في استعادة النسخة الاحتياطية: {str(e)}', 'danger')
                logger.error(f'Restore error: {str(e)}')

        elif action == 'delete_backup':
            try:
                backup_id = int(request.form.get('backup_id'))
                backup_record = BackupRecord.query.get_or_404(backup_id)

                # حذف الملف من القرص
                if os.path.exists(backup_record.file_path):
                    os.remove(backup_record.file_path)

                # حذف السجل من قاعدة البيانات
                db.session.delete(backup_record)
                db.session.commit()

                # تسجيل العملية
                log = ActivityLog(
                    action="حذف نسخة احتياطية",
                    description=f"تم حذف النسخة الاحتياطية: {backup_record.filename}",
                    ip_address=request.remote_addr,
                    user_id=current_user.id,
                    warehouse_id=Warehouse.query.first().id
                )
                db.session.add(log)
                db.session.commit()

                flash('تم حذف النسخة الاحتياطية بنجاح', 'success')

            except Exception as e:
                flash(f'خطأ في حذف النسخة الاحتياطية: {str(e)}', 'danger')
                logger.error(f'Delete backup error: {str(e)}')

        elif action == 'update_schedule':
            schedule_id = request.form.get('schedule_id')
            is_active = 'is_active' in request.form

            if schedule_id:
                schedule = update_backup_schedule(
                    schedule_id=int(schedule_id),
                    is_active=is_active
                )

                if schedule:
                    flash('تم تحديث جدولة النسخ الاحتياطي بنجاح', 'success')
                else:
                    flash('فشل تحديث جدولة النسخ الاحتياطي', 'danger')

        elif action == 'toggle_schedule':
            try:
                schedule_id = int(request.form.get('schedule_id'))
                schedule = BackupSchedule.query.get_or_404(schedule_id)

                # تبديل حالة التفعيل
                schedule.is_active = not schedule.is_active

                # إعادة حساب التشغيل التالي إذا تم التفعيل
                if schedule.is_active:
                    schedule.next_run = calculate_next_run(
                        schedule.schedule_type,
                        schedule.hour,
                        schedule.minute,
                        schedule.day_of_week,
                        schedule.day_of_month
                    )
                else:
                    schedule.next_run = None

                db.session.commit()

                # تسجيل العملية
                action_text = "تفعيل" if schedule.is_active else "إيقاف"
                log = ActivityLog(
                    action=f"{action_text} جدولة النسخ الاحتياطي",
                    description=f"تم {action_text} جدولة النسخ الاحتياطي رقم {schedule_id}",
                    ip_address=request.remote_addr,
                    user_id=current_user.id,
                    warehouse_id=Warehouse.query.first().id
                )
                db.session.add(log)
                db.session.commit()

                pass  # تم تعديل الجدولة بنجاح

            except Exception as e:
                flash(f'خطأ في تعديل حالة الجدولة: {str(e)}', 'danger')
                logger.error(f'Toggle schedule error: {str(e)}')

        elif action == 'delete_schedule':
            try:
                schedule_id = int(request.form.get('schedule_id'))
                schedule = BackupSchedule.query.get_or_404(schedule_id)

                # حذف الجدولة
                db.session.delete(schedule)
                db.session.commit()

                # تسجيل العملية
                log = ActivityLog(
                    action="حذف جدولة النسخ الاحتياطي",
                    description=f"تم حذف جدولة النسخ الاحتياطي رقم {schedule_id}",
                    ip_address=request.remote_addr,
                    user_id=current_user.id,
                    warehouse_id=Warehouse.query.first().id
                )
                db.session.add(log)
                db.session.commit()

                pass  # تم حذف الجدولة بنجاح

            except Exception as e:
                flash(f'خطأ في حذف الجدولة: {str(e)}', 'danger')
                logger.error(f'Delete schedule error: {str(e)}')

        elif action == 'upload_backup':
            if 'backup_file' in request.files:
                backup_file = request.files['backup_file']
                if backup_file.filename != '':
                    # التحقق من امتداد الملف
                    if not backup_file.filename.endswith('.json'):
                        flash('يجب أن يكون ملف النسخة الاحتياطية بصيغة JSON', 'danger')
                    else:
                        # إنشاء اسم ملف آمن
                        filename = secure_filename(backup_file.filename)

                        # التأكد من وجود مجلد النسخ الاحتياطية
                        backup_dir = current_app.config['BACKUP_DIR']
                        if not os.path.exists(backup_dir):
                            os.makedirs(backup_dir)

                        # حفظ الملف
                        file_path = os.path.join(backup_dir, filename)
                        backup_file.save(file_path)

                        # إنشاء سجل للنسخة الاحتياطية في قاعدة البيانات
                        file_size = os.path.getsize(file_path)

                        # تحديد نوع النسخة الاحتياطية من اسم الملف
                        if 'full' in filename:
                            backup_type = 'full'
                            warehouse_id = None
                        elif 'warehouse' in filename:
                            # استخراج معرف المستودع من اسم الملف إذا كان موجودًا
                            try:
                                import re
                                match = re.search(r'warehouse_(\d+)', filename)
                                if match:
                                    warehouse_id = int(match.group(1))
                                    backup_type = f'warehouse_{warehouse_id}'
                                else:
                                    warehouse_id = None
                                    backup_type = 'full'
                            except:
                                warehouse_id = None
                                backup_type = 'full'
                        else:
                            warehouse_id = None
                            backup_type = 'full'

                        backup_record = BackupRecord(
                            filename=filename,
                            backup_type=backup_type,
                            file_size=file_size,
                            user_id=current_user.id,
                            warehouse_id=warehouse_id
                        )
                        db.session.add(backup_record)
                        db.session.commit()

                        flash(f'تم رفع ملف النسخة الاحتياطية بنجاح: {filename}', 'success')
                else:
                    flash('لم يتم اختيار ملف', 'danger')
            else:
                flash('لم يتم تقديم ملف', 'danger')

    # تحديث قائمة النسخ الاحتياطية من المجلد
    print("\n\n[DEBUG] Calling update_backup_records()")
    update_backup_records()
    print("[DEBUG] update_backup_records() completed")

    # Get all backup records
    backup_records = BackupRecord.query.order_by(BackupRecord.timestamp.desc()).all()

    # Get all backup schedules
    backup_schedules = BackupSchedule.query.order_by(BackupSchedule.created_at.desc()).all()

    # Get warehouses
    if current_user.is_admin_role:
        warehouses = Warehouse.query.all()
    else:
        warehouses = current_user.warehouses

    return render_template('backup.html',
                          backup_records=backup_records,
                          backup_schedules=backup_schedules,
                          warehouses=warehouses,
                          schedule_form=schedule_form,
                          upload_form=upload_form,
                          format_datetime_12h=format_datetime_12h,
                          format_time_12h=format_time_12h,
                          backups=backup_records,
                          title='النسخ الاحتياطي واستعادة البيانات')


def update_backup_records():
    """تحديث قائمة النسخ الاحتياطية من المجلد"""
    backup_dir = current_app.config['BACKUP_DIR']

    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)

    # الحصول على قائمة ملفات النسخ الاحتياطية في المجلد
    try:
        backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.json') and f.startswith('backup_')]
    except Exception as e:
        backup_files = []

    # الحصول على قائمة النسخ الاحتياطية المسجلة في قاعدة البيانات
    try:
        db_backup_records = {record.filename: record for record in BackupRecord.query.all()}
    except Exception as e:
        db_backup_records = {}

    # إضافة الملفات الجديدة إلى قاعدة البيانات
    for filename in backup_files:
        if filename not in db_backup_records:
            # تحديد نوع النسخة الاحتياطية من اسم الملف
            if 'full' in filename:
                backup_type = 'full'
                warehouse_id = None
            elif 'warehouse' in filename:
                # استخراج معرف المستودع من اسم الملف إذا كان موجودًا
                try:
                    import re
                    match = re.search(r'warehouse_(\d+)', filename)
                    if match:
                        warehouse_id = int(match.group(1))
                        backup_type = f'warehouse_{warehouse_id}'
                    else:
                        warehouse_id = None
                        backup_type = 'full'
                except:
                    warehouse_id = None
                    backup_type = 'full'
            else:
                warehouse_id = None
                backup_type = 'full'

            # الحصول على حجم الملف
            file_path = os.path.join(backup_dir, filename)
            file_size = os.path.getsize(file_path)

            # التحقق من وجود مستخدم في قاعدة البيانات
            user = User.query.first()
            if user:
                # إنشاء سجل جديد
                backup_record = BackupRecord(
                    filename=filename,
                    backup_type=backup_type,
                    file_size=file_size,
                    user_id=user.id,
                    warehouse_id=warehouse_id
                )
                db.session.add(backup_record)

    # حذف السجلات التي لم يعد لها ملفات
    for filename, record in db_backup_records.items():
        if filename not in backup_files:
            db.session.delete(record)

    db.session.commit()


@reports_bp.route('/api/current-date-info')
@login_required
def current_date_info():
    """API endpoint للحصول على معلومات التاريخ الهجري والميلادي الحالي"""
    try:
        # الحصول على معلومات التاريخ الحالي
        current_date_info = get_hijri_date()
        week_info = get_week_range()

        # تنسيق فترة الأسبوع
        week_range = f"من {week_info['week_start_hijri']['hijri_formatted']} إلى {week_info['week_end_hijri']['hijri_formatted']}"

        return jsonify({
            'hijri_formatted': current_date_info['hijri_formatted'],
            'gregorian_formatted': current_date_info['gregorian_formatted'],
            'day_name': current_date_info['day_name'],
            'time_24h': current_date_info['time_24h'],
            'time_12h': current_date_info['time_12h'],
            'week_range': week_range,
            'success': True
        })

    except Exception as e:
        print(f"Error in current_date_info: {e}")
        return jsonify({
            'error': str(e),
            'success': False
        }), 500


@reports_bp.route('/api/hijri-date')
def get_current_hijri_date():
    """API endpoint للحصول على التاريخ الهجري الحالي بدقة (لا يتطلب تسجيل دخول)"""
    try:
        from hijri_converter import Gregorian
        from datetime_utils import get_saudi_now

        # الحصول على التاريخ والوقت الحالي بتوقيت السعودية
        saudi_now = get_saudi_now()

        # تحويل إلى التاريخ الهجري باستخدام تقويم أم القرى
        hijri_date = Gregorian(
            saudi_now.year,
            saudi_now.month,
            saudi_now.day
        ).to_hijri()

        # أسماء الأشهر الهجرية
        hijri_months = [
            'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
            'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
        ]

        # أسماء الأيام بالعربية
        arabic_days = [
            'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
        ]

        # الحصول على اسم اليوم
        day_name = arabic_days[saudi_now.weekday()]

        return jsonify({
            'hijri_day': hijri_date.day,
            'hijri_month': hijri_date.month,
            'hijri_month_name': hijri_months[hijri_date.month - 1],
            'hijri_year': hijri_date.year,
            'hijri_formatted': f"{hijri_date.day} {hijri_months[hijri_date.month - 1]} {hijri_date.year}هـ",
            'gregorian_formatted': saudi_now.strftime('%Y-%m-%d'),
            'day_name': day_name,
            'time_24h': saudi_now.strftime('%H:%M'),
            'time_12h': saudi_now.strftime('%I:%M %p'),
            'timestamp': saudi_now.isoformat(),
            'success': True
        })

    except Exception as e:
        print(f"Error in get_current_hijri_date: {e}")
        # في حالة الخطأ، استخدم النظام القديم كبديل
        try:
            current_date_info = get_hijri_date()
            return jsonify({
                'hijri_formatted': current_date_info['hijri_formatted'],
                'gregorian_formatted': current_date_info['gregorian_formatted'],
                'day_name': current_date_info['day_name'],
                'time_24h': current_date_info['time_24h'],
                'time_12h': current_date_info['time_12h'],
                'success': True,
                'fallback': True
            })
        except Exception as e2:
            return jsonify({
                'error': str(e2),
                'success': False
            }), 500
    except Exception as e:
        return jsonify({
            'error': str(e),
            'success': False
        }), 500

@reports_bp.route('/run-scheduled-backups', methods=['POST', 'GET'])
@login_required
def run_scheduled_backups_endpoint():
    """تشغيل النسخ الاحتياطية المجدولة يدوياً أو عبر cron"""

    # التحقق من صلاحيات المستخدم
    if not current_user.is_admin_role:
        return jsonify({
            'success': False,
            'error': 'ليس لديك صلاحية لتشغيل النسخ المجدولة'
        }), 403

    try:
        # فحص النسخ المستحقة
        due_schedules = get_due_backup_schedules()

        if not due_schedules:
            return jsonify({
                'success': True,
                'message': 'لا توجد نسخ احتياطية مستحقة للتشغيل',
                'schedules_processed': 0,
                'results': []
            })

        # تشغيل النسخ المستحقة
        results = run_scheduled_backups()

        # إحصائيات النتائج
        successful_backups = [r for r in results if r['success']]
        failed_backups = [r for r in results if not r['success']]

        # تسجيل العملية
        log = ActivityLog(
            action="تشغيل النسخ المجدولة",
            description=f"تم تشغيل {len(successful_backups)}/{len(results)} نسخة احتياطية مجدولة بنجاح",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=Warehouse.query.first().id
        )
        db.session.add(log)
        db.session.commit()

        # إرجاع النتائج
        response_data = {
            'success': True,
            'message': f'تم تشغيل {len(successful_backups)}/{len(results)} نسخة احتياطية بنجاح',
            'schedules_processed': len(results),
            'successful_backups': len(successful_backups),
            'failed_backups': len(failed_backups),
            'results': results
        }

        if request.method == 'GET':
            # إذا كان الطلب GET، أرجع صفحة HTML
            return redirect(url_for('reports.backup'))
        else:
            # إذا كان الطلب POST، أرجع JSON
            return jsonify(response_data)

    except Exception as e:
        error_msg = f"خطأ في تشغيل النسخ المجدولة: {str(e)}"
        logger.error(error_msg)

        if request.method == 'GET':
            return redirect(url_for('reports.backup'))
        else:
            return jsonify({
                'success': False,
                'error': error_msg
            }), 500

@reports_bp.route('/scheduler-status')
@login_required
def scheduler_status():
    """الحصول على حالة النسخ المجدولة"""

    if not current_user.is_admin_role:
        return jsonify({
            'success': False,
            'error': 'ليس لديك صلاحية لعرض حالة الجدولة'
        }), 403

    try:
        # الحصول على جميع الجدولات
        all_schedules = BackupSchedule.query.all()
        active_schedules = BackupSchedule.query.filter_by(is_active=True).all()

        # الحصول على النسخ المستحقة
        due_schedules = get_due_backup_schedules()

        # إحصائيات الجدولة
        schedule_stats = {
            'total_schedules': len(all_schedules),
            'active_schedules': len(active_schedules),
            'due_schedules': len(due_schedules),
            'inactive_schedules': len(all_schedules) - len(active_schedules)
        }

        # تفاصيل الجدولات النشطة
        active_schedule_details = []
        for schedule in active_schedules:
            active_schedule_details.append({
                'id': schedule.id,
                'schedule_type': schedule.schedule_type,
                'backup_type': schedule.backup_type,
                'next_run': schedule.next_run.isoformat() if schedule.next_run else None,
                'last_run': schedule.last_run.isoformat() if schedule.last_run else None,
                'warehouse_name': schedule.warehouse.name if schedule.warehouse else 'جميع المستودعات'
            })

        return jsonify({
            'success': True,
            'stats': schedule_stats,
            'active_schedules': active_schedule_details,
            'due_schedules_count': len(due_schedules)
        })

    except Exception as e:
        error_msg = f"خطأ في الحصول على حالة الجدولة: {str(e)}"
        logger.error(error_msg)
        return jsonify({
            'success': False,
            'error': error_msg
        }), 500