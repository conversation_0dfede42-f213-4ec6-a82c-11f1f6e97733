{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            نتائج البحث ({{ personnel_list|length }})
        </h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('personnel.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى القائمة
        </a>
    </div>
</div>

<div class="card mb-4" style="position: relative;z-index: 1;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div class="me-0">
            <h5 class="mb-0"><i class="fas fa-search"></i> نتائج البحث عن "{{ query }}"</h5>
        </div>
        <div>
            <form action="{{ url_for('personnel.search') }}" method="GET" class="d-flex" id="searchForm">
                <input type="text" name="q" class="form-control ms-2" placeholder="بحث..."
                    value="{{ query }}" id="searchInput" autocomplete="off">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="card-body p-0">
        {% if personnel_list|length == 0 %}
        <div class="alert alert-info m-3">
            لا توجد نتائج مطابقة لـ "{{ query }}". حاول استخدام كلمات بحث مختلفة.
        </div>
        {% else %}
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>#</th>
                    <th>الرقم العسكري</th>
                    <th>الاسم</th>
                    <th>الرتبة</th>
                    <th>الحالة</th>
                    <th>المستودع</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for personnel in personnel_list %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ personnel.personnel_id }}</td>
                    <td>{{ personnel.name }}</td>
                    <td>{{ personnel.rank }}</td>
                    <td>
                        {% if personnel.status == 'نشط' %}
                        <span class="badge badge-success">{{ personnel.status }}</span>
                        {% elif personnel.status == 'إجازة' %}
                        <span class="badge badge-warning">{{ personnel.status }}</span>
                        {% elif personnel.status == 'مهمة' %}
                        <span class="badge badge-mission">{{ personnel.status }}</span>
                        {% elif personnel.status == 'دورة' %}
                        <span class="badge badge-danger">{{ personnel.status }}</span>
                        {% elif personnel.status == 'مستلم' %}
                        <span class="badge badge-primary">{{ personnel.status }}</span>
                        {% else %}
                        <span class="badge badge-secondary">{{ personnel.status }}</span>
                        {% endif %}
                    </td>
                    <td>{{ personnel.warehouse.name }}</td>
                    <td style="max-width: 110px;">
                        <div class="btn-box">
                            <a href="{{ url_for('personnel.details', personnel_id=personnel.id) }}"
                                class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ url_for('personnel.edit', personnel_id=personnel.id) }}"
                                class="btn btn-sm btn-outline-info">
                                <i class="fas fa-edit"></i>
                            </a>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='نشط') }}">
                                        <i class="fas fa-check-circle text-success mr-2"></i> تعيين كنشط
                                    </a>
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='إجازة') }}">
                                        <i class="fas fa-calendar text-warning mr-2"></i> تعيين في إجازة
                                    </a>
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='مهمة') }}">
                                        <i class="fas fa-tasks text-warning mr-2" style="color: #fd7e14 !important;"></i> تعيين في مهمة
                                    </a>
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='دورة') }}">
                                        <i class="fas fa-graduation-cap text-danger mr-2"></i> تعيين في دورة
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='مستلم') }}">
                                        <i class="fas fa-user-clock text-primary mr-2"></i> تعيين كمستلم
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <form method="POST"
                                        action="{{ url_for('personnel.delete_personnel', personnel_id=personnel.id) }}">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-trash text-danger mr-2"></i> حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const searchForm = document.getElementById('searchForm');

        // تعيين التركيز على حقل البحث عند تحميل الصفحة ووضع المؤشر في نهاية النص
        searchInput.focus();
        // وضع المؤشر في نهاية النص
        const inputValue = searchInput.value;
        searchInput.value = '';
        searchInput.value = inputValue;

        // متغير لتخزين الوقت المستغرق بين الإدخالات
        let typingTimer;
        // الفترة الزمنية بالمللي ثانية قبل إرسال النموذج (300 مللي ثانية = 0.3 ثانية)
        const doneTypingInterval = 300;

        // عند الكتابة في حقل البحث
        searchInput.addEventListener('input', function() {
            // إعادة ضبط المؤقت في كل مرة يتم فيها الكتابة
            clearTimeout(typingTimer);

            // إذا كان الحقل غير فارغ، ابدأ المؤقت
            if (searchInput.value) {
                typingTimer = setTimeout(submitForm, doneTypingInterval);
            }
        });

        // وظيفة إرسال النموذج
        function submitForm() {
            searchForm.submit();
        }

        // عند مسح الحقل بالكامل، قم بإرسال النموذج أيضًا للعودة إلى القائمة الكاملة
        searchInput.addEventListener('keyup', function(e) {
            if (searchInput.value === '' && e.key === 'Backspace') {
                submitForm();
            }
        });

        document.querySelectorAll('.dropdown-toggle').forEach((dropdown) => {
            dropdown.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation(); // Stop Bootstrap's listener if still active
                const isOpen = dropdown.classList.contains("show");
                // Close all other dropdowns
                document.querySelectorAll(".dropdown-toggle").forEach((el) => {
                    el.classList.remove("show");
                    el.setAttribute("aria-expanded", false);
                });
                document.querySelectorAll(".dropdown-menu").forEach((el) => {
                    el.classList.remove("show");
                });

                if (!isOpen) {
                    dropdown.classList.add("show");
                } else {
                    dropdown.classList.remove("show");
                }
                dropdown.setAttribute("aria-expanded", dropdown.classList.contains("show"));

                document.addEventListener("click", function closeDropDown(event) {
                    // إغلاق القوائم المنسدلة عند النقر في أي مكان آخر

                    if (!dropdown.contains(event.target) || dropdown !== event.target) {
                        dropdown.classList.remove("show");
                        dropdown.setAttribute("aria-expanded", false);
                        document.removeEventListener("click", closeDropDown);
                    }
                });
            });
        });
    });
</script>
{% endblock %}
