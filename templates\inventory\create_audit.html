{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>إنشاء جرد جديد</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('inventory.audits') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة الجرد
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-clipboard-check"></i> بيانات الجرد</h5>
    </div>
    <div class="card-body">
        {% if form.warehouse_id.choices|length == 0 %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> لا توجد مستودعات متاحة لإنشاء جرد جديد. يرجى التواصل مع مدير النظام.
        </div>
        <a href="{{ url_for('inventory.audits') }}" class="btn btn-outline-secondary">العودة إلى قائمة الجرد</a>
        {% else %}
        <form method="POST" action="{{ url_for('inventory.create_audit') }}">
            {{ form.hidden_tag() }}
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        {{ form.warehouse_id.label }}
                        {{ form.warehouse_id(class="form-control") }}
                        {% if form.warehouse_id.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.warehouse_id.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">اختر المستودع الذي سيتم جرده</small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        {{ form.description.label }}
                        {{ form.description(class="form-control", rows=3) }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.description.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        {{ form.notes.label }}
                        {{ form.notes(class="form-control", rows=3) }}
                        {% if form.notes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.notes.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> ملاحظة: بعد إنشاء الجرد، سيتم توجيهك إلى صفحة تفاصيل الجرد حيث يمكنك
                جرد الأسلحة.
            </div>
            <div class="form-group mt-4">
                {{ form.submit(class="btn btn-primary") }}
                <a href="{{ url_for('inventory.audits') }}" class="btn btn-outline-secondary mr-2">إلغاء</a>
            </div>
        </form>
        {% endif %}
    </div>
</div>
{% endblock %}