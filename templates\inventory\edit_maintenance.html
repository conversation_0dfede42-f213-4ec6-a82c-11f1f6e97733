{% extends 'base.html' %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card bg-dark text-white">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4>تعديل سجل صيانة - {{ device.name }}</h4>
                    <a href="{{ url_for('inventory.device_details', device_id=device.id) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i> العودة للتفاصيل
                    </a>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('inventory.edit_device_maintenance', device_id=device.id, maintenance_id=maintenance.id) }}">
                        {{ form.hidden_tag() }}
                        <div class="row">
                            <div class="col-md-6 form-group">
                                {{ form.maintenance_type.label }}
                                {{ form.maintenance_type(class="form-control") }}
                            </div>
                            <div class="col-md-6 form-group">
                                {{ form.status.label }}
                                {{ form.status(class="form-control") }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 form-group">
                                {{ form.start_date.label }}
                                {{ form.start_date(class="form-control datepicker", placeholder="YYYY-MM-DD") }}
                            </div>
                            <div class="col-md-6 form-group">
                                {{ form.end_date.label }}
                                {{ form.end_date(class="form-control datepicker", placeholder="YYYY-MM-DD") }}
                            </div>
                        </div>
                        <div class="form-group">
                            {{ form.description.label }}
                            {{ form.description(class="form-control", rows=3) }}
                        </div>
                        <div class="row">
                            <div class="col-md-6 form-group">
                                {{ form.cost.label }}
                                {{ form.cost(class="form-control", placeholder="أدخل التكلفة (اختياري)") }}
                            </div>
                        </div>
                        <div class="form-group">
                            {{ form.notes.label }}
                            {{ form.notes(class="form-control", rows=2) }}
                        </div>
                        <div class="form-group text-center mt-4">
                            <a href="{{ url_for('inventory.device_details', device_id=device.id) }}" class="btn btn-secondary">إلغاء</a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize date pickers
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            rtl: true,
            language: 'ar'
        });
    });
</script>
{% endblock %}
