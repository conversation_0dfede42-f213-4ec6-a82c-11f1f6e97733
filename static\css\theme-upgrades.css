/* تعديلات على كل الألوان الرئيسية */
:root {
  /* Dark Theme Colors (Default) */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2d2d2d;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --accent-color: #0d6efd;
  --accent-hover: #0b5ed7;
  --success-color: #198754;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #3ba955;
  --border-color: #404040;
  --shooting-color: #C8BBBE;

  /* Light Theme Colors */
  --light-bg-primary: #ffffff;
  --light-bg-secondary: #f0f2f5;
  --light-bg-tertiary: #e4e6e9;
  --light-text-primary: #1a1a1a;
  --light-text-secondary: #4a4a4a;
  --light-border-color: #cfd4da;
  --light-accent-color: #0d6efd;
  --light-accent-hover: #0b5ed7;
  --light-success-color: #198754;
  --light-danger-color: #dc3545;
  --light-warning-color: #ffc107;
  --light-info-color: #3ba955;
  --light-shooting-color: #C8BBBE;
}
.bg-themed {
  background-color: #2c2c2c;
}
.light-theme .bg-themed {
  background-color: #e7e7e7;
}

/* تحسينات المظهر في الوضع الليلي والنهاري */
/* تنسيقات إضافية لضمان التباين المناسب في الوضع الليلي */
body:not(.light-theme) .btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

body:not(.light-theme) .list-group-item {
  background-color: #242424;
  color: var(--text-primary);
  border-bottom: var(--bs-list-group-border-width) solid
    rgba(255, 255, 255, 0.125);
}

/* تنسيقات خاصة بشارات الحالة في لوحة المراقبة */
body:not(.light-theme) .badge-dark {
  background-color: #343a40;
  color: white;
}

body:not(.light-theme) .bg-recipient {
  background-color: var(--accent-color);
  color: white;
}

body:not(.light-theme) .bg-shooting {
  background-color: var(--shooting-color);
  color: #333;
}

/* تنسيقات إضافية لضمان التباين المناسب في الوضع النهاري */
body.light-theme .btn-primary {
  background-color: var(--light-accent-color);
  border-color: var(--light-accent-color);
  color: white;
}

body.light-theme .btn-secondary {
  background-color: #e0e0e0;
  border-color: #d0d0d0;
  color: var(--light-text-primary);
}

/* تنسيقات خاصة بشارات الحالة في لوحة المراقبة في الوضع النهاري */
body.light-theme .badge-dark {
  background-color: #6c757d;
  color: white;
}

body.light-theme .bg-recipient {
  background-color: var(--light-accent-color);
  color: white;
}

body.light-theme .bg-shooting {
  background-color: var(--light-shooting-color);
  color: #333;
}

body.light-theme .table-hover tbody tr:hover {
  background-color: transparent;
}

body.light-theme .table-hover tbody tr:hover > * {
  background-color: rgba(255, 255, 255, 1);
}

/* تنسيقات الأيقونات في الوضع النهاري */
body.light-theme .fas,
body.light-theme .far,
body.light-theme .fab {
  color: inherit;
}

/* تحسينات واجهة المستخدم  */
body .dropdown-menu:not(.datepicker) {
  left: 0 !important;
}

/* تحسينات واجهة المستخدم للوضع النهاري */
body.light-theme .dropdown-menu {
  background-color: white;
  border: 1px solid var(--light-border-color);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
body .dropdown-toggle.show + .dropdown-menu {
  display: block;
}
body .dropdown-toggle + .dropdown-menu.fix-left {
  left: 0 !important;
  right: auto !important;
}
body .dropdown-toggle + .dropdown-menu.fix-right {
  left: auto !important;
  right: 0 !important;
}

body.light-theme .dropdown-item {
  color: var(--light-text-primary);
}

body.light-theme .dropdown-item:hover,
body.light-theme .dropdown-item:focus {
  background-color: var(--light-bg-secondary);
  color: var(--light-text-primary);
}

/* تنسيقات الوضع الليلي للعناصر المنسدلة */
body:not(.light-theme) .dropdown-menu {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: white;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5);
}

body:not(.light-theme) .dropdown-item {
  color: var(--text-primary);
}

body:not(.light-theme) .dropdown-item:hover,
body:not(.light-theme) .dropdown-item:focus {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* تنسيقات خاصة بصفحة تسجيل الدخول */
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease;
}

.login-card {
  width: 100%;
  max-width: 450px;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 30px;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

body.light-theme .login-container {
  background-color: var(--light-bg-primary);
}

body.light-theme .login-card {
  background-color: white;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* تنسيقات إضافية لزر تبديل المظهر */
#theme-switcher {
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

#theme-switcher:hover {
  transform: rotate(30deg);
  color: var(--text-primary);
}

body.light-theme #theme-switcher {
  color: var(--light-text-secondary);
}

body.light-theme #theme-switcher:hover {
  color: var(--light-text-primary);
}

/* تنسيقات لضمان وضوح العناصر في الوضع النهاري */
body.light-theme .alert-success {
  color: #0f5132;
  background-color: #d1e7dd;
  border-color: #badbcc;
}

body.light-theme .alert-danger {
  color: #842029;
  background-color: #f8d7da;
  border-color: #f5c2c7;
}

body.light-theme .alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
}

body.light-theme .alert-info {
  color: #055160;
  background-color: #cff4fc;
  border-color: #b6effb;
}

/* تنسيق الجداول في الوضع الليلي*/
body:not(.light-theme) .table-hover tbody tr * {
  color: var(--text-primary);
}
body:not(.light-theme) .table-light {
  --bs-table-color: #fff;
}

/* تنسيق الجداول في كلا الوضعين */
body.light-theme .table {
  color: var(--light-text-primary);
}

body.light-theme .table-striped > tbody > tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

body:not(.light-theme) .table-striped > tbody > tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

/* تنسيقات البطاقات الإضافية */
body.light-theme .card {
  background-color: white;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

body.light-theme .card-header {
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

body:not(.light-theme) .card-header {
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

/* تحسينات شريط التنقل */
.navbar {
  padding: 12px 15px;
}

.navbar-user {
  display: flex;
  align-items: center;
}

.navbar-user .navbar-text {
  display: flex;
  align-items: center;
  margin: 0;
}

.navbar-user .fa-user-circle {
  font-size: 1.2rem;
  margin-left: 8px;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.navbar .form-inline {
  margin-right: 0;
}

/* تحسين حجم وشكل أيقونات الشريط العلوي */
.navbar-nav .nav-link,
.btn-link.nav-link,
#theme-switcher,
#settingsButton {
  font-size: 1.1rem;
  padding: 0.5rem 0.75rem;
  margin: 0 0.25rem;
  color: var(--text-secondary);
  transition: color 0.2s ease, transform 0.2s ease;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-nav .nav-link:hover,
.btn-link.nav-link:hover,
#theme-switcher:hover,
#settingsButton:hover {
  color: var(--text-primary);
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* تنسيقات الوضع النهاري للشريط العلوي */
body.light-theme .navbar {
  background-color: var(--light-bg-secondary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

body.light-theme .navbar-nav .nav-link,
body.light-theme .btn-link.nav-link,
body.light-theme #theme-switcher,
body.light-theme #settingsButton {
  color: var(--light-text-secondary);
}

body.light-theme .navbar-nav .nav-link:hover,
body.light-theme .btn-link.nav-link:hover,
body.light-theme #theme-switcher:hover,
body.light-theme #settingsButton:hover {
  color: var(--light-text-primary);
  background-color: rgba(0, 0, 0, 0.05);
}

body.light-theme .navbar-user .navbar-text {
  color: var(--light-text-primary);
}

/* تنسيقات الوضع الليلي للشريط العلوي */
body:not(.light-theme) .navbar-user .navbar-text {
  color: var(--text-primary);
}

/* تحسين حقل البحث */
#search-form .input-group {
  width: 250px;
}

#search-input {
  border-radius: 20px 0 0 20px;
}

#search-form .btn {
  border-radius: 0 20px 20px 0;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-header {
  padding: 0.5rem 1rem;
  font-weight: 600;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
}

.dropdown-item i {
  margin-left: 0.5rem;
  width: 1rem;
  text-align: center;
}

/* تحسين الأزرار في الوضع الليلي */
body:not(.light-theme) .btn.btn-outline-dark {
  --bs-btn-color: rgb(33, 37, 41);
  --bs-btn-hover-color: rgb(18, 20, 22);
  --bs-btn-bg: rgb(240, 240, 240);
  --bs-btn-hover-bg: rgb(205, 205, 205);
  --bs-btn-border-color: rgb(206, 206, 206);
  --bs-btn-hover-border-color: rgb(180, 180, 180);
}

body:not(.light-theme) .btn.btn-outline-dark:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}

body .btn.btn-info,
body:not(.light-theme) .btn.btn-info,
body:not(.light-theme) .btn.btn-outline-info {
  --bs-btn-color: #fff;
  --bs-btn-bg: #3ba955;
  --bs-btn-border-color: #3ba955;

  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #2e8a45;
  --bs-btn-hover-border-color: #389d50;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #2e8a45;
  --bs-btn-active-border-color: #389d50;
}

body .btn.btn-outline-info {
  --bs-btn-color: #3ba955;

  --bs-btn-border-color: #3ba955;

  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #2e8a4542;
  --bs-btn-hover-border-color: #389d50;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #2e8a45;
  --bs-btn-active-border-color: #389d50;
}

body .btn.btn-info:active,
body .btn.btn-outline-info:active,
body:not(.light-theme) .btn.btn-info:active,
body:not(.light-theme) .btn.btn-outline-info:active,
body .btn.btn-info:focus,
body .btn.btn-outline-info:focus,
body:not(.light-theme) .btn.btn-info:focus,
body:not(.light-theme) .btn.btn-outline-info:focus {
  outline: none;
}

body:not(.light-theme) .btn-outline-secondary:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}

body:not(.light-theme) .btn-outline-secondary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #4b4b4b;
  --bs-btn-border-color: #4b4b4b;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #444444;
  --bs-btn-hover-border-color: #444647;
  --bs-btn-focus-shadow-rgb: 130, 138, 145;
  color: var(--bs-btn-color);
  background-color: var(--bs-btn-bg);
  border-color: var(--bs-btn-border-color);
}

body:not(.light-theme) .btn-outline-primary {
  --bs-btn-color: #fff;
  --bs-btn-hover-color: #fff;
  --bs-btn-bg: #0d6efd;
  --bs-btn-hover-bg: #0b5ed7;
  --bs-btn-border-color: #0d6efd;
  --bs-btn-hover-border-color: #0a58ca;
  color: var(--bs-btn-color);
  background-color: var(--bs-btn-bg);
  border-color: var(--bs-btn-border-color);
}

body:not(.light-theme) .btn-outline-primary:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}
body:not(.light-theme) .btn-outline-danger {
  --bs-btn-color: #fff;
  --bs-btn-bg: #dc3545;
  --bs-btn-border-color: #dc3545;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #b32836;
  --bs-btn-hover-border-color: #b02a37;
  color: var(--bs-btn-color);
  background-color: var(--bs-btn-bg);
  border-color: var(--bs-btn-border-color);
}
body:not(.light-theme) .btn-outline-danger:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}
body:not(.light-theme) .btn-outline-success {
  --bs-btn-color: #fff;
  --bs-btn-bg: #198754;
  --bs-btn-border-color: #198754;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #0e663d;
  --bs-btn-hover-border-color: #095833;
  color: var(--bs-btn-color);
  background-color: var(--bs-btn-bg);
  border-color: var(--bs-btn-border-color);
}
body:not(.light-theme) .btn-outline-success:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}

body:not(.light-theme) .tab-content {
  background-color: var(--bg-secondary) !important;
}
body:not(.light-theme) .nav-tabs,
body:not(.light-theme) .nav-link {
  --bs-nav-tabs-link-hover-border-color: var(--bs-gray) var(--bs-gray) #dee2e6;
  border-color: var(--bs-gray) !important;
  color: var(--text-primary);
}
body.light-theme .card.bg-dark {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;
}
body.light-theme .card.bg-dark > * {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;
}
body.light-theme .table.table-dark > :not(caption) > * > * {
  background-color: var(--bs-table-color);
}
body.light-theme .table tr td,
body.light-theme .table tr th {
  border-top-color: var(--light-border-color);
}
body.light-theme .table.table-dark {
  --bs-table-border-color: #d5d7d8;
  border-color: var(--bs-table-border-color);
}
body.light-theme .bg-secondary {
  --bs-bg-opacity: 0.2;
}
.input-group-append .input-group-text {
  height: 100%;
}
.dropdown-toggle-custom i {
  user-select: none;
  pointer-events: none;
}
.dropdown-toggle-custom::after {
  margin-left: 0;
  margin-right: 0.255em;
}
.dropdown-toggle-custom::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
