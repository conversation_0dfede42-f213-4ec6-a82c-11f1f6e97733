{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            جرد السلاح: {{ weapon.name }}
        </h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('inventory.audit_details', audit_id=audit.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل الجرد
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clipboard-check"></i> تسجيل حالة الجرد</h5>
            </div>
            <div class="card-body">
                <!-- معلومات السلاح -->
                <div class="card mb-4 border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات السلاح</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الرقم التسلسلي:</label>
                                <div>{{ weapon.serial_number }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">رقم الحفظ:</label>
                                <div>{{ weapon.weapon_number or 'غير محدد' }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الاسم:</label>
                                <div>{{ weapon.name }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">النوع:</label>
                                <div>
                                    {% if weapon.type == 'pistol' %}
                                    مسدس
                                    {% elif weapon.type == 'rifle' %}
                                    بندقية
                                    {% elif weapon.type == 'sniper' %}
                                    قناص
                                    {% elif weapon.type == 'machine_gun' %}
                                    رشاش
                                    {% else %}
                                    {{ weapon.type }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الحالة الحالية:</label>
                                <div>
                                    <span class="badge badge-{{ weapon.status|status_color }}">{{ weapon.status }}</span>
                                </div>
                            </div>
                            {% if weapon.condition %}
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">حالة السلاح:</label>
                                <div>{{ weapon.condition }}</div>
                            </div>
                            {% endif %}

                            {% if weapon.maintenance_records.count() > 0 %}
                            <div class="col-md-12 mb-3">
                                <label class="font-weight-bold">سجلات الصيانة السابقة:</label>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>النوع</th>
                                                <th>الحالة</th>
                                                <th>الوصف</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for record in weapon.maintenance_records.order_by(MaintenanceRecord.start_date.desc()).limit(3).all() %}
                                            <tr>
                                                <td>{{ record.start_date.strftime('%Y-%m-%d') }}</td>
                                                <td>{{ translate_maintenance_type(record.maintenance_type) }}</td>
                                                <td>{{ translate_maintenance_status(record.status) }}</td>
                                                <td>{{ record.description|truncate(50) }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                {% if weapon.personnel and weapon.personnel.count() > 0 %}
                <!-- معلومات الأفراد المرتبطين بالسلاح -->
                <div class="card mb-4 border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-users"></i> معلومات الأفراد المرتبطين بالسلاح</h5>
                    </div>
                    <div class="card-body">
                        {% for person in weapon.personnel %}
                        <div class="row {% if not loop.last %}mb-4 border-bottom pb-3{% endif %}">
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الاسم:</label>
                                <div>{{ person.name }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الرقم العسكري:</label>
                                <div>{{ person.personnel_id }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الرتبة:</label>
                                <div>{{ person.rank }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الحالة:</label>
                                <div>
                                    <span class="badge badge-{{ person.status|status_color }}">{{ person.status }}</span>
                                </div>
                            </div>
                            {% if person.phone %}
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">رقم الهوية الوطنية:</label>
                                <div>{{ person.phone }}</div>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% elif associated_personnel %}
                <!-- معلومات الفرد المرتبط بالسلاح (للتوافق مع الكود القديم) -->
                <div class="card mb-4 border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-user"></i> معلومات الفرد المرتبط بالسلاح</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الاسم:</label>
                                <div>{{ associated_personnel.name }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الرقم العسكري:</label>
                                <div>{{ associated_personnel.personnel_id }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الرتبة:</label>
                                <div>{{ associated_personnel.rank }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">الحالة:</label>
                                <div>
                                    <span class="badge badge-{{ associated_personnel.status|status_color }}">{{ associated_personnel.status }}</span>
                                </div>
                            </div>
                            {% if associated_personnel.phone %}
                            <div class="col-md-6 mb-3">
                                <label class="font-weight-bold">رقم الهوية الوطنية:</label>
                                <div>{{ associated_personnel.phone }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- نموذج تسجيل الجرد -->
                <form method="POST">
                    {{ form.csrf_token }}
                    {{ form.audit_id }}
                    {{ form.weapon_id }}

                    <div class="form-group">
                        <label>{{ form.status.label }}</label>
                        <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                            <label
                                class="btn btn-outline-success{% if form.status.data == 'found' %} active{% endif %}">
                                <input type="radio" name="{{ form.status.name }}" id="status_found" value="found" {% if
                                    form.status.data=='found' %}checked{% endif %}> سليم
                            </label>
                            <label
                                class="btn btn-outline-danger{% if form.status.data == 'missing' %} active{% endif %}">
                                <input type="radio" name="{{ form.status.name }}" id="status_missing" value="missing" {%
                                    if form.status.data=='missing' %}checked{% endif %}> مفقود
                            </label>
                            <label
                                class="btn btn-outline-primary{% if form.status.data == 'damaged' %} active{% endif %}">
                                <input type="radio" name="{{ form.status.name }}" id="status_damaged" value="damaged" {%
                                    if form.status.data=='damaged' %}checked{% endif %}> صيانة
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>{{ form.condition.label }}</label>
                        {{ form.condition(class="form-control", placeholder="اختياري - وصف حالة السلاح") }}
                    </div>

                    <div class="form-group">
                        <label>{{ form.notes.label }}</label>
                        {{ form.notes(class="form-control", rows=3, placeholder="اختياري - أي ملاحظات إضافية") }}
                    </div>

                    <!-- حقول إضافية للصيانة تظهر فقط عند اختيار حالة "صيانة" -->
                    <div id="maintenance-fields" style="display: none;">
                        <div class="card mb-4 border-info">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-tools"></i> معلومات الصيانة</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>{{ form.maintenance_reason.label }}</label>
                                    {{ form.maintenance_reason(class="form-control", rows=2, placeholder="سبب إرسال السلاح للصيانة") }}
                                </div>

                                <div class="form-group">
                                    <label>{{ form.technical_notes.label }}</label>
                                    {{ form.technical_notes(class="form-control", rows=2, placeholder="ملاحظات فنية حول الصيانة المطلوبة") }}
                                </div>

                                <div class="form-group">
                                    <label>{{ form.create_maintenance_record.label }}</label>
                                    {{ form.create_maintenance_record(class="form-control") }}
                                    <small class="form-text text-muted">اختر "نعم" لإنشاء سجل صيانة تلقائيًا وتحديث حالة السلاح</small>
                                </div>

                                <div id="maintenance-type-field" style="display: none;">
                                    <div class="form-group">
                                        <label>{{ form.maintenance_type.label }}</label>
                                        {{ form.maintenance_type(class="form-control") }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('inventory.audit_details', audit_id=audit.id) }}"
                            class="btn btn-secondary">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // الحصول على عناصر النموذج
        const statusRadios = document.querySelectorAll('input[name="{{ form.status.name }}"]');
        const maintenanceFields = document.getElementById('maintenance-fields');
        const createMaintenanceSelect = document.getElementById('{{ form.create_maintenance_record.id }}');
        const maintenanceTypeField = document.getElementById('maintenance-type-field');

        // دالة لإظهار/إخفاء حقول الصيانة بناءً على الحالة المختارة
        function toggleMaintenanceFields() {
            const damagedRadio = document.getElementById('status_damaged');
            if (damagedRadio.checked) {
                maintenanceFields.style.display = 'block';
            } else {
                maintenanceFields.style.display = 'none';
            }
        }

        // دالة لإظهار/إخفاء حقل نوع الصيانة بناءً على اختيار إنشاء سجل صيانة
        function toggleMaintenanceTypeField() {
            if (createMaintenanceSelect.value === 'yes') {
                maintenanceTypeField.style.display = 'block';
            } else {
                maintenanceTypeField.style.display = 'none';
            }
        }

        // إضافة مستمعي الأحداث
        statusRadios.forEach(function(radio) {
            radio.addEventListener('change', toggleMaintenanceFields);
        });

        createMaintenanceSelect.addEventListener('change', toggleMaintenanceTypeField);

        // تنفيذ الدوال عند تحميل الصفحة
        toggleMaintenanceFields();
        toggleMaintenanceTypeField();

        // إذا كانت الحالة المحددة مسبقًا هي "صيانة"، قم بإظهار حقول الصيانة
        {% if form.status.data == 'damaged' %}
        maintenanceFields.style.display = 'block';
        {% endif %}

        // إذا كان إنشاء سجل الصيانة محدد مسبقًا، قم بإظهار حقل نوع الصيانة
        {% if form.create_maintenance_record.data == 'yes' %}
        maintenanceTypeField.style.display = 'block';
        {% endif %}
    });
</script>
{% endblock %}