/* ===== Modern Display Screens Stylesheet ===== */
/* Author: Military Warehouse Management System
   Description: Modern CSS for warehouse display screens with card-based design
*/

/* ===== Variables ===== */
:root {
  /* Display Screen Colors - Dark Mode (Default) */
  --display-bg: #1d1d1d;
  --display-card-bg: #242424;
  --display-card-header: #2d2d2d;
  --display-accent: #3b82f6;
  --display-success: #28a745; /* لون أخضر لحالة نشط */
  --display-warning: #ffc107; /* لون أصفر لحالة إجازة */
  --display-danger: #dc3545; /* لون أحمر لحالة دورة */
  --display-info: #06b6d4;
  --display-mission: #fd7e14; /* لون برتقالي لحالة مهمة */
  --display-recipient: #0d6efd; /* لون أزرق لحالة مستلم */
  --display-maintenance: #FAAFBE; /* لون وردي لحالة صيانة */
  --display-vacant: #6c757d; /* لون رمادي لحالة شاغر */
  --display-cycle: #dc3545; /* لون أحمر لحالة دورة */
  --display-shooting: #C8BBBE; /* لون Lavender Blush3 لحالة رماية */
  --display-text: #f8fafc;
  --display-text-secondary: #94a3b8;
  --display-border: #475569;
  --display-shadow: rgba(0, 0, 0, 0.25);

  /* Light Mode Colors */
  --display-light-bg: #eef2f7;
  --display-light-card-bg: #e9edf3;
  --display-light-card-header: #dde3ec;
  --display-light-text: #0f172a;
  --display-light-text-secondary: #64748b;
  --display-light-border: #cbd5e1;
  --display-light-shadow: rgba(0, 0, 0, 0.1);
}

/* ===== Main Display Container ===== */
.display-screen {
  background-color: var(--display-bg);
  min-height: 100vh;
  padding: 0;
  color: var(--display-text);
  font-family: 'Cairo', sans-serif;
  overflow-x: hidden;
}

body.light-theme .display-screen {
  background-color: var(--display-light-bg);
  color: var(--display-light-text);
}

/* ===== Header Section ===== */
.display-header {
  background-color: var(--display-card-header);
  padding: 1rem;
  border-bottom: 1px solid var(--display-border);
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px var(--display-shadow);
}

body.light-theme .display-header {
  background-color: var(--display-light-card-header);
  border-bottom: 1px solid var(--display-light-border);
  box-shadow: 0 4px 6px var(--display-light-shadow);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
}

.logo-section img {
  height: 60px;
  margin-left: 1rem;
}

.logo-section h1 {
  font-size: 1.8rem;
  margin: 0;
  font-weight: 700;
  color: var(--display-text);
}

body.light-theme .logo-section h1 {
  color: var(--display-light-text);
}

.time-section {
  text-align: left;
}

.current-time {
  font-size: 2rem;
  font-weight: 700;
}

.current-date {
  font-size: 1.2rem;
  color: var(--display-text-secondary);
}

body.light-theme .current-date {
  color: var(--display-light-text-secondary);
}

/* ===== Status Banner ===== */
.status-banner {
  display: flex;
  justify-content: space-around;
  padding: 0.75rem 0;
  background-color: var(--display-card-bg);
  border-radius: 0.5rem;
  margin-top: 1rem;
  box-shadow: 0 2px 4px var(--display-shadow);
}

body.light-theme .status-banner {
  background-color: var(--display-light-card-bg);
  box-shadow: 0 2px 4px var(--display-light-shadow);
}

.status-item {
  display: flex;
  align-items: center;
}

.status-item i {
  margin-left: 0.5rem;
  color: var(--display-accent);
}

body.light-theme .status-item i {
  color: var(--display-accent);
}

/* ===== Card Layout ===== */
.display-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 0 1.5rem 1.5rem;
}

.display-card {
  background-color: var(--display-card-bg);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 6px var(--display-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

body.light-theme .display-card {
  background-color: var(--display-light-card-bg);
  box-shadow: 0 4px 6px var(--display-light-shadow);
}

.display-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 12px var(--display-shadow);
}

body.light-theme .display-card:hover {
  box-shadow: 0 8px 12px var(--display-light-shadow);
}

.card-header {
  background-color: var(--display-card-header);
  padding: 1rem;
  border-bottom: 1px solid var(--display-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

body.light-theme .card-header {
  background-color: var(--display-light-card-header);
  border-bottom: 1px solid var(--display-light-border);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
}

.card-title i {
  margin-left: 0.75rem;
}

.card-body {
  padding: 1.5rem;
}

/* ===== Statistics ===== */
.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-align: center;
}

.stat-label {
  font-size: 1rem;
  color: var(--display-text-secondary);
  text-align: center;
}

body.light-theme .stat-label {
  color: var(--display-light-text-secondary);
}

/* ===== Charts ===== */
.chart-container {
  position: relative;
  height: 200px;
  width: 100%;
}

/* ===== Progress Bars ===== */
.progress-container {
  margin-bottom: 1rem;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.progress-bar-container {
  height: 10px;
  background-color: var(--display-border);
  border-radius: 5px;
  overflow: hidden;
}

body.light-theme .progress-bar-container {
  background-color: var(--display-light-border);
}

.progress-bar {
  height: 100%;
  border-radius: 5px;
}

.progress-bar.active {
  background-color: var(--display-success);
}

.progress-bar.leave {
  background-color: var(--display-warning);
}

.progress-bar.mission {
  background-color: var(--display-mission);
}

.progress-bar.maintenance {
  background-color: var(--display-maintenance);
}

.progress-bar.cycle {
  background-color: #dc3545;
}

.progress-bar.vacant {
  background-color: #6c757d;
}

.progress-bar.recipient {
  background-color: var(--display-recipient);
}

.progress-bar.shooting {
  background-color: var(--display-shooting);
}

/* ===== Transactions List ===== */
.transactions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.transaction-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--display-border);
  display: flex;
  align-items: center;
  background-color: transparent;
}

body.light-theme .transaction-item {
  border-bottom: 1px solid var(--display-light-border);
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--display-card-header);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 1rem;
}

body.light-theme .transaction-icon {
  background-color: var(--display-light-card-header);
}

.transaction-icon.checkout {
  color: var(--display-success);
}

.transaction-icon.return {
  color: var(--display-warning);
}

.transaction-icon.transfer {
  color: var(--display-info);
}

.transaction-content {
  flex: 1;
}

.transaction-title.text-center {
  text-align: center;
  width: 100%;
  color: var(--display-text);
}

body.light-theme .transaction-title.text-center {
  color: var(--display-light-text);
}

.transaction-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.transaction-details {
  font-size: 0.875rem;
  color: var(--display-text-secondary);
}

body.light-theme .transaction-details {
  color: var(--display-light-text-secondary);
}

.transaction-time {
  font-size: 0.875rem;
  color: var(--display-text-secondary);
  text-align: left;
}

body.light-theme .transaction-time {
  color: var(--display-light-text-secondary);
}

/* ===== Donut Chart ===== */
.donut-chart {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
}

.donut-chart-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.donut-chart-number {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
}

.donut-chart-label {
  font-size: 1rem;
  color: var(--display-text-secondary);
}

body.light-theme .donut-chart-label {
  color: var(--display-light-text-secondary);
}

/* ===== Legend ===== */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0.25rem 0.5rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-left: 0.5rem;
}

.legend-color.active {
  background-color: var(--display-success);
}

.legend-color.leave {
  background-color: var(--display-warning);
}

.legend-color.mission {
  background-color: var(--display-mission);
}

.legend-color.maintenance {
  background-color: var(--display-maintenance);
}

.legend-color.cycle {
  background-color: #dc3545;
}

.legend-color.vacant {
  background-color: #6c757d;
}

.legend-color.retired {
  background-color: #3b82f6;
}

.legend-color.recipient {
  background-color: var(--display-recipient);
}

.legend-color.shooting {
  background-color: var(--display-shooting);
}

.legend-label {
  font-size: 0.875rem;
}

/* ===== Last Update ===== */
.last-update {
  text-align: center;
  padding: 1rem;
  color: var(--display-text-secondary);
  font-size: 0.875rem;
}

body.light-theme .last-update {
  color: var(--display-light-text-secondary);
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 768px) {
  .display-cards {
    grid-template-columns: 1fr;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .logo-section {
    margin-bottom: 1rem;
  }

  .time-section {
    text-align: center;
  }

  .status-banner {
    flex-direction: column;
    align-items: center;
  }

  .status-item {
    margin-bottom: 0.5rem;
  }
}

/* ===== Animation ===== */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}
