{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>إضافة فرد جديد</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('personnel.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة الأفراد
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-user-plus"></i> بيانات الفرد</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('personnel.create') }}">
            {{ form.hidden_tag() }}
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.personnel_id.label }}
                        {{ form.personnel_id(class="form-control") }}
                        {% if form.personnel_id.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.personnel_id.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.name.label }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.rank.label }}
                        {{ form.rank(class="form-control") }}
                        {% if form.rank.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.rank.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.status.label }}
                        {{ form.status(class="form-control") }}
                        {% if form.status.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.status.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.phone.label }}
                        {{ form.phone(class="form-control") }}
                        {% if form.phone.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.phone.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.warehouse_id.label }}
                        {{ form.warehouse_id(class="form-control") }}
                        {% if form.warehouse_id.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.warehouse_id.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="form-group">
                {{ form.primary_weapons.label }}
                {{ form.primary_weapons(class="form-control", size=6) }}
                {% if form.primary_weapons.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.primary_weapons.errors %}
                    <span>{{ error }}</span>
                    {% endfor %}
                </div>
                {% endif %}
                <small class="form-text text-muted">اختر الأسلحة الأساسية للفرد بالضغط مع CTRL للاختيار المتعدد</small>
            </div>
            <div class="form-group">
                {{ form.notes.label }}
                {{ form.notes(class="form-control", rows=4) }}
                {% if form.notes.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.notes.errors %}
                    <span>{{ error }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="form-group mt-4">
                {{ form.submit(class="btn btn-primary") }}
                <a href="{{ url_for('personnel.index') }}" class="btn btn-outline-secondary ml-2">إلغاء</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}