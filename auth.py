import os
from datetime import datetime, timezone
from flask import Blueprint, render_template, redirect, url_for, flash, request, session, make_response
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SelectField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Length, EqualTo

from db import db
from models import User, Warehouse, ActivityLog, UserRoleEnum, BackupRecord, Audit, WeaponTransaction, MaintenanceRecord

# Create the blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

# Forms
class LoginForm(FlaskForm):
    # استخدام أسماء حقول غير قياسية لمنع المتصفح من التعرف عليها
    username = StringField('اسم المستخدم', validators=[DataRequired()], render_kw={"name": "usr_" + str(hash("username"))[:8]})
    password = PasswordField('كلمة المرور', validators=[DataRequired()], render_kw={"name": "pwd_" + str(hash("password"))[:8]})
    remember_me = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class RegisterForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=64)])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Length(min=5, max=120)])
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=8)])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[DataRequired(), EqualTo('password')])
    role = SelectField('الدور', choices=[
        ('admin', 'مدير نظام'),
        ('warehouse_manager', 'مدير المستودعات'),
        ('inventory_manager', 'مسؤول مخزون'),
        ('monitor', 'مراقب'),
        ('company_duty', 'مناوب السرية')
    ])
    warehouses = SelectField('المستودع', choices=[], coerce=int)
    submit = SubmitField('تسجيل')

    def __init__(self, *args, **kwargs):
        super(RegisterForm, self).__init__(*args, **kwargs)
        self.warehouses.choices = [(w.id, w.name) for w in Warehouse.query.all()]

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=8)])
    confirm_password = PasswordField('تأكيد كلمة المرور الجديدة', validators=[DataRequired(), EqualTo('new_password')])
    submit = SubmitField('تغيير كلمة المرور')

class EditUserForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=64)])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Length(min=5, max=120)])
    role = SelectField('الدور', choices=[
        ('admin', 'مدير نظام'),
        ('warehouse_manager', 'مدير المستودعات'),
        ('inventory_manager', 'مسؤول مخزون'),
        ('monitor', 'مراقب'),
        ('company_duty', 'مناوب السرية')
    ])
    warehouses = SelectField('المستودع', choices=[], coerce=int)
    submit = SubmitField('حفظ التعديلات')

    def __init__(self, *args, **kwargs):
        super(EditUserForm, self).__init__(*args, **kwargs)
        self.warehouses.choices = [(w.id, w.name) for w in Warehouse.query.all()]

class ResetPasswordForm(FlaskForm):
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=8)])
    confirm_password = PasswordField('تأكيد كلمة المرور الجديدة', validators=[DataRequired(), EqualTo('new_password')])
    submit = SubmitField('تغيير كلمة المرور')

# Routes
@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        # إعادة توجيه مناوب السرية إلى كشف الاستلامات
        if current_user.is_company_duty:
            return redirect(url_for('receipts.index'))
        else:
            return redirect(url_for('warehouse.dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()

        if user and user.check_password(form.password.data) and user.is_active:
            # استخدام خيار "تذكرني" الذي اختاره المستخدم
            login_user(user, remember=form.remember_me.data)
            user.last_login = datetime.now(timezone.utc)

            # استخدم خاصية role التي تم تعديلها للتعامل مع حالة عدم وجود عمود user_role
            # القيمة الافتراضية ستكون 'admin' إذا كان المستخدم مديراً، وإلا 'monitor'
            user_role = user.role

            # حفظ الدور في جلسة المستخدم للتوافق مع الكود القديم
            user.save_role_to_session(session)

            db.session.commit()

            # Log the login activity
            log = ActivityLog(
                action="تسجيل دخول",
                description=f"تم تسجيل الدخول للمستخدم {user.username}",
                ip_address=request.remote_addr,
                user_id=user.id,
                warehouse_id=user.warehouses[0].id if user.warehouses else 1
            )
            db.session.add(log)
            db.session.commit()

            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                # إعادة توجيه مناوب السرية إلى كشف الاستلامات
                if user.is_company_duty:
                    next_page = url_for('receipts.index')
                else:
                    next_page = url_for('warehouse.dashboard')
            return redirect(next_page)
        else:
            flash('فشل تسجيل الدخول. يرجى التحقق من اسم المستخدم وكلمة المرور.', 'danger')

    return render_template('login.html', form=form, title='تسجيل الدخول')

@auth_bp.route('/logout')
@login_required
def logout():
    # Log the logout activity
    log = ActivityLog(
        action="تسجيل خروج",
        description=f"تم تسجيل الخروج للمستخدم {current_user.username}",
        ip_address=request.remote_addr,
        user_id=current_user.id,
        warehouse_id=current_user.warehouses[0].id if current_user.warehouses else 1
    )
    db.session.add(log)
    db.session.commit()

    logout_user()
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
@login_required
def register():
    # Only admins and warehouse managers can register new users
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية لإضافة مستخدمين جدد', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    form = RegisterForm()
    if form.validate_on_submit():
        existing_user = User.query.filter_by(username=form.username.data).first()
        existing_email = User.query.filter_by(email=form.email.data).first()

        if existing_user:
            flash('اسم المستخدم موجود بالفعل. الرجاء اختيار اسم آخر.', 'danger')
        elif existing_email:
            flash('البريد الإلكتروني موجود بالفعل. الرجاء استخدام بريد إلكتروني آخر.', 'danger')
        # مدير المستودعات لا يمكنه إضافة مستخدمين بصلاحية مدير نظام
        elif current_user.is_warehouse_manager and form.role.data == 'admin':
            flash('ليس لديك صلاحية لإضافة مستخدمين بصلاحية مدير نظام', 'danger')
        # إزالة القيد على عدد المستخدمين بصلاحية مدير المستودعات
        # elif form.role.data == 'warehouse_manager' and User.query.filter_by(user_role='warehouse_manager').count() > 0:
        #     flash('لا يمكن إضافة أكثر من مستخدم واحد بصلاحية مدير المستودعات', 'danger')
        else:
            # Create new user
            new_user = User(
                username=form.username.data,
                full_name=form.full_name.data,
                email=form.email.data,
                is_admin=(form.role.data == 'admin')  # للتوافق مع الكود القديم
            )
            # تعيين الدور الجديد في قاعدة البيانات
            new_user.role = form.role.data  # هذا سيقوم بتحديث user_role و is_admin معاً
            new_user.set_password(form.password.data)

            # Add warehouse association
            if form.role.data == 'admin' or form.role.data == 'warehouse_manager':
                # إذا كان المستخدم مدير نظام أو مدير مستودعات، منحه صلاحية على جميع المستودعات
                warehouses = Warehouse.query.all()
                new_user.warehouses.extend(warehouses)
                warehouse = warehouses[0] if warehouses else None
            elif form.role.data == 'company_duty':
                # مناوب السرية لا يحتاج إلى مستودعات (يعمل على كشف الاستلامات وإدارة المواقع فقط)
                warehouse = None
            else:
                # للأدوار الأخرى، منح صلاحية على المستودع المحدد فقط
                if form.warehouses.data:
                    warehouse = Warehouse.query.get(form.warehouses.data)
                    if warehouse:
                        new_user.warehouses.append(warehouse)
                else:
                    # في حالة عدم تحديد مستودع، استخدم المستودع الأول
                    warehouses = Warehouse.query.all()
                    if warehouses:
                        warehouse = warehouses[0]
                        new_user.warehouses.append(warehouse)
                    else:
                        warehouse = None

            try:
                db.session.add(new_user)

                # حفظ الدور في جلسة المستخدم للتوافق مع الكود القديم
                db.session.flush()  # للحصول على معرف المستخدم
                new_user.save_role_to_session(session)

                # Log the user creation
                # للمستخدمين بدون مستودعات (مناوب السرية)، استخدم المستودع الأول كمرجع للسجل
                log_warehouse_id = warehouse.id if warehouse else (current_user.warehouses[0].id if current_user.warehouses else 1)
                log = ActivityLog(
                    action="إنشاء مستخدم",
                    description=f"تم إنشاء المستخدم {new_user.username} بواسطة {current_user.username}",
                    ip_address=request.remote_addr,
                    user_id=current_user.id,
                    warehouse_id=log_warehouse_id
                )
                db.session.add(log)

                db.session.commit()
                flash('تم إنشاء المستخدم بنجاح!', 'success')
                return redirect(url_for('auth.users'))
            except Exception as e:
                db.session.rollback()
                # تحقق من نوع الخطأ وعرض رسالة مناسبة
                if 'users_email_key' in str(e):
                    flash('البريد الإلكتروني موجود بالفعل. الرجاء استخدام بريد إلكتروني آخر.', 'danger')
                elif 'users_username_key' in str(e):
                    flash('اسم المستخدم موجود بالفعل. الرجاء اختيار اسم آخر.', 'danger')
                else:
                    flash(f'حدث خطأ أثناء إنشاء المستخدم: {str(e)}', 'danger')
    return render_template('register.html', form=form, title='إضافة مستخدم جديد')

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            current_user.set_password(form.new_password.data)

            # Log the password change
            log = ActivityLog(
                action="تغيير كلمة المرور",
                description=f"تم تغيير كلمة المرور للمستخدم {current_user.username}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=current_user.warehouses[0].id if current_user.warehouses else 1
            )
            db.session.add(log)

            db.session.commit()
            flash('تم تغيير كلمة المرور بنجاح!', 'success')
            return redirect(url_for('warehouse.dashboard'))
        else:
            flash('كلمة المرور الحالية غير صحيحة', 'danger')

    return render_template('change_password.html', form=form, title='تغيير كلمة المرور')

@auth_bp.route('/users')
@login_required
def users():
    # Only admins and warehouse managers can view all users
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية لعرض المستخدمين', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    users = User.query.all()

    # التحقق من الدور لكل مستخدم
    for user in users:
        # استخدم خاصية role التي تم تعديلها للتعامل مع حالة عدم وجود عمود user_role
        # القيمة الافتراضية ستكون 'admin' إذا كان المستخدم مديراً، وإلا 'monitor'
        user_role = user.role

        # حفظ الدور في جلسة المستخدم للتوافق مع الكود القديم
        user.save_role_to_session(session)

    return render_template('users.html', users=users, title='إدارة المستخدمين')

@auth_bp.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
def delete_user(user_id):
    """
    حذف مستخدم مع التعامل مع جميع العلاقات المرتبطة به.
    بما أن بعض الجداول تحتوي على قيود NOT NULL على حقل user_id، فإننا نقوم بتعيين السجلات المرتبطة بالمستخدم المحذوف إلى المستخدم الحالي (مدير النظام) بدلاً من تعيينها إلى NULL.
    """
    # Only admins and warehouse managers can delete user
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية لحذف المستخدمين', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    user = User.query.get_or_404(user_id)
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الشخصي', 'danger')
    # مدير المستودعات لا يمكنه حذف مستخدم بصلاحية مدير نظام
    elif current_user.is_warehouse_manager and user.role == 'admin':
        flash('ليس لديك صلاحية لحذف مستخدمين بصلاحية مدير نظام', 'danger')
    else:
        try:
            username = user.username
            warehouse_id = user.warehouses[0].id if user.warehouses else 1

            # 1. تعامل مع سجلات النشاط (ActivityLog)
            # بما أن حقل user_id في قاعدة البيانات معرف كـ NOT NULL، سنقوم بتعيينه إلى المستخدم الحالي بدلاً من NULL
            ActivityLog.query.filter_by(user_id=user.id).update({"user_id": current_user.id})

            # 2. تعامل مع سجلات النسخ الاحتياطي (BackupRecord)
            # نقوم بتعيين سجلات النسخ الاحتياطي للمستخدم الحالي
            BackupRecord.query.filter_by(user_id=user.id).update({"user_id": current_user.id})

            # 3. تعامل مع سجلات التدقيق (Audit)
            # نفس المشكلة قد تحدث مع سجلات التدقيق، لذلك نقوم بتعيينها للمستخدم الحالي
            Audit.query.filter_by(user_id=user.id).update({"user_id": current_user.id})

            # 4. تعامل مع سجلات معاملات الأسلحة (WeaponTransaction)
            WeaponTransaction.query.filter_by(user_id=user.id).update({"user_id": current_user.id})

            # 5. تعامل مع سجلات صيانة الأسلحة (MaintenanceRecord)
            # إذا كان هناك علاقة مع المستخدم
            if hasattr(MaintenanceRecord, 'user_id'):
                MaintenanceRecord.query.filter_by(user_id=user.id).update({"user_id": current_user.id})

            # 6. مسح علاقة المستودعات
            user.warehouses.clear()

            # 7. حذف المستخدم
            db.session.delete(user)

            # 8. تسجيل عملية الحذف
            log = ActivityLog(
                action="حذف مستخدم",
                description=f"تم حذف المستخدم {username} بواسطة {current_user.username}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=warehouse_id
            )
            db.session.add(log)

            # 9. حفظ التغييرات
            db.session.commit()

            flash(f'تم حذف المستخدم {username} بنجاح', 'success')

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء حذف المستخدم: {str(e)}', 'danger')

    return redirect(url_for('auth.users'))


@auth_bp.route('/users/<int:user_id>/toggle-status')
@login_required
def toggle_user_status(user_id):
    # Only admins and warehouse managers can toggle user status
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية لتعديل حالة المستخدمين', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    user = User.query.get_or_404(user_id)
    if user.id == current_user.id:
        flash('لا يمكنك تعديل حالة حسابك الشخصي', 'danger')
    # مدير المستودعات لا يمكنه تغيير حالة مستخدم بصلاحية مدير نظام
    elif current_user.is_warehouse_manager and user.role == 'admin':
        flash('ليس لديك صلاحية لتغيير حالة مستخدمين بصلاحية مدير نظام', 'danger')
    else:
        user.is_active = not user.is_active
        status = 'تفعيل' if user.is_active else 'تعطيل'

        # Log the status change
        log = ActivityLog(
            action=f"{status} مستخدم",
            description=f"تم {status} المستخدم {user.username} بواسطة {current_user.username}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=user.warehouses[0].id if user.warehouses else 1
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم {status} المستخدم بنجاح', 'success')

    return redirect(url_for('auth.users'))

@auth_bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    # Only admins and warehouse managers can edit users
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية لتعديل المستخدمين', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    user = User.query.get_or_404(user_id)
    form = EditUserForm()

    if request.method == 'GET':
        form.username.data = user.username
        form.full_name.data = user.full_name
        form.email.data = user.email

        # التحقق من الدور الحالي للمستخدم
        # استخدم خاصية role التي تم تعديلها للتعامل مع حالة عدم وجود عمود user_role
        # القيمة الافتراضية ستكون 'admin' إذا كان المستخدم مديراً، وإلا 'monitor'
        form.role.data = user.role

        if user.warehouses:
            form.warehouses.data = user.warehouses[0].id

    if form.validate_on_submit():
        # Check if username is changed and already exists
        if user.username != form.username.data and User.query.filter_by(username=form.username.data).first():
            flash('اسم المستخدم موجود بالفعل. الرجاء اختيار اسم آخر.', 'danger')
            return render_template('edit_user.html', form=form, user=user, title='تعديل المستخدم')

        # Check if email is changed and already exists
        if user.email != form.email.data and User.query.filter_by(email=form.email.data).first():
            flash('البريد الإلكتروني موجود بالفعل. الرجاء استخدام بريد إلكتروني آخر.', 'danger')
            return render_template('edit_user.html', form=form, user=user, title='تعديل المستخدم')

        # مدير المستودعات لا يمكنه تعديل مستخدم ليصبح بصلاحية مدير نظام
        if current_user.is_warehouse_manager and form.role.data == 'admin':
            flash('ليس لديك صلاحية لتعيين مستخدمين بصلاحية مدير نظام', 'danger')
            return render_template('edit_user.html', form=form, user=user, title='تعديل المستخدم')

        # إزالة القيد على عدد المستخدمين بصلاحية مدير المستودعات
        # if form.role.data == 'warehouse_manager' and user.role != 'warehouse_manager':
        #     # التحقق من عدم وجود مستخدم آخر بصلاحية مدير المستودعات
        #     if User.query.filter_by(user_role='warehouse_manager').count() > 0:
        #         flash('لا يمكن وجود أكثر من مستخدم واحد بصلاحية مدير المستودعات', 'danger')
        #         return render_template('edit_user.html', form=form, user=user, title='تعديل المستخدم')

        user.username = form.username.data
        user.full_name = form.full_name.data
        user.email = form.email.data

        # تحديث الدور الجديد في قاعدة البيانات
        user.role = form.role.data  # هذا سيقوم بتحديث user_role و is_admin معاً
        # حفظ الدور في جلسة المستخدم للتوافق مع الكود القديم
        user.save_role_to_session(session)

        # Update warehouse association
        user.warehouses.clear()

        # إذا كان المستخدم مدير نظام أو مدير مستودعات، منحه صلاحية على جميع المستودعات
        if form.role.data == 'admin' or form.role.data == 'warehouse_manager':
            warehouses = Warehouse.query.all()
            user.warehouses.extend(warehouses)
            warehouse = warehouses[0] if warehouses else None
        elif form.role.data == 'company_duty':
            # مناوب السرية لا يحتاج إلى مستودعات (يعمل على كشف الاستلامات وإدارة المواقع فقط)
            warehouse = None
        else:
            # للأدوار الأخرى، منح صلاحية على المستودع المحدد فقط
            if form.warehouses.data:
                warehouse = Warehouse.query.get(form.warehouses.data)
                if warehouse:
                    user.warehouses.append(warehouse)
            else:
                # في حالة عدم تحديد مستودع، استخدم المستودع الأول
                warehouses = Warehouse.query.all()
                if warehouses:
                    warehouse = warehouses[0]
                    user.warehouses.append(warehouse)
                else:
                    warehouse = None

        try:
            # Log the user update
            # للمستخدمين بدون مستودعات (مناوب السرية)، استخدم المستودع الأول كمرجع للسجل
            log_warehouse_id = warehouse.id if warehouse else (current_user.warehouses[0].id if current_user.warehouses else 1)
            log = ActivityLog(
                action="تعديل مستخدم",
                description=f"تم تعديل بيانات المستخدم {user.username} بواسطة {current_user.username}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=log_warehouse_id
            )
            db.session.add(log)

            db.session.commit()
            flash('تم تعديل المستخدم بنجاح!', 'success')
            return redirect(url_for('auth.users'))
        except Exception as e:
            db.session.rollback()
            # تحقق من نوع الخطأ وعرض رسالة مناسبة
            if 'users_email_key' in str(e):
                flash('البريد الإلكتروني موجود بالفعل. الرجاء استخدام بريد إلكتروني آخر.', 'danger')
            elif 'users_username_key' in str(e):
                flash('اسم المستخدم موجود بالفعل. الرجاء اختيار اسم آخر.', 'danger')
            else:
                flash(f'حدث خطأ أثناء تعديل المستخدم: {str(e)}', 'danger')

    return render_template('edit_user.html', form=form, user=user, title='تعديل المستخدم')

@auth_bp.route('/users/<int:user_id>/reset-password', methods=['GET', 'POST'])
@login_required
def reset_user_password(user_id):
    # Only admins and warehouse managers can reset passwords, or users can reset their own password
    if not (current_user.is_admin_role or current_user.is_warehouse_manager) and current_user.id != user_id:
        flash('ليس لديك صلاحية لإعادة تعيين كلمات المرور', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    user = User.query.get_or_404(user_id)

    # مدير المستودعات لا يمكنه إعادة تعيين كلمة مرور مستخدم بصلاحية مدير نظام
    if current_user.is_warehouse_manager and user.role == 'admin':
        flash('ليس لديك صلاحية لإعادة تعيين كلمة مرور مستخدمين بصلاحية مدير نظام', 'danger')
        return redirect(url_for('auth.users'))

    form = ResetPasswordForm()

    if form.validate_on_submit():
        user.set_password(form.new_password.data)

        # Log the password reset
        log = ActivityLog(
            action="إعادة تعيين كلمة المرور",
            description=f"تم إعادة تعيين كلمة المرور للمستخدم {user.username} بواسطة {current_user.username}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=user.warehouses[0].id if user.warehouses else 1
        )
        db.session.add(log)

        db.session.commit()
        flash('تم إعادة تعيين كلمة المرور بنجاح!', 'success')
        return redirect(url_for('auth.users'))

    return render_template('reset_password.html', form=form, user=user, title='إعادة تعيين كلمة المرور')

# Initialize the first admin user if no users exist
def init_admin():
    if User.query.count() == 0:
        # Create default warehouses if they don't exist
        default_warehouses = [
            {'name': 'المستودع الأول', 'description': 'المستودع الرئيسي الأول للأسلحة والمعدات'},
            {'name': 'المستودع الثاني', 'description': 'المستودع الرئيسي الثاني للأسلحة والمعدات'},
            {'name': 'المخزون العام', 'description': 'مخزون عام للأسلحة والمعدات'}
        ]

        warehouses = []
        for wh in default_warehouses:
            warehouse = Warehouse.query.filter_by(name=wh['name']).first()
            if not warehouse:
                warehouse = Warehouse(name=wh['name'], description=wh['description'])
                db.session.add(warehouse)
            warehouses.append(warehouse)

        db.session.flush()  # Ensure warehouses have IDs

        # Create default admin user
        admin = User(
            username='admin',
            full_name='مدير النظام',
            email='<EMAIL>',
            is_admin=True,  # للتوافق مع الكود القديم
            is_active=True
        )
        # تعيين الدور الجديد
        admin.role = 'admin'  # هذا سيقوم بتحديث user_role و is_admin معاً
        admin.set_password('admin123')
        admin.warehouses = warehouses  # Set warehouses directly

        db.session.add(admin)
        db.session.flush()  # Get admin ID

        # Log the user creation
        for warehouse in warehouses:
            log = ActivityLog(
                action="إنشاء نظام",
                description="تم إنشاء مدير النظام الافتراضي وتهيئة المستودعات",
                ip_address="127.0.0.1",
                user_id=admin.id,
                warehouse_id=warehouse.id
            )
            db.session.add(log)

        db.session.commit()
