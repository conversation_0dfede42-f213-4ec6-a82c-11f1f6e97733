{% extends "base.html" %}

{% block title %}شاشة عرض المستودع الأول{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/display-screens-custom.css') }}">
{% endblock %}

{% block content %}
<div class="display-screen">
    <!-- Header -->
    <div class="display-header">
        <h1>شاشة عرض المستودع الأول</h1>
        <div class="display-date" id="current-date"></div>
        <img src="{{ url_for('static', filename='images/KSA.png') }}" alt="شعار" class="display-logo">
    </div>

    <!-- Navigation Tabs -->
    <div class="display-tabs">
        <div class="display-tab active">
            <i class="fas fa-warehouse"></i>
            <span>المستودع الأول</span>
        </div>
        {% if warehouse.capacity > 0 %}
        <div class="display-tab">
            <i class="fas fa-warehouse"></i>
            <span>الطاقة: {{ weapons_count }}/{{ warehouse.capacity }} ({{ warehouse.get_usage_percentage() }}%)</span>
        </div>
        {% endif %}
        <div class="display-tab">
            <i class="fas fa-sync"></i>
            <span>آخر تحديث: <span id="last-update"></span></span>
        </div>
    </div>

    <!-- Content -->
    <div class="display-content">
        <div class="display-row">
            <!-- Personnel Panel -->
            <div class="display-col">
                <div class="personnel-panel">
                    <div class="personnel-panel-header">
                        <h2><i class="fas fa-users"></i> الأفراد</h2>
                        <div class="count">{{ personnel_count }}</div>
                    </div>
                    <div class="personnel-chart-container">
                        <canvas id="personnelChart"></canvas>
                    </div>
                    <div class="personnel-legend">
                        <div class="legend-item">
                            <div class="legend-color legend-active"></div>
                            <span>نشط</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-leave"></div>
                            <span>إجازة</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-mission"></div>
                            <span>مهمة</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-cycle"></div>
                            <span>دورة</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-retired"></div>
                            <span>متقاعد</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Weapons Panel -->
            <div class="display-col">
                <div class="weapons-panel">
                    <div class="weapons-panel-header">
                        <h2><i class="fas fa-crosshairs"></i> الأسلحة</h2>
                        <div class="count">{{ weapons_count }}</div>
                    </div>
                    <div class="weapons-status-bars">
                        <div class="weapon-status-bar">
                            <div class="weapon-status-label">نشط</div>
                            <div class="weapon-status-progress">
                                <div class="weapon-status-progress-bar" style="width: {{ (weapon_status.active / weapons_count * 100) if weapons_count else 0 }}%; background-color: #28a745;"></div>
                            </div>
                            <div class="weapon-status-count">{{ weapon_status.active }}</div>
                        </div>
                        <div class="weapon-status-bar">
                            <div class="weapon-status-label">إجازة</div>
                            <div class="weapon-status-progress">
                                <div class="weapon-status-progress-bar" style="width: {{ (weapon_status.leave / weapons_count * 100) if weapons_count else 0 }}%; background-color: #ffc107;"></div>
                            </div>
                            <div class="weapon-status-count">{{ weapon_status.leave }}</div>
                        </div>
                        <div class="weapon-status-bar">
                            <div class="weapon-status-label">مهمة</div>
                            <div class="weapon-status-progress">
                                <div class="weapon-status-progress-bar" style="width: {{ (weapon_status.mission / weapons_count * 100) if weapons_count else 0 }}%; background-color: #17a2b8;"></div>
                            </div>
                            <div class="weapon-status-count">{{ weapon_status.mission }}</div>
                        </div>
                        <div class="weapon-status-bar">
                            <div class="weapon-status-label">صيانة</div>
                            <div class="weapon-status-progress">
                                <div class="weapon-status-progress-bar" style="width: {{ (weapon_status.maintenance / weapons_count * 100) if weapons_count else 0 }}%; background-color: #6c757d;"></div>
                            </div>
                            <div class="weapon-status-count">{{ weapon_status.maintenance }}</div>
                        </div>
                        <div class="weapon-status-bar">
                            <div class="weapon-status-label">تالف</div>
                            <div class="weapon-status-progress">
                                <div class="weapon-status-progress-bar" style="width: {{ (weapon_status.damaged / weapons_count * 100) if weapons_count else 0 }}%; background-color: #dc3545;"></div>
                            </div>
                            <div class="weapon-status-count">{{ weapon_status.damaged }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions Panel -->
        <div class="transactions-panel">
            <div class="transactions-panel-header">
                <h2><i class="fas fa-exchange-alt"></i> آخر المعاملات</h2>
                <button class="panel-toggle-btn" data-target="transactions-list" title="طي">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
            <div class="transactions-list" id="transactions-list">
                {% for transaction in recent_transactions %}
                <div class="transaction-item">
                    <div class="transaction-info">
                        {% if transaction.transaction_type == 'checkout' %}
                        <i class="fas fa-arrow-right"></i> تم تسليم
                        {% elif transaction.transaction_type == 'return' %}
                        <i class="fas fa-arrow-left"></i> تم استلام
                        {% elif transaction.transaction_type == 'transfer' %}
                        <i class="fas fa-exchange-alt"></i> تم نقل
                        {% endif %}
                        {{ transaction.weapon.name }} ({{ transaction.weapon.serial_number }})
                        {{ transaction.personnel.name }} ({{ transaction.personnel.personnel_id }})
                    </div>
                    <div class="transaction-time">{{ transaction.timestamp.strftime('%H:%M') }}</div>
                </div>
                {% else %}
                <div class="no-data">لا توجد معاملات حتى الآن</div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/Chart.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set up the current date and time
        function updateDateTime() {
            const now = new Date();
            const dateElement = document.getElementById('current-date');
            const lastUpdateElement = document.getElementById('last-update');

            // Format: Day DD/MM/YYYY at HH:MM:SS
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'numeric',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };

            dateElement.textContent = now.toLocaleDateString('ar-SA', options);
            lastUpdateElement.textContent = now.toLocaleTimeString('ar-SA');
        }

        updateDateTime();
        setInterval(updateDateTime, 1000);

        // Set up personnel chart
        const personnelCtx = document.getElementById('personnelChart').getContext('2d');
        new Chart(personnelCtx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'إجازة', 'مهمة', 'دورة', 'مستلم'],
                datasets: [{
                    data: [{{ active_personnel }}, {{ leave_personnel }}, {{ mission_personnel }}, {{ cycle_personnel|default(0) }}, {{ stoped_personnel }}],
                    backgroundColor: [
                        '#28a745', // Green for active
                        '#ffc107', // Yellow for leave
                        '#fd7e14', // Orange for mission
                        '#dc3545', // Red for cycle
                        '#007bff'  // Blue for retired (مستلم)
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutoutPercentage: 70,
                legend: {
                    display: false
                },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            const dataset = data.datasets[tooltipItem.datasetIndex];
                            const total = dataset.data.reduce((acc, value) => acc + value, 0);
                            const value = dataset.data[tooltipItem.index];
                            const percentage = Math.round((value / total) * 100);
                            return `${data.labels[tooltipItem.index]}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        });

        // Periodically refresh the data
        function refreshData() {
            fetch('/api/warehouse/{{ warehouse.id }}/stats')
                .then(response => response.json())
                .then(data => {
                    // Update counts and statistics
                    console.log("Refreshed data:", data);
                    // Implement real-time updates here
                })
                .catch(error => {
                    console.error('Error fetching updated data:', error);
                });
        }

        // Refresh every 30 seconds
        setInterval(refreshData, 30000);
    });
</script>
{% endblock %}
