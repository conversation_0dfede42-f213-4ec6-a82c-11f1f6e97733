{% extends "base.html" %}

{% block title %}إضافة صنف جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-plus text-primary"></i>
                    إضافة صنف جديد للمخزون
                </h2>
                <a href="{{ url_for('standalone_inventory.items') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Category Selection -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i>
                        اختر نوع الصنف المراد إضافته
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">اختر الفئة المناسبة للصنف الذي تريد إضافته للمخزون:</p>

                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card border-left-success h-100 category-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-tshirt fa-4x text-success mb-3"></i>
                                    <h5 class="card-title">الملبوسات</h5>
                                    <p class="card-text text-muted">إضافة ملبوسات مثل الزي الرسمي، الأحذية، القبعات، والإكسسوارات</p>
                                    <ul class="list-unstyled text-muted small mb-3">
                                        <li>• الزي الرسمي</li>
                                        <li>• الأحذية العسكرية</li>
                                        <li>• القبعات والطواقي</li>
                                        <li>• الأحزمة والإكسسوارات</li>
                                    </ul>
                                    <a href="{{ url_for('standalone_inventory.add_clothing') }}" class="btn btn-success btn-lg">
                                        <i class="fas fa-plus"></i> إضافة ملبوسات
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card border-left-info h-100 category-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-tools fa-4x text-info mb-3"></i>
                                    <h5 class="card-title">المعدات</h5>
                                    <p class="card-text text-muted">إضافة معدات مثل الخوذات، السترات الواقية، والمعدات التقنية</p>
                                    <ul class="list-unstyled text-muted small mb-3">
                                        <li>• الخوذات الواقية</li>
                                        <li>• السترات الواقية</li>
                                        <li>• المعدات التقنية</li>
                                        <li>• أدوات الصيانة</li>
                                    </ul>
                                    <a href="{{ url_for('standalone_inventory.add_equipment') }}" class="btn btn-info btn-lg">
                                        <i class="fas fa-plus"></i> إضافة معدات
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card border-left-danger h-100 category-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-bomb fa-4x text-danger mb-3"></i>
                                    <h5 class="card-title">الذخيرة</h5>
                                    <p class="card-text text-muted">إضافة أنواع مختلفة من الذخيرة والمواد المتفجرة</p>
                                    <ul class="list-unstyled text-muted small mb-3">
                                        <li>• ذخيرة الأسلحة الخفيفة</li>
                                        <li>• ذخيرة الأسلحة الثقيلة</li>
                                        <li>• القنابل والمتفجرات</li>
                                        <li>• المواد الكيميائية</li>
                                    </ul>
                                    <a href="{{ url_for('standalone_inventory.add_ammunition') }}" class="btn btn-danger btn-lg">
                                        <i class="fas fa-plus"></i> إضافة ذخيرة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Info Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> معلومات مهمة:</h6>
                <ul class="mb-0">
                    <li>كل فئة لها نموذج مخصص يحتوي على الحقول المناسبة لنوع الصنف</li>
                    <li>يمكنك استخدام ماسح الباركود لإدخال أكواد الأصناف بسرعة</li>
                    <li>سيتم إنشاء كود تلقائي للصنف إذا لم تقم بإدخال كود مخصص</li>
                    <li>جميع الأصناف المضافة ستظهر في قائمة المخزون الموحدة</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تأثيرات بصرية للبطاقات
    $('.category-card').hover(
        function() {
            $(this).addClass('shadow-lg').css('transform', 'translateY(-5px)');
        },
        function() {
            $(this).removeClass('shadow-lg').css('transform', 'translateY(0)');
        }
    );
});
</script>

<style>
.category-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.border-left-success {
    border-left: 4px solid #28a745 !important;
}

.border-left-info {
    border-left: 4px solid #17a2b8 !important;
}

.border-left-danger {
    border-left: 4px solid #dc3545 !important;
}
</style>
{% endblock %}
