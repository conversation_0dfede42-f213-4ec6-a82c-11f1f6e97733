/**
 * إصلاح نهائي للقوائم المنسدلة في صفحة قائمة الأسلحة
 */
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق الإصلاح فقط في صفحة قائمة الأسلحة
    if (document.querySelector('.table-responsive')) {
        // الحصول على جميع أزرار القوائم المنسدلة في الجدول
        const dropdownButtons = document.querySelectorAll('.weapons-dropdown-toggle');

        // إضافة معالج حدث لكل زر
        dropdownButtons.forEach(function(button) {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();

                // إغلاق جميع القوائم المنسدلة الأخرى
                document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                    if (!button.parentNode.contains(menu)) {
                        menu.classList.remove('show');
                    }
                });

                // الحصول على القائمة المنسدلة المرتبطة بالزر
                const parent = button.closest('.btn-block');
                if (parent) {
                    const menu = parent.querySelector('.dropdown-menu');
                    if (menu) {
                        // تبديل حالة القائمة المنسدلة
                        menu.classList.toggle('show');

                        // تحديد موضع القائمة المنسدلة بناءً على موضع الزر
                        const buttonRect = button.getBoundingClientRect();
                        const tableRect = document.querySelector('.table-responsive').getBoundingClientRect();

                        // تعيين موضع القائمة المنسدلة بشكل مطلق
                        menu.style.position = 'absolute';
                        menu.style.top = (buttonRect.bottom - tableRect.top + document.querySelector('.table-responsive').scrollTop) + 'px';
                        menu.style.left = (buttonRect.left - tableRect.left + document.querySelector('.table-responsive').scrollLeft - menu.offsetWidth + button.offsetWidth) + 'px';
                        menu.style.right = 'auto';
                        menu.style.zIndex = '9999';

                        // إغلاق القائمة عند النقر في أي مكان آخر
                        const closeMenu = function(e) {
                            if (!button.contains(e.target) && !menu.contains(e.target)) {
                                menu.classList.remove('show');
                                document.removeEventListener('click', closeMenu);
                            }
                        };

                        // إضافة معالج الحدث بعد تأخير قصير لتجنب إغلاق القائمة فوراً
                        setTimeout(function() {
                            document.addEventListener('click', closeMenu);
                        }, 10);
                    }
                }
            });
        });

        // إضافة معالج حدث للنقر على عناصر القائمة المنسدلة
        document.querySelectorAll('.dropdown-item').forEach(function(item) {
            item.addEventListener('click', function(event) {
                // إغلاق القائمة المنسدلة بعد النقر على أحد عناصرها
                const menu = item.closest('.dropdown-menu');
                if (menu) {
                    menu.classList.remove('show');
                }
            });
        });
    }
});
