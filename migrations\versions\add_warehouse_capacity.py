"""إضافة حقل الطاقة الاستيعابية للمستودعات

Revision ID: add_warehouse_capacity
Revises: 8727fcabb738
Create Date: 2025-01-31 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_warehouse_capacity'
down_revision = 'add_temp_data_tables'
branch_labels = None
depends_on = None


def upgrade():
    """إضافة عمود capacity إلى جدول warehouses"""
    with op.batch_alter_table('warehouses', schema=None) as batch_op:
        batch_op.add_column(sa.Column('capacity', sa.Integer(), nullable=True, default=0))
    
    # تحديث القيم الافتراضية للمستودعات الموجودة
    op.execute("UPDATE warehouses SET capacity = 100 WHERE capacity IS NULL")
    
    # جعل العمود غير قابل للقيم الفارغة
    with op.batch_alter_table('warehouses', schema=None) as batch_op:
        batch_op.alter_column('capacity', nullable=False)


def downgrade():
    """إزالة عمود capacity من جدول warehouses"""
    with op.batch_alter_table('warehouses', schema=None) as batch_op:
        batch_op.drop_column('capacity')
