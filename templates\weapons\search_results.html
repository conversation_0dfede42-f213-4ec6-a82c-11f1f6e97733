{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>نتائج البحث: "{{ query }}"</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('weapons.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة الأسلحة
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div class="me-0">
            <h5 class="mb-0"><i class="fas fa-search"></i> نتائج البحث ({{ weapons|length }})</h5>
        </div>
        <div>
            <form action="{{ url_for('weapons.search') }}" method="GET" class="d-flex" id="searchForm">
                <input type="text" name="q" class="form-control ms-2" placeholder="بحث عن الأسلحة..."
                    value="{{ query }}" id="searchInput" autocomplete="off">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="card-body">
        {% if weapons|length == 0 %}
        <div class="alert alert-info">
            لا توجد نتائج مطابقة لـ "{{ query }}". حاول استخدام كلمات بحث مختلفة.
        </div>
        {% else %}
        <div class="table-responsive">
            <table class="table table-hover table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>رقم الحفظ</th>
                        <th>الرقم التسلسلي</th>
                        <th>اسم السلاح</th>
                        <th>رقم السلاح</th>
                        <th>الحالة</th>
                        <th>المستودع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for weapon in weapons %}
                    <tr>
                        <td>{{ weapon.weapon_number or '-' }}</td>
                        <td>{{ weapon.serial_number }}</td>
                        <td>{{ weapon.name }}</td>
                        <td>
                            {% if weapon.type == 'pistol' %}
                            مسدس
                            {% elif weapon.type == 'rifle' %}
                            بندقية
                            {% elif weapon.type == 'sniper' %}
                            قناص
                            {% elif weapon.type == 'machine_gun' %}
                            رشاش
                            {% else %}
                            {{ weapon.type }}
                            {% endif %}
                        </td>
                        <td>
                            {% if weapon.status == 'نشط' %}
                            <span class="badge bg-success">{{ weapon.status }}</span>
                            {% elif weapon.status == 'إجازة' %}
                            <span class="badge bg-warning">{{ weapon.status }}</span>
                            {% elif weapon.status == 'مهمة' %}
                            <span class="badge bg-mission">{{ weapon.status }}</span>
                            {% elif weapon.status == 'صيانة' %}
                            <span class="badge bg-maintenance">{{ weapon.status }}</span>
                            {% elif weapon.status == 'تالف' %}
                            <span class="badge bg-primary">{{ weapon.status }}</span>
                            {% elif weapon.status == 'دورة' %}
                            <span class="badge bg-danger">{{ weapon.status }}</span>
                            {% elif weapon.status == 'شاغر' %}
                            <span class="badge bg-dark">{{ weapon.status }}</span>
                            {% elif weapon.status == 'مستلم' %}
                            <span class="badge bg-primary">{{ weapon.status }}</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ weapon.status }}</span>
                            {% endif %}
                        </td>
                        <td>{{ weapon.warehouse.name }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('weapons.details', weapon_id=weapon.id) }}"
                                    class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.is_admin %}
                                <a href="{{ url_for('weapons.edit', weapon_id=weapon.id) }}"
                                    class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const searchForm = document.getElementById('searchForm');

        // تعيين التركيز على حقل البحث عند تحميل الصفحة ووضع المؤشر في نهاية النص
        searchInput.focus();
        // وضع المؤشر في نهاية النص
        const inputValue = searchInput.value;
        searchInput.value = '';
        searchInput.value = inputValue;

        // متغير لتخزين الوقت المستغرق بين الإدخالات
        let typingTimer;
        // الفترة الزمنية بالمللي ثانية قبل إرسال النموذج (300 مللي ثانية = 0.3 ثانية)
        const doneTypingInterval = 300;

        // عند الكتابة في حقل البحث
        searchInput.addEventListener('input', function() {
            // إعادة ضبط المؤقت في كل مرة يتم فيها الكتابة
            clearTimeout(typingTimer);

            // إذا كان الحقل غير فارغ، ابدأ المؤقت
            if (searchInput.value) {
                typingTimer = setTimeout(submitForm, doneTypingInterval);
            }
        });

        // وظيفة إرسال النموذج
        function submitForm() {
            searchForm.submit();
        }

        // عند مسح الحقل بالكامل، قم بإرسال النموذج أيضًا للعودة إلى القائمة الكاملة
        searchInput.addEventListener('keyup', function(e) {
            if (searchInput.value === '' && e.key === 'Backspace') {
                submitForm();
            }
        });
    });
</script>
{% endblock %}