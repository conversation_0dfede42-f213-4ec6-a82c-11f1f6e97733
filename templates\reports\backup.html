{% extends "base.html" %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/backup-dashboard.css') }}">
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            <i class="fas fa-server"></i> النسخ الاحتياطي واستعادة البيانات
        </h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى التقارير
        </a>
    </div>
</div>



<!-- لوحة الإحصائيات -->
<div class="backup-dashboard mb-4">
    <div class="row">
        <div class="col-md-3">
            <div class="stats-card success">
                <div class="card-icon">
                    <i class="fas fa-save"></i>
                </div>
                <div class="card-title">إجمالي النسخ الاحتياطية</div>
                <div class="card-value">{{ backup_records|length }}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card info">
                <div class="card-icon">
                    <i class="fas fa-database"></i>
                </div>
                <div class="card-title">إجمالي الحجم</div>
                <div class="card-value">{{ (backup_records|sum(attribute='file_size') / (1024*1024))|round(2) }} ميجابايت</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card warning">
                <div class="card-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="card-title">جدولات نشطة</div>
                <div class="card-value">{{ backup_schedules|selectattr('is_active', 'equalto', true)|list|length }}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="card-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="card-title">آخر نسخة احتياطية</div>
                <div class="card-value">{{ format_datetime_12h(backup_records[0].timestamp).split(' ')[0] if backup_records else 'لا يوجد' }}</div>
            </div>
        </div>
    </div>
</div>

<!-- أزرار إنشاء النسخ الاحتياطية -->
<div class="row mb-4">
    <div class="col-md-12 text-center">
        <form method="POST" action="{{ url_for('reports.backup') }}" class="d-inline-block">
            <input type="hidden" name="action" value="backup">
            <input type="hidden" name="csrf_token" value="{{csrf_token()}}">
            <input type="hidden" name="warehouse_id" value="0">
            <button type="submit" class="btn btn-primary btn-lg mx-2 backup-btn">
                <i class="fas fa-download"></i> إنشاء نسخة احتياطية كاملة
            </button>
        </form>
        <div class="btn-group mx-2 d-inline-block">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-download"></i> نسخة احتياطية لمستودع محدد
            </button>
            <ul class="dropdown-menu">
                {% for warehouse in warehouses %}
                <li>
                    <form method="POST" action="{{ url_for('reports.backup') }}">
                        <input type="hidden" name="action" value="backup">
                        <input type="hidden" name="csrf_token" value="{{csrf_token()}}">
                        <input type="hidden" name="warehouse_id" value="{{ warehouse.id }}">
                        <button type="submit" class="dropdown-item">
                            {{ warehouse.name }}
                        </button>
                    </form>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>

<!-- Nav tabs -->
<div class="card mb-4">
    <div class="card-body p-0">
        <ul class="nav nav-tabs nav-fill" id="backupTabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="history-tab" data-bs-toggle="tab" href="#history" role="tab" aria-controls="history" aria-selected="true">
                    <i class="fas fa-history"></i> سجل النسخ الاحتياطية
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="manual-tab" data-bs-toggle="tab" href="#manual" role="tab" aria-controls="manual" aria-selected="false">
                    <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="restore-tab" data-bs-toggle="tab" href="#restore" role="tab" aria-controls="restore" aria-selected="false">
                    <i class="fas fa-undo-alt"></i> استعادة البيانات
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="schedule-tab" data-bs-toggle="tab" href="#schedule" role="tab" aria-controls="schedule" aria-selected="false">
                    <i class="fas fa-calendar-alt"></i> جدولة النسخ الاحتياطي
                </a>
            </li>
        </ul>
    </div>
</div>

<!-- Tab panes -->
<div class="tab-content">
    <!-- Backup History Tab -->
    <div class="tab-pane fade show active" id="history" role="tabpanel" aria-labelledby="history-tab">
        <div class="backup-table">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-history"></i> سجل النسخ الاحتياطية</h5>
                <div>
                    <button class="btn btn-sm btn-light" id="refresh-history">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>المستودع</th>
                                <th>الحجم</th>
                                <th>المستخدم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for backup in backup_records %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ format_datetime_12h(backup.timestamp) }}</td>
                                <td>
                                    {% if backup.backup_type == 'full' %}
                                    <span class="badge badge-primary">كامل</span>
                                    {% else %}
                                    <span class="badge badge-secondary">{{ backup.backup_type }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ backup.warehouse.name if backup.warehouse else 'جميع المستودعات' }}</td>
                                <td>{{ (backup.file_size / 1024)|round(1) }} KB</td>
                                <td>{{ backup.user.username }}</td>
                                <td>
                                    <div class="d-flex justify-content-between">
                                        <form method="POST" action="{{ url_for('reports.backup') }}" class="mx-1">
                                            <input type="hidden" name="action" value="restore">
                                            <input type="hidden" name="csrf_token" value="{{csrf_token()}}">
                                            <input type="hidden" name="backup_id" value="{{ backup.id }}">
                                            <button type="submit" class="btn btn-sm btn-warning restore-btn"
                                                onclick="return confirm('هل أنت متأكد من استعادة هذه النسخة؟ سيتم استبدال البيانات الحالية!');">
                                                <i class="fas fa-undo"></i> استعادة
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ url_for('reports.backup') }}" class="mx-1">
                                            <input type="hidden" name="action" value="download">
                                            <input type="hidden" name="csrf_token" value="{{csrf_token()}}">
                                            <input type="hidden" name="backup_id" value="{{ backup.id }}">
                                            <button type="submit" class="btn btn-sm btn-info">
                                                <i class="fas fa-download"></i> تنزيل
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ url_for('reports.backup') }}" class="mx-1">
                                            <input type="hidden" name="action" value="delete_backup">
                                            <input type="hidden" name="csrf_token" value="{{csrf_token()}}">
                                            <input type="hidden" name="backup_id" value="{{ backup.id }}">
                                            <button type="submit" class="btn btn-sm btn-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذه العملية!');">
                                                <i class="fas fa-trash"></i> حذف
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center py-3">
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle"></i> لم يتم إنشاء أي نسخة احتياطية حتى الآن
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="progress backup-progress" id="restore-progress" style="display: none; margin-top: 20px;">
            <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
        </div>
    </div>

    <!-- Manual Backup Tab -->
    <div class="tab-pane fade" id="manual" role="tabpanel" aria-labelledby="manual-tab">
        <div class="row">
            <div class="col-md-12">
                <div class="backup-table mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-download"></i> إنشاء نسخة احتياطية جديدة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center border-end">
                                <form method="POST" action="{{ url_for('reports.backup') }}" id="quick-backup-form">
                                    <input type="hidden" name="action" value="backup">
                                    <input type="hidden" name="csrf_token" value="{{csrf_token()}}">
                                    <input type="hidden" name="warehouse_id" value="0">

                                    <div class="stats-card success mb-3">
                                        <div class="card-icon">
                                            <i class="fas fa-download"></i>
                                        </div>
                                        <div class="card-title">نسخة احتياطية كاملة</div>
                                        <p class="mt-2">قم بإنشاء نسخة احتياطية كاملة لجميع البيانات بنقرة واحدة</p>
                                    </div>

                                    <button type="submit" class="btn btn-primary btn-lg backup-btn">
                                        <i class="fas fa-download"></i> إنشاء نسخة احتياطية كاملة
                                    </button>
                                </form>
                            </div>

                            <div class="col-md-8">
                                <form method="POST" action="{{ url_for('reports.backup') }}" id="backup-form">
                                    <input type="hidden" name="action" value="backup">
                                    <input type="hidden" name="csrf_token" value="{{csrf_token()}}">

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="warehouse_id" class="form-label">المستودع</label>
                                                <select name="warehouse_id" id="warehouse_id" class="form-select">
                                                    <option value="0">جميع المستودعات</option>
                                                    {% for warehouse in warehouses %}
                                                    <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                                    {% endfor %}
                                                </select>
                                                <small class="form-text text-muted">اختر المستودع الذي تريد إنشاء نسخة احتياطية له، أو اختر "جميع المستودعات" لإنشاء نسخة احتياطية كاملة.</small>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">الحجم المقدر</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-hdd"></i></span>
                                                    <input type="text" class="form-control" id="estimated-size" value="2.5 ميجابايت" readonly>
                                                </div>
                                                <small class="form-text text-muted">الحجم التقريبي للنسخة الاحتياطية بناءً على الخيارات المحددة.</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <button type="button" id="advanced-options-toggle" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-chevron-down"></i> عرض الخيارات المتقدمة
                                        </button>
                                    </div>

                                    <div id="advanced-options" style="display: none;" class="advanced-options mb-4 p-3 border rounded">
                                        <h6 class="mb-3">محتوى النسخة الاحتياطية</h6>
                                        <div class="row" id="backup-content">
                                            <div class="col-md-3">
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input backup-option" type="checkbox" id="option-weapons" name="include_weapons" checked>
                                                    <label class="form-check-label" for="option-weapons">
                                                        <i class="fas fa-gun me-2"></i> الأسلحة والذخائر
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input backup-option" type="checkbox" id="option-personnel" name="include_personnel" checked>
                                                    <label class="form-check-label" for="option-personnel">
                                                        <i class="fas fa-users me-2"></i> الأفراد
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input backup-option" type="checkbox" id="option-transactions" name="include_transactions" checked>
                                                    <label class="form-check-label" for="option-transactions">
                                                        <i class="fas fa-exchange-alt me-2"></i> المعاملات
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input backup-option" type="checkbox" id="option-logs" name="include_logs" checked>
                                                    <label class="form-check-label" for="option-logs">
                                                        <i class="fas fa-history me-2"></i> سجلات النشاط
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <h6 class="mb-3">خيارات إضافية</h6>
                                        <div class="row" id="backup-content-options">
                                            <div class="col-md-4">
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input backup-option" type="checkbox" id="option-compression" name="compression" checked>
                                                    <label class="form-check-label" for="option-compression">
                                                        <i class="fas fa-file-archive me-2"></i> ضغط النسخة الاحتياطية
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input backup-option" type="checkbox" id="option-encryption" name="encryption">
                                                    <label class="form-check-label" for="option-encryption">
                                                        <i class="fas fa-lock me-2"></i> تشفير النسخة الاحتياطية
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="retention-slider" class="form-label">الاحتفاظ بالنسخ (أيام)</label>
                                                    <div class="d-flex align-items-center">
                                                        <input type="range" class="form-range" id="retention-slider" name="retention_days" min="1" max="90" value="30">
                                                        <span id="retention-value" class="ms-2">30</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="progress backup-progress" id="backup-progress" style="display: none;">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                                    </div>

                                    <div class="form-group text-end mt-4">
                                        <button type="submit" id="create-backup-btn" class="btn btn-primary backup-btn">
                                            <i class="fas fa-download"></i> إنشاء نسخة احتياطية مخصصة
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="backup-table">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-upload"></i> رفع نسخة احتياطية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <div class="stats-card info mb-4">
                                    <div class="card-icon">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="card-title">رفع نسخة احتياطية موجودة</div>
                                    <p class="mt-2">يمكنك رفع ملف نسخة احتياطية موجود لديك مسبقاً. يجب أن يكون الملف بصيغة JSON وتم إنشاؤه بواسطة هذا النظام.</p>
                                </div>

                                <form method="POST" action="{{ url_for('reports.backup') }}" enctype="multipart/form-data" class="p-3 border rounded">
                                    <input type="hidden" name="action" value="upload_backup">
                                    <input type="hidden" name="csrf_token" value="{{csrf_token()}}">

                                    <div class="mb-3">
                                        <label for="backup_file" class="form-label">اختر ملف النسخة الاحتياطية</label>
                                        <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".json" required>
                                        <small class="form-text text-muted">يجب أن يكون الملف بصيغة JSON</small>
                                    </div>

                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary btn-upload-backup btn-lg">
                                            <i class="fas fa-upload"></i> رفع النسخة الاحتياطية
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Backup Tab -->
    <div class="tab-pane fade" id="schedule" role="tabpanel" aria-labelledby="schedule-tab">
        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="stats-card info">
                    <div class="card-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="card-title">جدولة النسخ الاحتياطي</div>
                    <p class="mt-2">يمكنك جدولة النسخ الاحتياطي بشكل يومي أو أسبوعي أو شهري لضمان حماية بياناتك بشكل دوري.</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-5">
                <div class="backup-table mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-calendar-plus"></i> إنشاء جدولة جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('reports.backup') }}">
                            <input type="hidden" name="action" value="create_schedule">
                            <input type="hidden" id="csrf_token" name="csrf_token" value="{{csrf_token()}}">

                            <div class="mb-3">
                                <label for="schedule_type" class="form-label">{{ schedule_form.schedule_type.label }}</label>
                                {{ schedule_form.schedule_type(class="form-select", id="schedule_type") }}
                            </div>

                            <div class="mb-3" id="day_of_week_group" style="display: none;">
                                <label for="day_of_week" class="form-label">{{ schedule_form.day_of_week.label }}</label>
                                {{ schedule_form.day_of_week(class="form-select", id="day_of_week") }}
                            </div>

                            <div class="mb-3" id="day_of_month_group" style="display: none;">
                                <label for="day_of_month" class="form-label">{{ schedule_form.day_of_month.label }}</label>
                                {{ schedule_form.day_of_month(class="form-select", id="day_of_month") }}
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="hour" class="form-label">{{ schedule_form.hour.label }}</label>
                                    {{ schedule_form.hour(class="form-select", id="hour") }}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="minute" class="form-label">{{ schedule_form.minute.label }}</label>
                                    {{ schedule_form.minute(class="form-select", id="minute") }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="backup_type" class="form-label">{{ schedule_form.backup_type.label }}</label>
                                {{ schedule_form.backup_type(class="form-select", id="backup_type") }}
                            </div>

                            <div class="mb-3" id="warehouse_id_group" style="display: none;">
                                <label for="warehouse_id_schedule" class="form-label">{{ schedule_form.warehouse_id.label }}</label>
                                {{ schedule_form.warehouse_id(class="form-select", id="warehouse_id_schedule") }}
                            </div>

                            <div class="mb-3 form-check form-switch">
                                {{ schedule_form.is_active(class="form-check-input", id="is_active") }}
                                <label class="form-check-label" for="is_active">{{ schedule_form.is_active.label }}</label>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary backup-btn">
                                    <i class="fas fa-save"></i> {{ schedule_form.submit.label }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-7">
                <div class="backup-table">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> جدولة النسخ الاحتياطي</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>النوع</th>
                                        <th>التكرار</th>
                                        <th>الوقت</th>
                                        <th>المستودع</th>
                                        <th>الحالة</th>
                                        <th>التشغيل التالي</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for schedule in backup_schedules %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            {% if schedule.backup_type == 'full' %}
                                            <span class="badge bg-primary">كامل</span>
                                            {% else %}
                                            <span class="badge bg-secondary">مستودع</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if schedule.schedule_type == 'daily' %}
                                            <span class="badge bg-info">يومي</span>
                                            {% elif schedule.schedule_type == 'weekly' %}
                                            <span class="badge bg-info">أسبوعي</span>
                                            ({{ ['الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'][schedule.day_of_week] }})
                                            {% elif schedule.schedule_type == 'monthly' %}
                                            <span class="badge bg-info">شهري</span>
                                            (يوم {{ schedule.day_of_month }})
                                            {% endif %}
                                        </td>
                                        <td>{{ format_time_12h(schedule.hour) }}</td>
                                        <td>{{ schedule.warehouse.name if schedule.warehouse else 'جميع المستودعات' }}</td>
                                        <td>
                                            {% if schedule.is_active %}
                                            <span class="badge bg-success">مفعل</span>
                                            {% else %}
                                            <span class="badge bg-danger">معطل</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ format_datetime_12h(schedule.next_run) if schedule.next_run else 'غير محدد' }}</td>
                                        <td>
                                            <div class="d-flex">
                                                <form method="POST" action="{{ url_for('reports.backup') }}" class="me-1">
                                                    <input type="hidden" name="action" value="update_schedule">
                                                    <input type="hidden" id="csrf_token" name="csrf_token" value="{{csrf_token()}}">
                                                    <input type="hidden" name="schedule_id" value="{{ schedule.id }}">
                                                    <input type="checkbox" name="is_active" {% if schedule.is_active %}checked{% endif %} onchange="this.form.submit()" style="display: none;">
                                                    <button type="submit" class="btn btn-sm {% if schedule.is_active %}btn-warning{% else %}btn-success{% endif %}">
                                                        <i class="fas {% if schedule.is_active %}fa-pause{% else %}fa-play{% endif %}"></i>
                                                    </button>
                                                </form>
                                                <form method="POST" action="{{ url_for('reports.backup') }}">
                                                    <input type="hidden" name="action" value="delete_schedule">
                                                    <input type="hidden" id="csrf_token" name="csrf_token" value="{{csrf_token()}}">
                                                    <input type="hidden" name="schedule_id" value="{{ schedule.id }}">
                                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الجدولة؟');">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center py-3">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle"></i> لم يتم إنشاء أي جدولة للنسخ الاحتياطي حتى الآن
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Restore Tab -->
    <div class="tab-pane fade" id="restore" role="tabpanel" aria-labelledby="restore-tab">
        <div class="row">
            <div class="col-md-12">
                <div class="backup-table mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-undo-alt"></i> استعادة البيانات</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> <strong>تحذير:</strong> استعادة النسخة الاحتياطية ستؤدي إلى استبدال جميع البيانات الحالية بالبيانات الموجودة في النسخة الاحتياطية.
                                </div>

                                <form method="POST" action="{{ url_for('reports.backup') }}" id="restore-form" class="p-3 border rounded">
                                    <input type="hidden" name="action" value="restore">
                                    <input type="hidden" id="csrf_token" name="csrf_token" value="{{csrf_token()}}">

                                    <div class="mb-3">
                                        <label for="backup_id" class="form-label">اختر نسخة احتياطية</label>
                                        <select name="backup_id" id="backup_id" class="form-select" required>
                                            <option value="">اختر نسخة احتياطية...</option>
                                            {% for backup in backup_records %}
                                            <option value="{{ backup.id }}">
                                                {{ format_datetime_12h(backup.timestamp) }} -
                                                {% if backup.backup_type == 'full' %}كامل{% else %}{{ backup.warehouse.name }}{% endif %} -
                                                {{ (backup.file_size / 1024)|round(1) }} KB
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label fw-bold">خيارات الاستعادة</label>
                                        <div class="restore-options-container">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" id="restore-weapons" name="restore_weapons" checked>
                                                        <label class="form-check-label" for="restore-weapons">
                                                            <i class="fas fa-gun me-2"></i> استعادة بيانات الأسلحة والذخائر
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" id="restore-personnel" name="restore_personnel" checked>
                                                        <label class="form-check-label" for="restore-personnel">
                                                            <i class="fas fa-users me-2"></i> استعادة بيانات الأفراد
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" id="restore-transactions" name="restore_transactions" checked>
                                                        <label class="form-check-label" for="restore-transactions">
                                                            <i class="fas fa-exchange-alt me-2"></i> استعادة بيانات المعاملات
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" id="restore-logs" name="restore_logs">
                                                        <label class="form-check-label" for="restore-logs">
                                                            <i class="fas fa-history me-2"></i> استعادة سجلات النشاط
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-4 mt-4">
                                        <div class="form-check confirm-restore-check">
                                            <input class="form-check-input" type="checkbox" id="confirm-restore" name="confirm_restore" required>
                                            <label class="form-check-label" for="confirm-restore">
                                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                                <strong>أؤكد أنني أفهم أن هذه العملية ستؤدي إلى استبدال البيانات الحالية</strong>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="progress backup-progress" id="restore-form-progress" style="display: none;">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                                    </div>

                                    <div class="text-center mt-4">
                                        <button type="submit" class="btn btn-warning backup-btn" id="restore-btn">
                                            <i class="fas fa-undo-alt"></i> استعادة البيانات
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <div class="col-md-4">
                                <div class="stats-card info mb-4">
                                    <div class="card-icon">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="card-title">معلومات الاستعادة</div>
                                    <p class="mt-2">يمكنك استعادة البيانات بشكل جزئي باختيار البيانات التي تريد استعادتها فقط.</p>
                                </div>

                                <div class="stats-card danger">
                                    <div class="card-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="card-title">تحذير هام</div>
                                    <p class="mt-2">قبل الاستعادة، ينصح بإنشاء نسخة احتياطية جديدة للبيانات الحالية للتمكن من الرجوع إليها في حالة حدوث أي مشكلة.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- التنبيهات والمعلومات -->
<div class="row mt-5">
    <div class="col-md-6">
        <div class="stats-card info">
            <div class="card-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="card-title">معلومات النسخ الاحتياطي</div>
            <p class="mt-2">يتم حفظ النسخ الاحتياطية في مجلد آمن على الخادم. يمكنك تنزيل النسخ الاحتياطية أو استعادتها في أي وقت.</p>
        </div>
    </div>
    <div class="col-md-6">
        <div class="stats-card warning">
            <div class="card-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="card-title">تنبيه</div>
            <p class="mt-2">استعادة النسخة الاحتياطية ستؤدي إلى استبدال جميع البيانات الحالية بالبيانات الموجودة في النسخة الاحتياطية.</p>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/backup-dashboard.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show/hide fields based on schedule type
        const scheduleTypeSelect = document.getElementById('schedule_type');
        const dayOfWeekGroup = document.getElementById('day_of_week_group');
        const dayOfMonthGroup = document.getElementById('day_of_month_group');

        function updateScheduleFields() {
            const scheduleType = scheduleTypeSelect.value;
            dayOfWeekGroup.style.display = scheduleType === 'weekly' ? 'block' : 'none';
            dayOfMonthGroup.style.display = scheduleType === 'monthly' ? 'block' : 'none';
        }

        scheduleTypeSelect.addEventListener('change', updateScheduleFields);
        updateScheduleFields(); // Initial update

        // Show/hide warehouse field based on backup type
        const backupTypeSelect = document.getElementById('backup_type');
        const warehouseIdGroup = document.getElementById('warehouse_id_group');

        function updateBackupFields() {
            const backupType = backupTypeSelect.value;
            warehouseIdGroup.style.display = backupType === 'warehouse' ? 'block' : 'none';
        }

        backupTypeSelect.addEventListener('change', updateBackupFields);
        updateBackupFields(); // Initial update

        // تحديث سجل النسخ الاحتياطية
        const refreshHistoryBtn = document.getElementById('refresh-history');
        if (refreshHistoryBtn) {
            refreshHistoryBtn.addEventListener('click', function() {
                window.location.href = "{{ url_for('reports.backup') }}" + "?tab=history";
            });
        }

        // التبديل إلى علامة التبويب المحددة في الرابط
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');
        if (tabParam) {
            const tabEl = document.querySelector('#backupTabs a[href="#' + tabParam + '"]');
            if (tabEl) {
                tabEl.click();
            }
        }

        // عرض اسم الملف المختار في نموذج رفع الملفات
        const backupFileInput = document.getElementById('backup_file');
        const backupFileLabel = document.querySelector('.custom-file-label');

        if (backupFileInput && backupFileLabel) {
            backupFileInput.addEventListener('change', function() {
                if (this.files && this.files.length > 0) {
                    backupFileLabel.textContent = this.files[0].name;
                } else {
                    backupFileLabel.textContent = 'اختر ملف...';
                }
            });
        }
    });
</script>
{% endblock %}