// modal-handlers.js
document.addEventListener('DOMContentLoaded', function() {
    // معالجة النقر على روابط فتح النوافذ المنبثقة للصيانة
    const modalLinks = document.querySelectorAll('.maintenance-modal-link');
    
    modalLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // الحصول على معرف النافذة المنبثقة من سمة data-target أو data-bs-target وتحويلها إلى صيغة Bootstrap 5
            const modalId = this.getAttribute('data-bs-target') || this.getAttribute('data-target');
            const modalElement = document.querySelector(modalId);
            
            if (modalElement) {
                // تكوين كائن Modal مع Bootstrap 5
                if (window.bootstrap && window.bootstrap.Modal) {
                    const modalInstance = new window.bootstrap.Modal(modalElement);
                    modalInstance.show();
                } else {
                    // تنفيذ بديل إذا لم تكن مكتبة Bootstrap متاحة
                    modalElement.classList.add('show');
                    modalElement.style.display = 'block';
                    document.body.classList.add('modal-open');
                    
                    // إضافة خلفية داكنة
                    const backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    document.body.appendChild(backdrop);
                    
                    // معالجة إغلاق النافذة المنبثقة
                    const closeButtons = modalElement.querySelectorAll('[data-bs-dismiss="modal"], .btn-close, .close');
                    closeButtons.forEach(function(button) {
                        button.addEventListener('click', function() {
                            modalElement.classList.remove('show');
                            modalElement.style.display = 'none';
                            document.body.classList.remove('modal-open');
                            backdrop.remove();
                        });
                    });
                }
            }
        });
    });
    
    // معالجة روابط النوافذ المنبثقة الأخرى (الإعارة والإعادة)
    document.querySelectorAll('[data-bs-toggle="modal"]').forEach(function(element) {
        if (!element.classList.contains('maintenance-modal-link')) {
            element.addEventListener('click', function(e) {
                const target = this.getAttribute('data-bs-target');
                if (target) {
                    const modalElement = document.querySelector(target);
                    if (modalElement && window.bootstrap && window.bootstrap.Modal) {
                        const modal = new window.bootstrap.Modal(modalElement);
                        modal.show();
                    }
                }
            });
        }
    });
    
    // إضافة معالجة للقوائم المنسدلة
    const dropdownToggleButtons = document.querySelectorAll('.dropdown-toggle');
    
    dropdownToggleButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // استخدام Bootstrap 5 dropdown إذا كان متاحًا
            if (window.bootstrap && window.bootstrap.Dropdown) {
                const dropdown = new window.bootstrap.Dropdown(button);
                dropdown.toggle();
            } else {
                // تنفيذ بديل
                const parent = this.closest('.dropdown, .btn-group');
                if (parent) {
                    const menu = parent.querySelector('.dropdown-menu');
                    if (menu) {
                        menu.classList.toggle('show');
                        
                        // إغلاق القائمة عند النقر في أي مكان آخر
                        const closeMenu = function(event) {
                            if (!parent.contains(event.target)) {
                                menu.classList.remove('show');
                                document.removeEventListener('click', closeMenu);
                            }
                        };
                        
                        document.addEventListener('click', closeMenu);
                    }
                }
            }
        });
    });
});
