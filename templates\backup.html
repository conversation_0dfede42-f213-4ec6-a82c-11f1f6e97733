{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>إدارة النسخ الاحتياطي</h3>
    </div>
    <div class="col-md-4 text-right">
        <div class="btn-group">
            <form method="POST" action="{{ url_for('reports.backup') }}" class="d-inline-block">
                <input type="hidden" name="action" value="backup">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" name="warehouse_id" value="0">
                <button type="submit" class="btn btn-success btn-lg">
                    <i class="fas fa-database"></i> إنشاء نسخة احتياطية كاملة شاملة
                </button>
            </form>
            <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split btn-lg" data-toggle="dropdown"
                aria-haspopup="true" aria-expanded="false">
                <span class="sr-only">Toggle Dropdown</span>
            </button>
            <div class="dropdown-menu dropdown-menu-right">
                <div class="dropdown-header">
                    <i class="fas fa-info-circle"></i> النسخة الكاملة تشمل جميع البيانات
                </div>
                <div class="dropdown-divider"></div>
                <h6 class="dropdown-header">نسخ احتياطية للمستودعات:</h6>
                {% for warehouse in warehouses %}
                <form method="POST" action="{{ url_for('reports.backup') }}" class="d-inline-block w-100">
                    <input type="hidden" name="action" value="backup">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="warehouse_id" value="{{ warehouse.id }}">
                    <button type="submit" class="dropdown-item">
                        <i class="fas fa-warehouse mr-2"></i> {{ warehouse.name }}
                    </button>
                </form>
                {% endfor %}
                <div class="dropdown-divider"></div>
                <div class="dropdown-item-text">
                    <small class="text-muted">
                        <i class="fas fa-exclamation-triangle"></i>
                        نسخ المستودعات تشمل البيانات المرتبطة بالمستودع فقط
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-history"></i> النسخ الاحتياطية السابقة</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الملف</th>
                        <th>نوع النسخة</th>
                        <th>المستودع</th>
                        <th>تاريخ النسخ</th>
                        <th>حجم الملف</th>
                        <th>المستخدم</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for backup in backups %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ backup.filename }}</td>
                        <td>
                            {% if backup.backup_type == 'full' %}
                            <span class="badge badge-primary">كامل</span>
                            {% else %}
                            <span class="badge badge-info">مستودع</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if backup.warehouse %}
                            {{ backup.warehouse.name }}
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>{{ backup.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>{{ (backup.file_size / 1024)|round|int }} كيلوبايت</td>
                        <td>{{ backup.user.username }}</td>
                        <td>
                            <div class="btn-group">
                                <!-- تنزيل النسخة الاحتياطية -->
                                <form method="POST" action="{{ url_for('reports.backup') }}" class="d-inline-block">
                                    <input type="hidden" name="action" value="download">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="backup_id" value="{{ backup.id }}">
                                    <button type="submit" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </form>

                                {% if current_user.role == 'admin' %}
                                <!-- استعادة النسخة الاحتياطية -->
                                <form method="POST" action="{{ url_for('reports.backup') }}" class="d-inline-block">
                                    <input type="hidden" name="action" value="restore">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="backup_id" value="{{ backup.id }}">
                                    <button type="submit" class="btn btn-sm btn-outline-warning"
                                        onclick="return confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                </form>

                                <!-- حذف النسخة الاحتياطية -->
                                <form method="POST" action="{{ url_for('reports.backup') }}" class="d-inline-block">
                                    <input type="hidden" name="action" value="delete_backup">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="backup_id" value="{{ backup.id }}">
                                    <button type="submit" class="btn btn-sm btn-outline-danger"
                                        onclick="return confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="text-center py-3">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> لا توجد نسخ احتياطية حتى الآن
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> معلومات النسخ الاحتياطي</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        إجمالي النسخ الاحتياطية
                        <span class="badge badge-primary badge-pill">{{ backups|length }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        نسخ احتياطية كاملة
                        <span class="badge badge-success badge-pill">{{ backups|selectattr('backup_type', 'equalto',
                            'full')|list|length }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        نسخ احتياطية للمستودعات
                        <span class="badge badge-info badge-pill">{{ backups|selectattr('backup_type', 'equalto',
                            'warehouse')|list|length }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        إجمالي حجم النسخ الاحتياطية
                        <span class="badge badge-warning badge-pill">{{ (backups|sum(attribute='file_size') /
                            (1024*1024))|round(2) }} ميجابايت</span>
                    </li>
                </ul>

                <div class="mt-3">
                    <h6><i class="fas fa-database"></i> محتويات النسخة الاحتياطية الكاملة:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-check text-success"></i> المستودعات والأسلحة<br>
                                <i class="fas fa-check text-success"></i> الأفراد والمعاملات<br>
                                <i class="fas fa-check text-success"></i> الأجهزة والصيانة<br>
                                <i class="fas fa-check text-success"></i> عمليات التدقيق<br>
                                <i class="fas fa-check text-success"></i> سجلات النشاط<br>
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-check text-success"></i> المستخدمين والصلاحيات<br>
                                <i class="fas fa-check text-success"></i> المخزون والملبوسات<br>
                                <i class="fas fa-check text-success"></i> المواقع الأمنية<br>
                                <i class="fas fa-check text-success"></i> كشوفات الاستلامات<br>
                                <i class="fas fa-check text-success"></i> التقارير الأسبوعية<br>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-clock"></i> إدارة جدولة النسخ الاحتياطي</h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-info" onclick="checkSchedulerStatus()">
                        <i class="fas fa-sync"></i> فحص الحالة
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="runScheduledBackups()">
                        <i class="fas fa-play"></i> تشغيل الآن
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- حالة الجدولة -->
                <div id="scheduler-status" class="mb-3">
                    <div class="alert alert-info">
                        <i class="fas fa-spinner fa-spin"></i> جاري فحص حالة الجدولة...
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-3" id="scheduler-stats" style="display: none;">
                    <div class="col-6">
                        <div class="text-center">
                            <h6 class="text-muted">الجدولات النشطة</h6>
                            <h4 class="text-success" id="active-schedules-count">-</h4>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h6 class="text-muted">النسخ المستحقة</h6>
                            <h4 class="text-warning" id="due-schedules-count">-</h4>
                        </div>
                    </div>
                </div>

                <!-- نموذج إنشاء جدولة جديدة -->
                <div class="border-top pt-3">
                    <h6><i class="fas fa-plus"></i> إنشاء جدولة جديدة:</h6>
                    <form method="POST" action="{{ url_for('reports.backup') }}">
                        <input type="hidden" name="action" value="create_schedule">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>نوع الجدولة</label>
                                    <select name="schedule_type" class="form-control form-control-sm" required>
                                        <option value="daily">يومي</option>
                                        <option value="weekly">أسبوعي</option>
                                        <option value="monthly">شهري</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>نوع النسخة</label>
                                    <select name="backup_type" class="form-control form-control-sm" required>
                                        <option value="full">نسخة كاملة</option>
                                        <option value="warehouse">مستودع محدد</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>الساعة</label>
                                    <select name="hour" class="form-control form-control-sm" required>
                                        {% for h in range(24) %}
                                        <option value="{{ h }}" {% if h == 2 %}selected{% endif %}>
                                            {{ h }}:00 {% if h < 12 %}ص{% else %}م{% endif %}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>الدقيقة</label>
                                    <select name="minute" class="form-control form-control-sm">
                                        <option value="0">00</option>
                                        <option value="15">15</option>
                                        <option value="30">30</option>
                                        <option value="45">45</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>المستودع</label>
                                    <select name="warehouse_id" class="form-control form-control-sm">
                                        <option value="0">جميع المستودعات</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> إنشاء جدولة
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الجدولات الموجودة -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-calendar-alt"></i> الجدولات الموجودة</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>نوع الجدولة</th>
                        <th>نوع النسخة</th>
                        <th>الوقت</th>
                        <th>المستودع</th>
                        <th>الحالة</th>
                        <th>التشغيل التالي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for schedule in backup_schedules %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>
                            {% if schedule.schedule_type == 'daily' %}
                            <span class="badge badge-success">يومي</span>
                            {% elif schedule.schedule_type == 'weekly' %}
                            <span class="badge badge-info">أسبوعي</span>
                            {% elif schedule.schedule_type == 'monthly' %}
                            <span class="badge badge-warning">شهري</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if schedule.backup_type == 'full' %}
                            <span class="badge badge-primary">كامل</span>
                            {% else %}
                            <span class="badge badge-secondary">مستودع</span>
                            {% endif %}
                        </td>
                        <td>{{ format_time_12h(schedule.hour, schedule.minute) }}</td>
                        <td>
                            {% if schedule.warehouse %}
                            {{ schedule.warehouse.name }}
                            {% else %}
                            جميع المستودعات
                            {% endif %}
                        </td>
                        <td>
                            {% if schedule.is_active %}
                            <span class="badge badge-success">نشط</span>
                            {% else %}
                            <span class="badge badge-secondary">متوقف</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if schedule.next_run %}
                            {{ format_datetime_12h(schedule.next_run) }}
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <!-- تفعيل/إيقاف الجدولة -->
                                <form method="POST" action="{{ url_for('reports.backup') }}" class="d-inline-block">
                                    <input type="hidden" name="action" value="toggle_schedule">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="schedule_id" value="{{ schedule.id }}">
                                    <button type="submit" class="btn btn-sm {% if schedule.is_active %}btn-outline-warning{% else %}btn-outline-success{% endif %}"
                                        title="{% if schedule.is_active %}إيقاف الجدولة{% else %}تفعيل الجدولة{% endif %}">
                                        {% if schedule.is_active %}
                                        <i class="fas fa-pause"></i>
                                        {% else %}
                                        <i class="fas fa-play"></i>
                                        {% endif %}
                                    </button>
                                </form>

                                <!-- حذف الجدولة -->
                                <form method="POST" action="{{ url_for('reports.backup') }}" class="d-inline-block">
                                    <input type="hidden" name="action" value="delete_schedule">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="schedule_id" value="{{ schedule.id }}">
                                    <button type="submit" class="btn btn-sm btn-outline-danger"
                                        onclick="return confirm('هل أنت متأكد من حذف هذه الجدولة؟ سيتم إيقاف النسخ التلقائية.')"
                                        title="حذف الجدولة">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="text-center py-3">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> لا توجد جدولات للنسخ الاحتياطي حتى الآن
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>



{% endblock %}

{% block scripts %}
<script>
// دوال إدارة جدولة النسخ الاحتياطي
function checkSchedulerStatus() {
    const statusDiv = document.getElementById('scheduler-status');
    const statsDiv = document.getElementById('scheduler-stats');

    // إظهار مؤشر التحميل
    statusDiv.innerHTML = '<div class="text-muted"><i class="fas fa-spinner fa-spin"></i> جاري التحقق...</div>';
    statsDiv.style.display = 'none';

    fetch('{{ url_for("reports.scheduler_status") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إظهار حالة الجدولة
                let statusHtml = '';
                if (data.stats.active_schedules > 0) {
                    statusHtml = `<small class="text-success">
                        <i class="fas fa-check-circle"></i>
                        ${data.stats.active_schedules} جدولة نشطة
                    </small>`;
                } else {
                    statusHtml = '<small class="text-muted">لا توجد جدولات نشطة</small>';
                }

                statusDiv.innerHTML = statusHtml;

                // إظهار الإحصائيات
                document.getElementById('active-schedules-count').textContent = data.stats.active_schedules;
                document.getElementById('due-schedules-count').textContent = data.stats.due_schedules;
                statsDiv.style.display = 'block';

            } else {
                statusDiv.innerHTML = `<small class="text-danger">خطأ: ${data.error}</small>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            statusDiv.innerHTML = '<small class="text-danger">خطأ في الاتصال</small>';
        });
}

function runScheduledBackups() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    // تعطيل الزر وإظهار مؤشر التحميل
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التشغيل...';

    fetch('{{ url_for("reports.run_scheduled_backups_endpoint") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث حالة الجدولة
            setTimeout(checkSchedulerStatus, 1000);
        } else {
            console.error('Error:', data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    })
    .finally(() => {
        // إعادة تفعيل الزر
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// تم حذف دالة showAlert لتقليل الإشعارات المزعجة

// فحص حالة الجدولة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkSchedulerStatus();

    // تحديث الحالة كل دقيقة
    setInterval(checkSchedulerStatus, 60000);
});
</script>
{% endblock %}