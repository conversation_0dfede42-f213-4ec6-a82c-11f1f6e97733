from datetime import datetime, timezone, timedelta

from flask import Blueprint, request, render_template, flash, redirect, url_for
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, TextAreaField, HiddenField, SubmitField
from wtforms.validators import DataRequired, Length, Optional

from db import db
from models import Device, Warehouse, DeviceMaintenanceRecord, Audit, AuditItem, ActivityLog, Personnel, Weapon, MaintenanceRecord
# من الواضح أننا لا نحتاج إلى دالة log_activity من ملف warehouse

# قاموس لترجمة أنواع الأجهزة من الإنجليزية إلى العربية
DEVICE_TYPES_AR = {
    'computer': 'كمبيوتر',
    'laptop': 'لابتوب',
    'printer': 'طابعة',
    'scanner': 'سكانر',
    'monitor': 'شاشة',
    'projector': 'جهاز عرض',
    'server': 'سيرفر',
    'network': 'معدات شبكة',
    'camera': 'كاميرا',
    'storage': 'وحدة تخزين',
    'ups': 'مزود طاقة احتياطي',
    'other': 'أخرى'
}



# Create inventory blueprint
inventory_bp = Blueprint('inventory', __name__, url_prefix='/inventory')

@inventory_bp.route('/')
@login_required
def index():
    # توجيه المستخدم مباشرة إلى صفحة الأجهزة بدلاً من صفحة المخزون
    return redirect(url_for('inventory.devices'))

@inventory_bp.route('/devices')
@login_required
def devices():
    # الحصول على مصطلح البحث من الطلب
    search_query = request.args.get('q', '')

    # Get devices from warehouses the user has access to
    devices_list = []

    # جميع المستخدمين يمكنهم رؤية جميع الأجهزة لأنها أصبحت مستقلة عن المستودعات
    if search_query:
        # البحث في الأجهزة بناءً على الاسم أو الرقم التسلسلي
        devices_list = Device.query.filter(
            (Device.name.ilike(f'%{search_query}%')) |
            (Device.serial_number.ilike(f'%{search_query}%'))
        ).all()
    else:
        # إذا لم يكن هناك بحث، عرض جميع الأجهزة
        devices_list = Device.query.all()

    # الموقع الآن محفوظ في حقل منفصل، لا حاجة لاستخراجه من الملاحظات

    return render_template('inventory/devices.html',
                          devices=devices_list,
                          title='الأجهزة الإدارية',
                          search_query=search_query)

@inventory_bp.route('/devices/warehouse/<int:warehouse_id>')
@inventory_bp.route('/warehouses/<int:warehouse_id>/devices')
@login_required
def warehouse_devices(warehouse_id):
    # تم تعديل هذه الدالة لتوجيه المستخدم إلى صفحة الأجهزة العامة
    # لأن الأجهزة أصبحت مستقلة عن المستودعات
    flash('تم فصل الأجهزة عن المستودعات، يمكنك الوصول إلى جميع الأجهزة من قائمة الأجهزة الرئيسية', 'info')
    return redirect(url_for('inventory.devices'))

@inventory_bp.route('/devices/create', methods=['GET', 'POST'])
@login_required
def create_device():
    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لإضافة أجهزة جديدة', 'danger')
        return redirect(url_for('inventory.devices'))

    form = DeviceForm()
    if form.validate_on_submit():
        # Check if serial number already exists (if provided)
        if form.serial_number.data:
            existing_device = Device.query.filter_by(serial_number=form.serial_number.data).first()
            if existing_device:
                flash('الرقم التسلسلي موجود بالفعل', 'danger')
                return render_template('inventory/create_device.html', form=form, title='إضافة جهاز جديد')



        # Create new device
        device = Device(
            name=form.name.data,
            type=form.type.data,
            model=form.model.data,
            manufacturer=form.manufacturer.data,
            serial_number=form.serial_number.data,
            status=form.status.data,
            location=form.device_location.data,
            notes=form.notes.data,
            warehouse_id=1  # تعيين قيمة افتراضية للمستودع (مطلوب بسبب قيد not-null في قاعدة البيانات)
        )


        db.session.add(device)

        # Log the device creation
        # استخدام القاموس للحصول على الاسم العربي لنوع الجهاز
        device_type_ar = DEVICE_TYPES_AR.get(device.type, device.type)
        log = ActivityLog(
            action="إضافة جهاز",
            description=f"تم إضافة جهاز جديد: {device.name} (النوع: {device_type_ar})",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=1  # تعيين المستودع الأول كمستودع افتراضي لسجل النشاط
        )
        db.session.add(log)

        db.session.commit()
        flash('تم إضافة الجهاز بنجاح!', 'success')
        return redirect(url_for('inventory.devices'))

    return render_template('inventory/create_device.html', form=form, title='إضافة جهاز جديد')

@inventory_bp.route('/devices/<int:device_id>')
@login_required
def device_details(device_id):
    device = Device.query.get_or_404(device_id)

    # الأجهزة أصبحت مستقلة عن المستودعات، لذا يمكن لجميع المستخدمين الوصول إليها



    # Get maintenance records
    maintenance_records = DeviceMaintenanceRecord.query.filter_by(device_id=device.id).order_by(DeviceMaintenanceRecord.start_date.desc()).all()

    # Initialize maintenance form
    maintenance_form = DeviceMaintenanceForm(device_id=device.id)

    return render_template('inventory/device_details.html',
                          device=device,
                          maintenance_records=maintenance_records,
                          maintenance_form=maintenance_form,
                          title=f'تفاصيل الجهاز: {device.name}')

@inventory_bp.route('/devices/<int:device_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_device(device_id):
    device = Device.query.get_or_404(device_id)

    # الأجهزة أصبحت مستقلة عن المستودعات، لذا يمكن لجميع المستخدمين الوصول إليها

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لتعديل هذا الجهاز', 'danger')
        return redirect(url_for('inventory.devices'))

    form = DeviceForm(obj=device)



    # تحميل موقع الجهاز في النموذج
    if device.location:
        form.device_location.data = device.location

        # Extraer las notas sin la ubicación
        if '\n\n' in device.notes:
            notes_without_location = device.notes.split('\n\n', 1)[1]
            form.notes.data = notes_without_location
        else:
            form.notes.data = ""
    if form.validate_on_submit():
        # Check if serial number already exists (if provided and changed)
        if form.serial_number.data and form.serial_number.data != device.serial_number:
            existing_device = Device.query.filter(
                Device.serial_number == form.serial_number.data,
                Device.id != device.id
            ).first()

            if existing_device:
                flash('الرقم التسلسلي موجود بالفعل', 'danger')
                return render_template('inventory/edit_device.html', form=form, device=device, title='تعديل جهاز')



        # Update device data
        device.name = form.name.data
        device.type = form.type.data
        device.model = form.model.data
        device.manufacturer = form.manufacturer.data
        device.serial_number = form.serial_number.data
        device.status = form.status.data
        device.location = form.device_location.data

        # تحديث الملاحظات فقط
        device.notes = form.notes.data

        # تعيين قيمة افتراضية للمستودع (مطلوب بسبب قيد not-null في قاعدة البيانات)
        device.warehouse_id = 1

        # Log the device update
        log = ActivityLog(
            action="تعديل جهاز",
            description=f"تم تعديل بيانات الجهاز: {device.name}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=device.warehouse_id
        )
        db.session.add(log)

        db.session.commit()
        flash('تم تحديث بيانات الجهاز بنجاح!', 'success')
        return redirect(url_for('inventory.device_details', device_id=device.id))

    return render_template('inventory/edit_device.html', form=form, device=device, title='تعديل جهاز')

@inventory_bp.route('/devices/<int:device_id>/delete', methods=['POST'])
@login_required
def delete_device(device_id):
    device = Device.query.get_or_404(device_id)

    # الأجهزة أصبحت مستقلة عن المستودعات، لذا يمكن لجميع المستخدمين الوصول إليها

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لحذف هذا الجهاز', 'danger')
        return redirect(url_for('inventory.devices'))

    deviceName = device.name
    # تعيين المستودع الأول كمستودع افتراضي لسجل النشاط
    deviceWarehouse_id = 1

    # Delete related maintenance records
    for record in device.maintenance_records.all():
        db.session.delete(record)

    db.session.delete(device)

    # Log the device update
    log = ActivityLog(
        action="حذف جهاز",
        description=f"تم حذف الجهاز: {deviceName}",
        ip_address=request.remote_addr,
        user_id=current_user.id,
        warehouse_id=deviceWarehouse_id
    )
    db.session.add(log)

    db.session.commit()
    flash('تم حذف الجهاز بنجاح!', 'success')
    return redirect(url_for('inventory.devices'))

@inventory_bp.route('/devices/<int:device_id>/maintenance', methods=['GET', 'POST'])
@login_required
def add_device_maintenance(device_id):
    device = Device.query.get_or_404(device_id)

    # الأجهزة أصبحت مستقلة عن المستودعات، لذا يمكن لجميع المستخدمين الوصول إليها

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لإضافة سجل صيانة لهذا الجهاز', 'danger')
        return redirect(url_for('inventory.device_details', device_id=device.id))

    form = DeviceMaintenanceForm()
    if form.validate_on_submit():
        # Parse dates
        start_date = None
        end_date = None

        try:
            if form.start_date.data and form.start_date.data.strip():
                start_date = datetime.strptime(form.start_date.data, '%Y-%m-%d')
            if form.end_date.data and form.end_date.data.strip():
                end_date = datetime.strptime(form.end_date.data, '%Y-%m-%d')
        except ValueError:
            flash('صيغة التاريخ غير صحيحة، استخدم الصيغة YYYY-MM-DD', 'danger')
            return redirect(url_for('inventory.device_details', device_id=device.id))

        # Create maintenance record
        maintenance = DeviceMaintenanceRecord(
            device_id=device.id,
            maintenance_type=form.maintenance_type.data,
            description=form.description.data,
            start_date=start_date,
            end_date=end_date,
            status=form.status.data,
            cost=float(form.cost.data) if form.cost.data else None,
            notes=form.notes.data
            # user_id 字段在模型中被注释掉了，所以这里不能设置
            # user_id=current_user.id
        )
        db.session.add(maintenance)

        # Update device status based on maintenance type and status
        if form.status.data == 'ongoing':
            if form.maintenance_type.data == 'replacement':
                device.status = 'خارج الخدمة'  # طلب تعويض
            else:
                device.status = 'تحت الصيانة'
        elif form.status.data == 'completed':
            if form.maintenance_type.data == 'replacement':
                device.status = 'خارج الخدمة'  # لا يزال خارج الخدمة حتى يتم الاستبدال
            else:
                device.status = 'سليم'  # عودة للحالة الطبيعية بعد انتهاء الصيانة

        # Log the maintenance record creation
        log = ActivityLog(
            action="إضافة سجل صيانة",
            description=f"تم إضافة سجل صيانة للجهاز: {device.name}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=1  # تعيين المستودع الأول كمستودع افتراضي لسجل النشاط
        )
        db.session.add(log)

        db.session.commit()
        flash('تم إضافة سجل الصيانة بنجاح!', 'success')

    return redirect(url_for('inventory.device_details', device_id=device.id))

@inventory_bp.route('/devices/<int:device_id>/maintenance/<int:maintenance_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_device_maintenance(device_id, maintenance_id):
    device = Device.query.get_or_404(device_id)
    maintenance = DeviceMaintenanceRecord.query.get_or_404(maintenance_id)

    # Check if maintenance record belongs to this device
    if maintenance.device_id != device.id:
        flash('سجل الصيانة لا ينتمي لهذا الجهاز', 'danger')
        return redirect(url_for('inventory.device_details', device_id=device.id))

    # الأجهزة أصبحت مستقلة عن المستودعات، لذا يمكن لجميع المستخدمين الوصول إليها

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لتعديل سجل صيانة لهذا الجهاز', 'danger')
        return redirect(url_for('inventory.device_details', device_id=device.id))

    # Prepare form with existing data
    form = DeviceMaintenanceForm(obj=maintenance)

    # Convert dates to string format for the form
    if maintenance.start_date:
        form.start_date.data = maintenance.start_date.strftime('%Y-%m-%d')
    if maintenance.end_date:
        form.end_date.data = maintenance.end_date.strftime('%Y-%m-%d')

    if form.validate_on_submit():
        # Parse dates
        start_date = None
        end_date = None

        try:
            if form.start_date.data and form.start_date.data.strip():
                start_date = datetime.strptime(form.start_date.data, '%Y-%m-%d')
            if form.end_date.data and form.end_date.data.strip():
                end_date = datetime.strptime(form.end_date.data, '%Y-%m-%d')
        except ValueError:
            flash('صيغة التاريخ غير صحيحة، استخدم الصيغة YYYY-MM-DD', 'danger')
            return redirect(url_for('inventory.device_details', device_id=device.id))

        # Update maintenance record
        maintenance.maintenance_type = form.maintenance_type.data
        maintenance.description = form.description.data
        maintenance.start_date = start_date
        maintenance.end_date = end_date
        maintenance.status = form.status.data
        maintenance.cost = float(form.cost.data) if form.cost.data else None
        maintenance.notes = form.notes.data

        # Update device status based on maintenance type and status
        if form.status.data == 'ongoing':
            if form.maintenance_type.data == 'replacement':
                device.status = 'خارج الخدمة'  # طلب تعويض
            else:
                device.status = 'تحت الصيانة'
        elif form.status.data == 'completed':
            if form.maintenance_type.data == 'replacement':
                device.status = 'خارج الخدمة'  # لا يزال خارج الخدمة حتى يتم الاستبدال
            else:
                device.status = 'سليم'  # عودة للحالة الطبيعية بعد انتهاء الصيانة
        elif form.status.data == 'cancelled':
            # إذا تم إلغاء الصيانة، عودة للحالة السابقة
            if device.status in ['تحت الصيانة', 'خارج الخدمة']:
                device.status = 'سليم'

        # Log the maintenance record update
        log = ActivityLog(
            action="تعديل سجل صيانة",
            description=f"تم تعديل سجل صيانة للجهاز: {device.name}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=1  # تعيين المستودع الأول كمستودع افتراضي لسجل النشاط
        )
        db.session.add(log)

        db.session.commit()
        flash('تم تعديل سجل الصيانة بنجاح!', 'success')
        return redirect(url_for('inventory.device_details', device_id=device.id))

    return render_template('inventory/edit_maintenance.html',
                          form=form,
                          device=device,
                          maintenance=maintenance,
                          title=f'تعديل سجل صيانة - {device.name}')

@inventory_bp.route('/devices/<int:device_id>/maintenance/<int:maintenance_id>/delete', methods=['GET', 'POST'])
@login_required
def delete_device_maintenance(device_id, maintenance_id):
    device = Device.query.get_or_404(device_id)
    maintenance = DeviceMaintenanceRecord.query.get_or_404(maintenance_id)

    # Check if maintenance record belongs to this device
    if maintenance.device_id != device.id:
        flash('سجل الصيانة لا ينتمي لهذا الجهاز', 'danger')
        return redirect(url_for('inventory.device_details', device_id=device.id))

    # الأجهزة أصبحت مستقلة عن المستودعات، لذا يمكن لجميع المستخدمين الوصول إليها

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لحذف سجل صيانة لهذا الجهاز', 'danger')
        return redirect(url_for('inventory.device_details', device_id=device.id))

    # Store maintenance info for logging
    maintenance_type = maintenance.maintenance_type
    maintenance_date = maintenance.start_date.strftime('%Y-%m-%d') if maintenance.start_date else ''

    # Delete the maintenance record
    db.session.delete(maintenance)

    # Log the maintenance record deletion
    log = ActivityLog(
        action="حذف سجل صيانة",
        description=f"تم حذف سجل صيانة للجهاز: {device.name} (النوع: {maintenance_type}, التاريخ: {maintenance_date})",
        ip_address=request.remote_addr,
        user_id=current_user.id,
        warehouse_id=1  # تعيين المستودع الأول كمستودع افتراضي لسجل النشاط
    )
    db.session.add(log)

    db.session.commit()
    flash('تم حذف سجل الصيانة بنجاح!', 'success')
    return redirect(url_for('inventory.device_details', device_id=device.id))

@inventory_bp.route('/audits')
@login_required
def audits():
    try:
        # Get audits from warehouses the user has access to
        if current_user.is_admin_role:
            # Si el usuario es administrador, mostrar todos los audits
            audits_list = Audit.query.order_by(Audit.audit_date.desc()).all()
        else:
            warehouse_ids = [w.id for w in current_user.warehouses]
            audits_list = Audit.query.filter(Audit.warehouse_id.in_(warehouse_ids)).order_by(Audit.audit_date.desc()).all()

        # حساب التنبيهات للجرد القادم
        audit_notifications = []
        current_date = datetime.now(timezone.utc)

        for audit in audits_list:
            if audit.status == 'completed' and audit.next_audit_date:
                # حساب الأيام المتبقية للجرد القادم
                days_remaining = (audit.next_audit_date - current_date).days

                # إضافة تنبيه إذا كان الجرد القادم خلال 30 يوم أو متأخر
                if days_remaining <= 30:
                    notification = {
                        'audit': audit,
                        'days_remaining': days_remaining,
                        'next_audit_date': audit.next_audit_date,
                        'warehouse_name': audit.warehouse.name,
                        'is_overdue': days_remaining < 0,
                        'is_today': days_remaining == 0,
                        'is_urgent': days_remaining <= 7 and days_remaining >= 0
                    }
                    audit_notifications.append(notification)

        # ترتيب التنبيهات حسب الأولوية (المتأخر أولاً، ثم الأقرب)
        audit_notifications.sort(key=lambda x: (x['days_remaining'] if x['days_remaining'] >= 0 else -1000 + x['days_remaining']))

        return render_template('inventory/audits.html',
                             audits=audits_list,
                             audit_notifications=audit_notifications,
                             current_date=current_date,
                             title='الجرد الدوري')
    except Exception as e:
        # Log the error
        print(f"Error in audits route: {str(e)}")
        db.session.rollback()  # Rollback any failed transaction
        return render_template('inventory/audits.html',
                             audits=[],
                             audit_notifications=[],
                             current_date=datetime.now(timezone.utc),
                             title='الجرد الدوري')

@inventory_bp.route('/audits/<int:audit_id>')
@login_required
def audit_details(audit_id):
    audit = Audit.query.get_or_404(audit_id)

    # Check if user has access to this warehouse
    if not current_user.is_admin_role and audit.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا الجرد', 'danger')
        return redirect(url_for('inventory.audits'))

    # Get weapons from this warehouse
    weapons = Weapon.query.filter_by(warehouse_id=audit.warehouse_id).all()

    # Get already audited weapons
    audit_items = audit.audit_items.all()
    audited_weapons = {item.weapon_id: item for item in audit_items}

    # Get counts
    found_count = sum(1 for item in audit_items if item.status == 'found')
    missing_count = sum(1 for item in audit_items if item.status == 'missing')
    damaged_count = sum(1 for item in audit_items if item.status == 'damaged')

    # Get remaining weapons (not audited yet)
    remaining_weapons = [w for w in weapons if w.id not in audited_weapons]
    total_weapons = len(weapons)

    # الحصول على التاريخ الحالي لحساب الأيام المتبقية للجرد القادم
    now = datetime.now(timezone.utc)

    # التأكد من أن تاريخ الجرد يحتوي على معلومات المنطقة الزمنية
    if audit.audit_date.tzinfo is None:
        audit_date_aware = audit.audit_date.replace(tzinfo=timezone.utc)
    else:
        audit_date_aware = audit.audit_date

    # حساب تاريخ الجرد القادم
    next_audit_date = audit_date_aware + timedelta(days=90)

    # حساب الأيام المتبقية
    days_remaining = (next_audit_date - now).days

    form = AuditForm()  # Create form instance for CSRF token
    return render_template('inventory/audit_details.html',
                         audit=audit,
                         weapons=weapons,
                         audit_items=audit_items,
                         remaining_weapons=remaining_weapons,
                         total_weapons=total_weapons,
                         audited_weapons=audited_weapons,
                         found_count=found_count,
                         missing_count=missing_count,
                         damaged_count=damaged_count,
                         form=form,
                         now=now,
                         next_audit_date=next_audit_date,
                         days_remaining=days_remaining,
                         timedelta=timedelta,
                         title=f'تفاصيل الجرد: {audit.audit_date.strftime("%Y-%m-%d")}')

@inventory_bp.route('/audits/<int:audit_id>/complete', methods=['POST'])
@login_required
def complete_audit(audit_id):
    audit = Audit.query.get_or_404(audit_id)

    # Check if user has access to this warehouse
    if not current_user.is_admin_role and audit.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('inventory.audits'))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لإكمال هذا الجرد', 'danger')
        return redirect(url_for('inventory.audits'))

    audit.status = 'completed'
    # تعيين تاريخ اكتمال الجرد
    audit.completed_at = datetime.now(timezone.utc)

    # تحديد تاريخ الجرد القادم (بعد 3 أشهر)
    # التأكد من أن تاريخ الجرد يحتوي على معلومات المنطقة الزمنية
    if audit.audit_date.tzinfo is None:
        audit_date_aware = audit.audit_date.replace(tzinfo=timezone.utc)
    else:
        audit_date_aware = audit.audit_date

    next_audit_date = audit_date_aware + timedelta(days=90)
    audit.next_audit_date = next_audit_date

    # Log the audit completion
    log = ActivityLog(
        action="اكتمال جرد",
        description=f"تم إكمال الجرد في {audit.warehouse.name}، الجرد القادم في {next_audit_date.strftime('%Y-%m-%d')}",
        ip_address=request.remote_addr,
        user_id=current_user.id,
        warehouse_id=audit.warehouse_id
    )
    db.session.add(log)
    db.session.commit()

    flash(f'تم إكمال الجرد بنجاح! الجرد القادم في {next_audit_date.strftime("%Y-%m-%d")}', 'success')
    return redirect(url_for('inventory.audit_details', audit_id=audit.id))

@inventory_bp.route('/audits/<int:audit_id>/cancel', methods=['POST'])
@login_required
def cancel_audit(audit_id):
    audit = Audit.query.get_or_404(audit_id)

    # Check if user has access to this warehouse
    if not current_user.is_admin_role and audit.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('inventory.audits'))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لإلغاء هذا الجرد', 'danger')
        return redirect(url_for('inventory.audits'))

    audit.status = 'cancelled'

    # Log the audit cancellation
    log = ActivityLog(
        action="إلغاء جرد",
        description=f"تم إلغاء الجرد في {audit.warehouse.name}",
        ip_address=request.remote_addr,
        user_id=current_user.id,
        warehouse_id=audit.warehouse_id
    )
    db.session.add(log)
    db.session.commit()

    flash('تم إلغاء الجرد!', 'warning')
    return redirect(url_for('inventory.audits'))

@inventory_bp.route('/audits/<int:audit_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_audit(audit_id):
    audit = Audit.query.get_or_404(audit_id)

    # Check if user has access to this warehouse
    if not current_user.is_admin_role and audit.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا الجرد', 'danger')
        return redirect(url_for('inventory.audits'))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لتعديل هذا الجرد', 'danger')
        return redirect(url_for('inventory.audits'))

    # Check if audit is still in progress
    if audit.status != 'in-progress':
        flash('لا يمكن تعديل جرد مكتمل أو ملغي', 'danger')
        return redirect(url_for('inventory.audit_details', audit_id=audit.id))

    form = AuditForm(obj=audit)

    # Set up warehouse choices
    if current_user.is_admin_role:
        warehouses = Warehouse.query.all()
        form.warehouse_id.choices = [(w.id, w.name) for w in warehouses]
    else:
        form.warehouse_id.choices = [(w.id, w.name) for w in current_user.warehouses]

    if form.validate_on_submit():
        try:
            # التحقق من وجود المستودع المحدد
            warehouse = Warehouse.query.get(form.warehouse_id.data)
            if not warehouse:
                flash('المستودع المحدد غير موجود.', 'danger')
                return render_template('inventory/edit_audit.html', form=form, audit=audit, title='تعديل الجرد')

            # التحقق من صلاحية المستخدم للوصول إلى المستودع المحدد
            if not current_user.is_admin_role and warehouse not in current_user.warehouses:
                flash('ليس لديك صلاحية للوصول إلى المستودع المحدد.', 'danger')
                return render_template('inventory/edit_audit.html', form=form, audit=audit, title='تعديل الجرد')

            # Update audit details
            audit.description = form.description.data
            audit.notes = form.notes.data
            audit.warehouse_id = warehouse.id

            # Log the audit update
            log = ActivityLog(
                action="تعديل جرد",
                description=f"تم تعديل الجرد في {warehouse.name}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=warehouse.id
            )
            db.session.add(log)
            db.session.commit()

            flash('تم تعديل الجرد بنجاح!', 'success')
            return redirect(url_for('inventory.audit_details', audit_id=audit.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تعديل الجرد: {str(e)}', 'danger')

    return render_template('inventory/edit_audit.html', form=form, audit=audit, title='تعديل الجرد')

@login_required
def delete_audit(audit_id):
    audit = Audit.query.get_or_404(audit_id)

    # Check if user has access to this warehouse
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية لحذف هذا الجرد', 'danger')
        return redirect(url_for('inventory.audits'))

    # Delete all audit items first
    AuditItem.query.filter_by(audit_id=audit.id).delete()

    # Log the audit deletion
    log = ActivityLog(
        action="حذف جرد",
        description=f"تم حذف الجرد في {audit.warehouse.name}",
        ip_address=request.remote_addr,
        user_id=current_user.id,
        warehouse_id=audit.warehouse_id
    )
    db.session.add(log)

    # Delete the audit
    db.session.delete(audit)
    db.session.commit()

    flash('تم حذف الجرد بنجاح!', 'success')
    return redirect(url_for('inventory.audits'))

@inventory_bp.route('/audits/create', methods=['GET', 'POST'])
@login_required
def create_audit():
    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لإنشاء جرد جديد', 'danger')
        return redirect(url_for('inventory.audits'))

    # التحقق من وجود مستودعات مرتبطة بالمستخدم
    if not current_user.is_admin_role and not current_user.warehouses:
        flash('ليس لديك أي مستودعات مرتبطة بحسابك. يرجى التواصل مع مدير النظام.', 'danger')
        return redirect(url_for('inventory.audits'))

    form = AuditForm()

    # التحقق من وجود خيارات في قائمة المستودعات
    if not form.warehouse_id.choices:
        if current_user.is_admin_role:
            warehouses = Warehouse.query.all()
            if not warehouses:
                flash('لا توجد مستودعات في النظام. يرجى إنشاء مستودع أولاً.', 'danger')
                return redirect(url_for('inventory.audits'))
            form.warehouse_id.choices = [(w.id, w.name) for w in warehouses]
        else:
            if not current_user.warehouses:
                flash('ليس لديك أي مستودعات مرتبطة بحسابك. يرجى التواصل مع مدير النظام.', 'danger')
                return redirect(url_for('inventory.audits'))
            form.warehouse_id.choices = [(w.id, w.name) for w in current_user.warehouses]

    # إذا تم تمرير معرف المستودع من التنبيه، قم بتعيينه كقيمة افتراضية
    warehouse_id_param = request.args.get('warehouse_id', type=int)
    if warehouse_id_param and request.method == 'GET':
        # التحقق من أن المستخدم لديه صلاحية للوصول إلى هذا المستودع
        if current_user.is_admin_role:
            warehouse = Warehouse.query.get(warehouse_id_param)
            if warehouse:
                form.warehouse_id.data = warehouse_id_param
        else:
            warehouse_ids = [w.id for w in current_user.warehouses]
            if warehouse_id_param in warehouse_ids:
                form.warehouse_id.data = warehouse_id_param

    if form.validate_on_submit():
        try:
            # التحقق من وجود المستودع المحدد
            warehouse = Warehouse.query.get(form.warehouse_id.data)
            if not warehouse:
                flash('المستودع المحدد غير موجود.', 'danger')
                return render_template('inventory/create_audit.html', form=form, title='إنشاء جرد جديد')

            # التحقق من صلاحية المستخدم للوصول إلى المستودع المحدد
            if not current_user.is_admin_role and warehouse not in current_user.warehouses:
                flash('ليس لديك صلاحية للوصول إلى المستودع المحدد.', 'danger')
                return render_template('inventory/create_audit.html', form=form, title='إنشاء جرد جديد')

            # Create new audit
            audit = Audit(
                audit_date=datetime.now(timezone.utc),
                status='in-progress',
                description=form.description.data,
                notes=form.notes.data,
                warehouse_id=warehouse.id,
                user_id=current_user.id,
                next_audit_date=None,
                completed_at=None
            )

            db.session.add(audit)
            db.session.flush()  # Get audit ID

            # Log the audit creation
            log = ActivityLog(
                action="إنشاء جرد",
                description=f"تم إنشاء جرد جديد في {warehouse.name}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=warehouse.id
            )
            db.session.add(log)
            db.session.commit()
            flash('تم إنشاء الجرد بنجاح!', 'success')
            return redirect(url_for('inventory.audit_details', audit_id=audit.id))
        except Exception as e:
            db.session.rollback()
            import traceback
            error_details = traceback.format_exc()
            print(f"Error creating audit: {str(e)}\n{error_details}")
            flash(f'حدث خطأ أثناء إنشاء الجرد: {str(e)}', 'danger')
            return render_template('inventory/create_audit.html', form=form, title='إنشاء جرد جديد')

    return render_template('inventory/create_audit.html', form=form, title='إنشاء جرد جديد')


@inventory_bp.route('/audits/<int:audit_id>/weapons/<int:weapon_id>', methods=['GET', 'POST'])
@login_required
def audit_weapon(audit_id, weapon_id):
    audit = Audit.query.get_or_404(audit_id)
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to this warehouse
    if not current_user.is_admin_role and audit.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('inventory.audits'))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لجرد هذا السلاح', 'danger')
        return redirect(url_for('inventory.audits'))

    # Get or create audit item
    audit_item = AuditItem.query.filter_by(audit_id=audit.id, weapon_id=weapon.id).first()
    form = AuditItemForm(obj=audit_item)

    # Get personnel associated with this weapon
    associated_personnel = None
    try:
        # استخدام first() بدلاً من الوصول المباشر إلى العناصر
        associated_personnel = weapon.personnel.first()
    except Exception as e:
        print(f"Error getting associated personnel: {str(e)}")
        associated_personnel = None

    if form.validate_on_submit():
        if not audit_item:
            audit_item = AuditItem(audit_id=audit.id, weapon_id=weapon.id)
            db.session.add(audit_item)

        # حفظ الحالة السابقة للمقارنة
        previous_status = audit_item.status if audit_item else None

        audit_item.status = form.status.data
        audit_item.condition = form.condition.data
        audit_item.notes = form.notes.data

        # إذا كانت الحالة هي "صيانة"، قم بتخزين سبب الصيانة والملاحظات الفنية
        if form.status.data == 'damaged':
            audit_item.maintenance_reason = form.maintenance_reason.data
            audit_item.technical_notes = form.technical_notes.data

            # إذا تم اختيار إنشاء سجل صيانة
            if form.create_maintenance_record.data == 'yes':
                # إنشاء سجل صيانة جديد
                maintenance_record = MaintenanceRecord(
                    weapon_id=weapon.id,
                    maintenance_type=form.maintenance_type.data,
                    description=form.maintenance_reason.data or "صيانة من خلال الجرد",
                    start_date=datetime.now(timezone.utc),
                    status='ongoing',
                    notes=form.technical_notes.data,
                    user_id=current_user.id
                )
                db.session.add(maintenance_record)
                db.session.flush()  # للحصول على معرف سجل الصيانة

                # ربط سجل الصيانة بعنصر الجرد
                audit_item.maintenance_record_id = maintenance_record.id

                # تحديث حالة السلاح إلى "صيانة"
                weapon.status = 'صيانة'

                # إضافة سجل نشاط
                log = ActivityLog(
                    action="إضافة سجل صيانة",
                    description=f"تم إضافة سجل صيانة للسلاح: {weapon.name} من خلال الجرد",
                    ip_address=request.remote_addr,
                    user_id=current_user.id,
                    warehouse_id=audit.warehouse_id
                )
                db.session.add(log)
        # إذا تم تغيير الحالة من "صيانة" إلى "سليم"، قم بحذف سجل الصيانة المرتبط
        elif previous_status == 'damaged' and form.status.data == 'found' and audit_item.maintenance_record_id:
            # البحث عن سجل الصيانة المرتبط
            maintenance_record = MaintenanceRecord.query.get(audit_item.maintenance_record_id)

            if maintenance_record and maintenance_record.status == 'ongoing':
                # حذف سجل الصيانة
                db.session.delete(maintenance_record)

                # إزالة الارتباط بين عنصر الجرد وسجل الصيانة
                audit_item.maintenance_record_id = None

                # تحديث حالة السلاح إلى "نشط"
                weapon.status = 'نشط'

                # إضافة سجل نشاط
                log = ActivityLog(
                    action="حذف سجل صيانة",
                    description=f"تم حذف سجل صيانة للسلاح: {weapon.name} وتغيير حالته إلى نشط بعد تسجيل حالته كسليم في الجرد",
                    ip_address=request.remote_addr,
                    user_id=current_user.id,
                    warehouse_id=audit.warehouse_id
                )
                db.session.add(log)

        db.session.commit()
        flash('تم حفظ حالة السلاح بنجاح!', 'success')
        return redirect(url_for('inventory.audit_details', audit_id=audit.id))

    from utils import translate_maintenance_type, translate_maintenance_status, translate_audit_status

    return render_template('inventory/audit_weapon.html',
                         audit=audit,
                         weapon=weapon,
                         form=form,
                         audit_item=audit_item,
                         associated_personnel=associated_personnel,
                         MaintenanceRecord=MaintenanceRecord,
                         translate_maintenance_type=translate_maintenance_type,
                         translate_maintenance_status=translate_maintenance_status,
                         translate_audit_status=translate_audit_status,
                         title=f'جرد السلاح: {weapon.name}')


class DeviceForm(FlaskForm):
    name = StringField('اسم الجهاز', validators=[DataRequired(), Length(min=1, max=100)])
    type = SelectField('نوع الجهاز', choices=[
        ('computer', 'كمبيوتر'),
        ('laptop', 'لابتوب'),
        ('printer', 'طابعة'),
        ('scanner', 'سكانر'),
        ('monitor', 'شاشة'),
        ('projector', 'جهاز عرض'),
        ('server', 'سيرفر'),
        ('network', 'معدات شبكة'),
        ('camera', 'كاميرا'),
        ('storage', 'وحدة تخزين'),
        ('ups', 'مزود طاقة احتياطي'),
        ('other', 'أخرى')
    ])
    model = StringField('الموديل', validators=[Length(max=100)])
    manufacturer = StringField('الشركة المصنعة', validators=[Length(max=100)])
    serial_number = StringField('الرقم التسلسلي', validators=[Length(max=100)])
    status = SelectField('الحالة', choices=[
        ('سليم', 'سليم'),
        ('عطل بسيط', 'عطل بسيط'),
        ('عطل جسيم', 'عطل جسيم'),
        ('تحت الصيانة', 'تحت الصيانة'),
        ('خارج الخدمة', 'خارج الخدمة'),
        ('مفقود', 'مفقود')
    ])
    notes = TextAreaField('ملاحظات')
    device_location = StringField('الموقع', validators=[Length(max=100)])
    submit = SubmitField('حفظ')

    # تم إزالة دالة __init__ لأن الأجهزة أصبحت مستقلة عن المستودعات

class DeviceMaintenanceForm(FlaskForm):
    device_id = HiddenField('معرف الجهاز')
    maintenance_type = SelectField('نوع الصيانة', choices=[
        ('routine', 'صيانة دورية'),
        ('repair', 'إصلاح'),
        ('upgrade', 'تحديث'),
        ('inspection', 'فحص'),
        ('replacement', 'طلب تعويض'),
        ('other', 'أخرى')
    ])
    description = TextAreaField('وصف الصيانة', validators=[DataRequired()])
    start_date = StringField('تاريخ البدء', validators=[DataRequired()])
    end_date = StringField('تاريخ الانتهاء (متوقع)', validators=[Optional()])
    status = SelectField('الحالة', choices=[
        ('ongoing', 'جارية'),
        ('completed', 'مكتملة'),
        ('cancelled', 'ملغاة')
    ])
    cost = StringField('التكلفة')
    notes = TextAreaField('ملاحظات')
    submit = SubmitField('حفظ')

class AuditForm(FlaskForm):
    warehouse_id = SelectField('المستودع', coerce=int, validators=[DataRequired(message='يرجى اختيار مستودع')])
    description = TextAreaField('وصف الجرد', validators=[DataRequired(message='يرجى إدخال وصف للجرد')])
    notes = TextAreaField('ملاحظات')
    submit = SubmitField('بدء الجرد')

    def __init__(self, *args, **kwargs):
        super(AuditForm, self).__init__(*args, **kwargs)
        try:
            if current_user.is_admin_role:
                warehouses = Warehouse.query.all()
                if warehouses:
                    self.warehouse_id.choices = [(w.id, w.name) for w in warehouses]
                else:
                    self.warehouse_id.choices = []
            else:
                if hasattr(current_user, 'warehouses') and current_user.warehouses:
                    self.warehouse_id.choices = [(w.id, w.name) for w in current_user.warehouses]
                else:
                    self.warehouse_id.choices = []
        except Exception as e:
            print(f"Error initializing AuditForm: {str(e)}")
            self.warehouse_id.choices = []

class AuditItemForm(FlaskForm):
    audit_id = HiddenField('معرف الجرد')
    weapon_id = HiddenField('معرف السلاح')
    status = SelectField('الحالة', choices=[
        ('found', 'سليم'),
        ('missing', 'مفقود'),
        ('damaged', 'صيانة')
    ])
    condition = StringField('الحالة', validators=[Length(max=100)])
    notes = TextAreaField('ملاحظات')
    maintenance_reason = TextAreaField('سبب الصيانة', validators=[Optional()])
    technical_notes = TextAreaField('ملاحظات فنية', validators=[Optional()])
    create_maintenance_record = SelectField('إنشاء سجل صيانة', choices=[
        ('no', 'لا'),
        ('yes', 'نعم')
    ], default='no')
    maintenance_type = SelectField('نوع الصيانة', choices=[
        ('routine', 'صيانة دورية'),
        ('repair', 'إصلاح'),
        ('inspection', 'فحص'),
        ('replacement', 'طلب تعويض'),
        ('other', 'أخرى')
    ], default='routine')
    submit = SubmitField('حفظ')