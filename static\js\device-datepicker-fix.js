/**
 * إصلاح خاص لمنتقي التاريخ في صفحات الأجهزة
 */
document.addEventListener('DOMContentLoaded', function() {
    // التحقق مما إذا كنا في صفحة إضافة أو تعديل جهاز
    const isDevicePage = window.location.href.includes('create_device') || 
                        window.location.href.includes('edit_device') ||
                        window.location.href.includes('devices/create') ||
                        window.location.href.includes('devices/edit');
    
    if (!isDevicePage) return;
    
    // إعادة تهيئة منتقي التاريخ بإعدادات خاصة لصفحات الأجهزة
    $('.datepicker').datepicker('remove');
    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        language: 'ar',
        rtl: true,
        clearBtn: true,
        todayBtn: 'linked',
        todayHighlight: true,
        zIndexOffset: 99999,
        container: 'body',
        orientation: 'auto',
        templates: {
            leftArrow: '<i class="fas fa-chevron-right"></i>',
            rightArrow: '<i class="fas fa-chevron-left"></i>'
        }
    });
    
    // إصلاح خاص لموضع منتقي التاريخ في صفحات الأجهزة
    $('.datepicker').on('show', function(e) {
        const input = this;
        
        // تأخير قصير للتأكد من أن منتقي التاريخ قد تم إنشاؤه
        setTimeout(function() {
            const datepickerDropdown = document.querySelector('.datepicker-dropdown');
            if (!datepickerDropdown) return;
            
            // إضافة فئة خاصة لتحديد موضع منتقي التاريخ
            datepickerDropdown.classList.add('datepicker-position-fixed');
            
            // تعيين z-index عالي جدًا
            datepickerDropdown.style.zIndex = '99999';
            
            // وضع منتقي التاريخ في وسط الشاشة
            datepickerDropdown.style.position = 'fixed';
            datepickerDropdown.style.top = '50%';
            datepickerDropdown.style.left = '50%';
            datepickerDropdown.style.transform = 'translate(-50%, -50%)';
            
            // التأكد من أن منتقي التاريخ مرئي
            datepickerDropdown.style.display = 'block';
        }, 50);
    });
    
    // إضافة معالج نقر خاص لأيقونة التقويم في صفحات الأجهزة
    document.querySelectorAll('.input-group-append').forEach(function(appendSpan) {
        // إزالة معالجات النقر السابقة
        const newAppendSpan = appendSpan.cloneNode(true);
        appendSpan.parentNode.replaceChild(newAppendSpan, appendSpan);
        
        // إضافة معالج نقر جديد
        newAppendSpan.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            
            // إغلاق أي منتقي تاريخ مفتوح
            $('.datepicker').datepicker('hide');
            
            // الحصول على حقل الإدخال المرتبط
            const input = this.parentElement.querySelector('.datepicker');
            if (!input) return;
            
            // فتح منتقي التاريخ لهذا الحقل
            setTimeout(function() {
                $(input).datepicker('show');
            }, 50);
        });
    });
});
