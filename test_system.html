<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الحفظ المحسن</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .status-indicator {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🧪 اختبار نظام الحفظ المحسن</h1>
        
        <div class="test-section">
            <h3>📝 اختبار حفظ البيانات</h3>
            <div class="mb-3">
                <label for="testInput" class="form-label">أدخل نص للاختبار:</label>
                <input type="text" class="form-control" id="testInput" placeholder="اكتب شيئاً هنا...">
            </div>
            <div class="mb-3">
                <label for="testSelect" class="form-label">اختر موقع:</label>
                <select class="form-select" id="testSelect">
                    <option value="">اختر موقع</option>
                    <option value="موقع1">الموقع الأول</option>
                    <option value="موقع2">الموقع الثاني</option>
                    <option value="موقع3">الموقع الثالث</option>
                </select>
            </div>
            <button class="btn btn-primary" onclick="testSave()">💾 اختبار الحفظ</button>
            <button class="btn btn-success" onclick="testLoad()">📥 اختبار التحميل</button>
            <button class="btn btn-warning" onclick="clearData()">🗑️ مسح البيانات</button>
        </div>
        
        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <div id="statusContainer">
                <div class="status-indicator status-warning">⏳ جاري التحقق من النظام...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 سجل الأحداث</h3>
            <div id="logContainer" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                <div class="text-muted">سيتم عرض سجل الأحداث هنا...</div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات الاختبار
        let testData = {
            input: '',
            select: '',
            timestamp: null
        };

        // دالة إضافة سجل
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            switch(type) {
                case 'success':
                    logEntry.style.backgroundColor = '#d4edda';
                    logEntry.style.color = '#155724';
                    break;
                case 'error':
                    logEntry.style.backgroundColor = '#f8d7da';
                    logEntry.style.color = '#721c24';
                    break;
                case 'warning':
                    logEntry.style.backgroundColor = '#fff3cd';
                    logEntry.style.color = '#856404';
                    break;
                default:
                    logEntry.style.backgroundColor = '#d1ecf1';
                    logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>${time}</strong> - ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // دالة اختبار الحفظ
        function testSave() {
            try {
                const input = document.getElementById('testInput').value;
                const select = document.getElementById('testSelect').value;
                
                testData = {
                    input: input,
                    select: select,
                    timestamp: new Date().toISOString()
                };
                
                // حفظ في localStorage
                localStorage.setItem('testData', JSON.stringify(testData));
                
                addLog(`✅ تم حفظ البيانات: "${input}" - "${select}"`, 'success');
                updateStatus('تم الحفظ بنجاح', 'success');
                
            } catch (error) {
                addLog(`❌ خطأ في الحفظ: ${error.message}`, 'error');
                updateStatus('خطأ في الحفظ', 'error');
            }
        }

        // دالة اختبار التحميل
        function testLoad() {
            try {
                const savedData = localStorage.getItem('testData');
                
                if (savedData) {
                    const data = JSON.parse(savedData);
                    
                    document.getElementById('testInput').value = data.input || '';
                    document.getElementById('testSelect').value = data.select || '';
                    
                    addLog(`📥 تم تحميل البيانات: "${data.input}" - "${data.select}"`, 'success');
                    addLog(`⏰ وقت الحفظ: ${new Date(data.timestamp).toLocaleString('ar-SA')}`, 'info');
                    updateStatus('تم التحميل بنجاح', 'success');
                } else {
                    addLog('⚠️ لا توجد بيانات محفوظة', 'warning');
                    updateStatus('لا توجد بيانات محفوظة', 'warning');
                }
                
            } catch (error) {
                addLog(`❌ خطأ في التحميل: ${error.message}`, 'error');
                updateStatus('خطأ في التحميل', 'error');
            }
        }

        // دالة مسح البيانات
        function clearData() {
            try {
                localStorage.removeItem('testData');
                document.getElementById('testInput').value = '';
                document.getElementById('testSelect').value = '';
                
                addLog('🗑️ تم مسح جميع البيانات', 'warning');
                updateStatus('تم مسح البيانات', 'warning');
                
            } catch (error) {
                addLog(`❌ خطأ في مسح البيانات: ${error.message}`, 'error');
            }
        }

        // دالة تحديث الحالة
        function updateStatus(message, type) {
            const statusContainer = document.getElementById('statusContainer');
            const statusClass = `status-${type}`;
            
            statusContainer.innerHTML = `<div class="status-indicator ${statusClass}">${message}</div>`;
        }

        // دالة فحص النظام
        function checkSystem() {
            addLog('🔍 بدء فحص النظام...', 'info');
            
            // فحص localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                addLog('✅ localStorage يعمل بشكل صحيح', 'success');
            } catch (error) {
                addLog('❌ localStorage لا يعمل', 'error');
            }
            
            // فحص الاتصال
            if (navigator.onLine) {
                addLog('🌐 الاتصال بالإنترنت متاح', 'success');
            } else {
                addLog('📴 لا يوجد اتصال بالإنترنت', 'warning');
            }
            
            updateStatus('النظام جاهز للاختبار', 'success');
        }

        // إضافة event listeners
        document.getElementById('testInput').addEventListener('input', function() {
            addLog(`📝 تغيير في النص: "${this.value}"`, 'info');
        });

        document.getElementById('testSelect').addEventListener('change', function() {
            addLog(`🔄 تغيير في الاختيار: "${this.value}"`, 'info');
        });

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 تم تحميل الصفحة', 'success');
            checkSystem();
            
            // محاولة تحميل البيانات المحفوظة
            setTimeout(() => {
                testLoad();
            }, 1000);
        });

        // فحص تغيير حالة الاتصال
        window.addEventListener('online', () => {
            addLog('🌐 تم استعادة الاتصال', 'success');
        });

        window.addEventListener('offline', () => {
            addLog('📴 فقدان الاتصال', 'warning');
        });
    </script>
</body>
</html>
