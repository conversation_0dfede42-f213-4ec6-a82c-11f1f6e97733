{% extends "base.html" %}

{% block title %}عرض الموقع - {{ location.name }}{% endblock %}

{% block styles %}
<style>
    .location-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }

    .location-title {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .location-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
    }

    .info-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .info-card h3 {
        color: var(--primary-color);
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 15px;
        background: var(--section-bg);
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }

    .info-icon {
        width: 40px;
        height: 40px;
        background: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
    }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-size: 0.9rem;
        color: var(--text-secondary);
        margin-bottom: 5px;
    }

    .info-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        text-align: center;
        display: inline-block;
    }

    .status-active { background: #d4edda; color: #155724; }
    .status-inactive { background: #f8d7da; color: #721c24; }
    .status-withdrawn { background: #fff3cd; color: #856404; }
    .status-maintenance { background: #cce7ff; color: #004085; }

    .equipment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    /* Personnel Compact Cards */
    .personnel-compact-card {
        transition: all 0.2s ease;
        border-radius: 8px;
        min-height: 200px;
    }

    .personnel-compact-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .personnel-compact-card .card-body {
        font-size: 0.9rem;
    }

    .personnel-compact-card .card-title {
        font-size: 1rem;
        font-weight: 600;
    }

    .personnel-compact-card .btn-xs {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }

    .badge-sm {
        font-size: 0.75rem;
        padding: 0.3rem 0.5rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .personnel-compact-card {
            min-height: auto;
        }
    }

    /* Personnel Search Cards */
    .personnel-card {
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .personnel-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .personnel-card.border-warning {
        border-left: 4px solid #ffc107 !important;
    }

    .search-result-enter {
        animation: slideInFromRight 0.3s ease-out;
    }

    @keyframes slideInFromRight {
        from {
            opacity: 0;
            transform: translateX(20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .equipment-item {
        background: var(--section-bg);
        border: 1px solid var(--border-color);
        border-radius: 10px;
        padding: 20px;
        transition: all 0.3s ease;
    }

    .equipment-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .equipment-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
    }

    .equipment-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--primary-color);
        margin: 0;
    }

    .equipment-type {
        background: var(--primary-color);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin-left: 10px;
    }

    .equipment-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-top: 15px;
    }

    .equipment-detail {
        font-size: 0.9rem;
    }

    .equipment-detail strong {
        color: var(--text-primary);
    }

    .condition-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .condition-good { background: #d4edda; color: #155724; }
    .condition-average { background: #fff3cd; color: #856404; }
    .condition-needs-repair { background: #f8d7da; color: #721c24; }
    .condition-broken { background: #f5c6cb; color: #721c24; }

    .action-buttons {
        display: flex;
        gap: 15px;
        margin-top: 30px;
    }

    .btn-action {
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-edit {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
    }

    .btn-delete {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border: none;
    }

    .btn-back {
        background: linear-gradient(135deg, #6c757d, #5a6268);
        color: white;
        border: none;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        color: white;
        text-decoration: none;
    }

    .empty-equipment {
        text-align: center;
        padding: 40px;
        color: var(--text-secondary);
    }

    .empty-equipment i {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .location-header {
            padding: 20px;
        }
        
        .location-title {
            font-size: 1.5rem;
        }
        
        .info-grid {
            grid-template-columns: 1fr;
        }
        
        .equipment-grid {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .equipment-details {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Location Header -->
    <div class="location-header">
        <h1 class="location-title">{{ location.name }}</h1>
        <p class="location-subtitle">الرقم التسلسلي: {{ location.serial_number }}</p>
    </div>

    <div class="row">
        <!-- Basic Information -->
        <div class="col-lg-8">
            <div class="info-card">
                <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">نوع الموقع</div>
                            <div class="info-value">{{ location.type }}</div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-circle-check"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">حالة الموقع</div>
                            <div class="info-value">
                                <span class="status-badge status-{{ 'active' if location.status == 'نشط' else 'inactive' if location.status == 'غير نشط' else 'withdrawn' if location.status == 'مسحوب' else 'maintenance' }}">
                                    {{ location.status }}
                                </span>
                            </div>
                        </div>
                    </div>

                    {% if location.coordinates %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-map-pin"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">الإحداثيات</div>
                            <div class="info-value">{{ location.coordinates }}</div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">تاريخ الإنشاء</div>
                            <div class="info-value">{{ location.created_at.strftime('%Y-%m-%d') if location.created_at else 'غير محدد' }}</div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">عدد العهد</div>
                            <div class="info-value">{{ location.equipment.count() }} عهدة</div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">عدد الأفراد</div>
                            <div class="info-value">{{ location.personnel_assignments.filter_by(is_active=True).count() }} فرد</div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-hashtag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">معرف الموقع</div>
                            <div class="info-value">#{{ location.id }}</div>
                        </div>
                    </div>

                    {% if location.instructions_file %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">ملف التعليمات</div>
                            <div class="info-value">
                                <a href="{{ url_for('locations.download_instructions', location_id=location.id) }}" class="text-primary">
                                    <i class="fas fa-download"></i> تحميل ملف التعليمات
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                {% if location.description %}
                <div class="mt-4">
                    <h5><i class="fas fa-file-text"></i> الوصف</h5>
                    <p class="text-muted">{{ location.description }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-lg-4">
            <div class="info-card">
                <h3><i class="fas fa-chart-bar"></i> إحصائيات سريعة</h3>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>العهد الجيدة</span>
                        <span class="badge bg-success">{{ location.equipment.filter_by(condition_status='جيد').count() }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>تحتاج صيانة</span>
                        <span class="badge bg-warning">{{ location.equipment.filter_by(condition_status='يحتاج صيانة').count() }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>معطلة</span>
                        <span class="badge bg-danger">{{ location.equipment.filter_by(condition_status='معطل').count() }}</span>
                    </div>
                </div>

                <hr>

                <div class="text-center">
                    <h4 class="text-primary">{{ location.equipment.count() }}</h4>
                    <p class="text-muted mb-0">إجمالي العهد</p>
                </div>

                <hr>

                <div class="text-center">
                    <h4 class="text-success">{{ location.personnel_assignments.filter_by(is_active=True).count() }}</h4>
                    <p class="text-muted mb-0">الأفراد المرتبطين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Equipment Section -->
    <div class="info-card">
        <h3><i class="fas fa-boxes"></i> العهد المخصصة للموقع</h3>
        
        {% if location.equipment %}
            <div class="equipment-grid">
                {% for equipment in location.equipment %}
                <div class="equipment-item">
                    <div class="equipment-header">
                        <h4 class="equipment-name">{{ equipment.equipment_name }}</h4>
                        {% if equipment.equipment_type %}
                        <span class="equipment-type">{{ equipment.equipment_type }}</span>
                        {% endif %}
                    </div>
                    
                    <div class="equipment-details">
                        {% if equipment.serial_number %}
                        <div class="equipment-detail">
                            <strong>الرقم التسلسلي:</strong><br>
                            {{ equipment.serial_number }}
                        </div>
                        {% endif %}
                        
                        <div class="equipment-detail">
                            <strong>الكمية:</strong><br>
                            {{ equipment.quantity }}
                        </div>
                        
                        <div class="equipment-detail">
                            <strong>الحالة:</strong><br>
                            <span class="condition-badge condition-{{ 'good' if equipment.condition_status == 'جيد' else 'average' if equipment.condition_status == 'متوسط' else 'needs-repair' if equipment.condition_status == 'يحتاج صيانة' else 'broken' }}">
                                {{ equipment.condition_status }}
                            </span>
                        </div>
                        
                        {% if equipment.notes %}
                        <div class="equipment-detail">
                            <strong>ملاحظات:</strong><br>
                            {{ equipment.notes }}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-equipment">
                <i class="fas fa-box-open"></i>
                <h4>لا توجد عهد مخصصة</h4>
                <p>لم يتم تخصيص أي عهد لهذا الموقع بعد.</p>
            </div>
        {% endif %}
    </div>

    <!-- Personnel Section -->
    <div class="info-card">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3><i class="fas fa-users"></i> الأفراد المرتبطين بالموقع</h3>
            <button type="button" class="btn btn-primary" onclick="openAddPersonnelModal()">
                <i class="fas fa-plus"></i> إضافة فرد
            </button>
        </div>

        {% if location.personnel %}
            <div class="row">
                {% for person in location.personnel %}
                <div class="col-md-4 col-lg-3 mb-3">
                    <div class="card h-100 personnel-compact-card">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-1 text-truncate" title="{{ person.name }}">{{ person.name }}</h6>
                                <button type="button" class="btn btn-sm btn-outline-danger btn-xs"
                                        onclick="removePersonnel({{ person.id }}, '{{ person.name }}')"
                                        title="إزالة من الموقع">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <div class="mb-1">
                                <small class="text-muted">الرقم العسكري:</small>
                                <div class="fw-bold small">{{ person.personnel_id }}</div>
                            </div>

                            <div class="mb-1">
                                <small class="text-muted">الحالة:</small>
                                <div>
                                    {% if person.status == 'نشط' %}
                                    <span class="badge badge-success badge-sm">{{ person.status }}</span>
                                    {% elif person.status == 'إجازة' %}
                                    <span class="badge badge-warning badge-sm">{{ person.status }}</span>
                                    {% elif person.status == 'مهمة' %}
                                    <span class="badge badge-mission badge-sm">{{ person.status }}</span>
                                    {% elif person.status == 'دورة' %}
                                    <span class="badge badge-danger badge-sm">{{ person.status }}</span>
                                    {% elif person.status == 'مستلم' %}
                                    <span class="badge badge-primary badge-sm">{{ person.status }}</span>
                                    {% elif person.status == 'رماية' %}
                                    <span class="badge badge-shooting badge-sm">{{ person.status }}</span>
                                    {% else %}
                                    <span class="badge badge-secondary badge-sm">{{ person.status }}</span>
                                    {% endif %}
                                </div>
                            </div>

                            {% if person.rank %}
                            <div class="mb-1">
                                <small class="text-muted">الرتبة:</small>
                                <div class="small">{{ person.rank }}</div>
                            </div>
                            {% endif %}

                            <div class="mb-1">
                                <small class="text-muted">الوحدة:</small>
                                <div class="small text-truncate" title="{{ person.warehouse_name }}">{{ person.warehouse_name }}</div>
                            </div>

                            {% if person.assignment_date %}
                            <div class="mb-1">
                                <small class="text-muted">تاريخ التعيين:</small>
                                <div class="small">{{ person.assignment_date.strftime('%Y-%m-%d') if person.assignment_date else 'غير محدد' }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-equipment">
                <i class="fas fa-user-plus"></i>
                <h4>لا يوجد أفراد مرتبطين</h4>
                <p>لم يتم تعيين أي أفراد لهذا الموقع بعد.</p>
                <button type="button" class="btn btn-primary mt-3" onclick="openAddPersonnelModal()">
                    <i class="fas fa-plus"></i> إضافة أول فرد
                </button>
            </div>
        {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{{ url_for('locations.index') }}" class="btn-action btn-back">
            <i class="fas fa-arrow-right"></i>
            العودة للقائمة
        </a>
        {% if location.instructions_file %}
        <a href="{{ url_for('locations.download_instructions', location_id=location.id) }}" class="btn-action" style="background: linear-gradient(135deg, #17a2b8, #138496); color: white; border: none;">
            <i class="fas fa-file-pdf"></i>
            تحميل تعليمات الموقع
        </a>
        {% endif %}
        <a href="{{ url_for('locations.edit_location', location_id=location.id) }}" class="btn-action btn-edit">
            <i class="fas fa-edit"></i>
            تعديل الموقع
        </a>
        <button type="button" class="btn-action btn-delete" onclick="deleteLocation({{ location.id }}, '{{ location.name }}')">
            <i class="fas fa-trash"></i>
            حذف الموقع
        </button>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الموقع "<span id="locationNameToDelete"></span>"؟</p>
                <p class="text-danger"><small>سيتم حذف جميع العهد المرتبطة بهذا الموقع أيضاً.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Personnel Modal -->
<div class="modal fade" id="addPersonnelModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة فرد للموقع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPersonnelForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="searchTerm" class="form-label">رقم الهوية الوطنية أو الرقم العسكري</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchTerm"
                                           placeholder="أدخل رقم الهوية أو الرقم العسكري"
                                           oninput="autoSearchPersonnel(this.value)">
                                    <button type="button" class="btn btn-outline-primary" onclick="searchPersonnel();">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                                <small class="form-text text-muted">سيتم البحث تلقائياً عند الكتابة</small>
                            </div>

                            <!-- Personnel Assignment Details -->
                            <div id="assignmentDetails" style="display: none;">
                                <div class="mb-3">
                                    <label for="shiftType" class="form-label">نوع الوردية</label>
                                    <select class="form-select" id="shiftType">
                                        <option value="عام">عام</option>
                                        <option value="صباحية">صباحية</option>
                                        <option value="مسائية">مسائية</option>
                                        <option value="ليلية">ليلية</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="assignmentNotes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="assignmentNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Personnel Information Display -->
                            <div id="personnelSearchResult" style="display: none;">
                                <div class="card personnel-info-card search-result-enter">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-user"></i> معلومات الفرد</h6>
                                    </div>
                                    <div class="card-body" id="personnelInfo">
                                        <!-- سيتم ملء هذا القسم ديناميكياً -->
                                    </div>
                                </div>
                            </div>

                            <!-- Search Status -->
                            <div id="searchStatus" style="display: none;">
                                <div class="alert alert-info search-status">
                                    <i class="fas fa-spinner fa-spin spinner"></i> جاري البحث...
                                </div>
                            </div>

                            <!-- Search Error -->
                            <div id="searchError" class="alert alert-danger" style="display: none;"></div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="addPersonnelBtn" onclick="addPersonnelToLocation()" style="display: none;">
                    <i class="fas fa-plus"></i> إضافة للموقع
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Remove Personnel Confirmation Modal -->
<div class="modal fade" id="removePersonnelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إزالة الفرد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إزالة الفرد "<span id="personnelNameToRemove"></span>" من هذا الموقع؟</p>
                <p class="text-warning"><small>سيتم إزالة الفرد من الموقع فقط وليس من النظام.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmRemovePersonnel">إزالة</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPersonnelData = null;
let searchTimeout = null;

function openAddPersonnelModal() {
    const modal = new bootstrap.Modal(document.getElementById('addPersonnelModal'));
    modal.show();

    // Reset form with safety checks
    const form = document.getElementById('addPersonnelForm');
    if (form) form.reset();

    hideAllSearchElements();
    currentPersonnelData = null;

    // Setup search key listener after modal is shown
    setTimeout(setupSearchKeyListener, 100);
}

function hideAllSearchElements() {
    const searchResult = document.getElementById('personnelSearchResult');
    if (searchResult) searchResult.style.display = 'none';

    const searchError = document.getElementById('searchError');
    if (searchError) searchError.style.display = 'none';

    const searchStatus = document.getElementById('searchStatus');
    if (searchStatus) searchStatus.style.display = 'none';

    const assignmentDetails = document.getElementById('assignmentDetails');
    if (assignmentDetails) assignmentDetails.style.display = 'none';

    const addBtn = document.getElementById('addPersonnelBtn');
    if (addBtn) addBtn.style.display = 'none';
}

function autoSearchPersonnel(searchTerm) {
    // Clear previous timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    // Hide all elements first
    hideAllSearchElements();

    // If search term is empty, return
    if (!searchTerm.trim()) {
        return;
    }

    // Show loading status
    const searchStatus = document.getElementById('searchStatus');
    if (searchStatus) searchStatus.style.display = 'block';

    // Set timeout for auto search (500ms delay)
    searchTimeout = setTimeout(() => {
        performSearch(searchTerm.trim());
    }, 500);
}

function searchPersonnel() {
    const searchInput = document.getElementById('searchTerm');

    if (!searchInput) {
        showSearchError('خطأ: لم يتم العثور على حقل البحث');
        return;
    }

    const searchTerm = searchInput.value.trim();

    if (!searchTerm) {
        showSearchError('يرجى إدخال رقم الهوية أو الرقم العسكري');
        return;
    }

    hideAllSearchElements();
    const searchStatus = document.getElementById('searchStatus');
    if (searchStatus) searchStatus.style.display = 'block';

    performSearch(searchTerm);
}

function performSearch(searchTerm) {
    fetch(`/locations/{{ location.id }}/personnel/search?search_term=${encodeURIComponent(searchTerm)}`)
        .then(response => {
            // التحقق من أن الاستجابة JSON وليس HTML
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('الاستجابة ليست JSON - قد تحتاج لتسجيل الدخول مرة أخرى');
            }

            return response.json();
        })
        .then(data => {
            const searchStatus = document.getElementById('searchStatus');
            if (searchStatus) searchStatus.style.display = 'none';

            if (data.success && data.personnel && data.personnel.length > 0) {
                // إذا كان هناك أكثر من فرد، اعرض قائمة للاختيار
                if (data.personnel.length > 1) {
                    showPersonnelList(data.personnel);
                } else {
                    // إذا كان فرد واحد فقط، اعرضه مباشرة
                    const person = data.personnel[0];
                    showPersonnelInfo(person, person.already_assigned);
                    currentPersonnelData = person;
                }
            } else {
                showSearchError(data.message || 'لم يتم العثور على فرد بهذا الرقم');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            const searchStatus = document.getElementById('searchStatus');
            if (searchStatus) searchStatus.style.display = 'none';
            showSearchError('حدث خطأ أثناء البحث. تأكد من تسجيل الدخول.');
        });
}

function showPersonnelList(personnelArray) {
    const infoDiv = document.getElementById('personnelInfo');
    if (infoDiv) {
        let listHtml = `
            <div class="alert alert-info mb-3">
                <i class="fas fa-info-circle"></i>
                <strong>تم العثور على ${personnelArray.length} أفراد. اختر الفرد المطلوب:</strong>
            </div>
        `;

        personnelArray.forEach((person, index) => {
            const alreadyAssignedBadge = person.already_assigned ?
                '<span class="badge bg-warning text-dark ms-2">مضاف بالفعل</span>' : '';

            listHtml += `
                <div class="card mb-2 personnel-card ${person.already_assigned ? 'border-warning' : ''}"
                     style="cursor: pointer;"
                     onclick="selectPersonnel(${index})">
                    <div class="card-body py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${person.name}</strong> ${alreadyAssignedBadge}
                                <br>
                                <small class="text-muted">
                                    الرقم العسكري: ${person.personnel_id} |
                                    الهوية: ${person.phone || 'غير محدد'} |
                                    المستودع: ${person.warehouse_name || 'غير محدد'}
                                </small>
                            </div>
                            <div>
                                <span class="badge bg-primary">${person.rank || 'غير محدد'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        infoDiv.innerHTML = listHtml;

        // حفظ قائمة الأفراد للاستخدام لاحقاً
        window.currentPersonnelList = personnelArray;
    }

    const searchResult = document.getElementById('personnelSearchResult');
    if (searchResult) searchResult.style.display = 'block';

    const searchError = document.getElementById('searchError');
    if (searchError) searchError.style.display = 'none';

    // إخفاء تفاصيل التعيين حتى يتم اختيار فرد
    const assignmentDetails = document.getElementById('assignmentDetails');
    if (assignmentDetails) assignmentDetails.style.display = 'none';

    const addBtn = document.getElementById('addPersonnelBtn');
    if (addBtn) addBtn.style.display = 'none';
}

function selectPersonnel(index) {
    if (window.currentPersonnelList && window.currentPersonnelList[index]) {
        const person = window.currentPersonnelList[index];
        showPersonnelInfo(person, person.already_assigned);
        currentPersonnelData = person;
    }
}

function showPersonnelInfo(personnel, alreadyAssigned = false, warningMessage = null) {
    const infoDiv = document.getElementById('personnelInfo');
    if (infoDiv) {
        infoDiv.innerHTML = `
            ${alreadyAssigned ? `
            <div class="alert alert-warning mb-3" style="border-right: 4px solid #ffc107;">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>تنبيه:</strong> ${warningMessage || 'هذا الفرد مضاف بالفعل في هذا الموقع'}
            </div>
            ` : ''}
            <div class="row">
                <div class="col-12">
                    <h5 class="text-primary mb-3">${personnel.name}</h5>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-6"><strong>الرقم العسكري:</strong></div>
                <div class="col-6">${personnel.personnel_id}</div>
            </div>
            <div class="row mb-2">
                <div class="col-6"><strong>رقم الهوية:</strong></div>
                <div class="col-6">${personnel.phone || 'غير محدد'}</div>
            </div>
            <div class="row mb-2">
                <div class="col-6"><strong>الرتبة:</strong></div>
                <div class="col-6"><span class="badge bg-primary">${personnel.rank || 'غير محدد'}</span></div>
            </div>
            <div class="row mb-2">
                <div class="col-6"><strong>المستودع:</strong></div>
                <div class="col-6">${personnel.warehouse_name || 'غير محدد'}</div>
            </div>
            ${personnel.unit ? `
            <div class="row mb-2">
                <div class="col-6"><strong>الوحدة:</strong></div>
                <div class="col-6">${personnel.unit}</div>
            </div>
            ` : ''}
        `;
    }

    const searchResult = document.getElementById('personnelSearchResult');
    if (searchResult) searchResult.style.display = 'block';

    const assignmentDetails = document.getElementById('assignmentDetails');
    if (assignmentDetails) {
        if (alreadyAssigned) {
            assignmentDetails.style.display = 'none';
        } else {
            assignmentDetails.style.display = 'block';
        }
    }

    const searchError = document.getElementById('searchError');
    if (searchError) searchError.style.display = 'none';

    const addBtn = document.getElementById('addPersonnelBtn');
    if (addBtn) {
        if (alreadyAssigned) {
            addBtn.style.display = 'none';
        } else {
            addBtn.style.display = 'block';
        }
    }
}

function getStatusBadgeClass(status) {
    switch(status) {
        case 'نشط': return 'badge-success';
        case 'إجازة': return 'badge-warning';
        case 'مهمة': return 'badge-mission';
        case 'دورة': return 'badge-danger';
        case 'مستلم': return 'badge-primary';
        case 'رماية': return 'badge-shooting';
        default: return 'badge-secondary';
    }
}

function showSearchError(message) {
    const searchError = document.getElementById('searchError');
    if (searchError) {
        searchError.textContent = message;
        searchError.style.display = 'block';
    }

    const searchResult = document.getElementById('personnelSearchResult');
    if (searchResult) searchResult.style.display = 'none';

    const addBtn = document.getElementById('addPersonnelBtn');
    if (addBtn) addBtn.style.display = 'none';
}

function addPersonnelToLocation() {
    if (!currentPersonnelData) {
        showSearchError('لم يتم العثور على بيانات الفرد');
        return;
    }

    // التحقق من أن الفرد ليس مضاف بالفعل
    if (currentPersonnelData.already_assigned) {
        showNotification('هذا الفرد مضاف بالفعل في هذا الموقع', 'warning');
        return;
    }

    const shiftTypeEl = document.getElementById('shiftType');
    const notesEl = document.getElementById('assignmentNotes');
    const shiftType = shiftTypeEl ? shiftTypeEl.value : '';
    const notes = notesEl ? notesEl.value.trim() : '';

    const addBtn = document.getElementById('addPersonnelBtn');
    if (!addBtn) {
        console.error('Add button not found');
        return;
    }

    const originalText = addBtn.innerHTML;
    addBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
    addBtn.disabled = true;

    const requestData = {
        personnel_id: currentPersonnelData.id,
        shift_type: shiftType,
        notes: notes
    };



    fetch(`/locations/{{ location.id }}/personnel/add`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إضافة الفرد للموقع بنجاح', 'success');

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addPersonnelModal'));
            if (modal) {
                modal.hide();
            }

            // تحديث قائمة الأفراد فوراً
            refreshPersonnelList();
        } else {
            // Show error message with better styling
            if (data.message.includes('مرتبط بالفعل بموقع:')) {
                showNotification(data.message, 'warning');
            } else if (data.message.includes('مضاف بالفعل في هذا الموقع')) {
                showNotification('هذا الفرد مضاف بالفعل في هذا الموقع', 'info');
            } else {
                showNotification(data.message || 'حدث خطأ أثناء إضافة الفرد', 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء إضافة الفرد', 'error');
    })
    .finally(() => {
        if (addBtn) {
            addBtn.innerHTML = originalText;
            addBtn.disabled = false;
        }
    });
}

function refreshPersonnelList() {
    // إعادة تحميل قسم الأفراد فقط
    fetch(`/locations/{{ location.id }}/personnel/list`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updatePersonnelSection(data.personnel);
                updatePersonnelCount(data.count);

                // تحديث عدد الأفراد في الصفحة الرئيسية إذا كانت مفتوحة
                if (window.opener && !window.opener.closed) {
                    try {
                        window.opener.updateLocationPersonnelCount({{ location.id }});
                    } catch (e) {
                        console.log('لا يمكن تحديث الصفحة الرئيسية');
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error refreshing personnel list:', error);
            // في حالة الخطأ، أعد تحميل الصفحة
            location.reload();
        });
}

function updatePersonnelSection(personnel) {
    // البحث عن قسم الأفراد
    const allInfoCards = document.querySelectorAll('.info-card');
    let personnelSection = null;

    for (let card of allInfoCards) {
        const h3 = card.querySelector('h3');
        if (h3 && h3.textContent.includes('الأفراد المرتبطين')) {
            personnelSection = card;
            break;
        }
    }

    if (!personnelSection) {
        location.reload();
        return;
    }

    const personnelContainer = personnelSection.querySelector('.row');
    if (!personnelContainer) {
        location.reload();
        return;
    }

    if (personnel.length === 0) {
        personnelContainer.innerHTML = `
            <div class="col-12">
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا يوجد أفراد مرتبطين بهذا الموقع</p>
                </div>
            </div>
        `;
    } else {
        let html = '';
        personnel.forEach(person => {
            html += `
                <div class="col-md-4 col-lg-3 mb-3">
                    <div class="card h-100 personnel-compact-card">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-1 text-truncate" title="${person.name}">${person.name}</h6>
                                <button type="button" class="btn btn-sm btn-outline-danger btn-xs"
                                        onclick="removePersonnel(${person.id}, '${person.name}')"
                                        title="إزالة من الموقع">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <div class="mb-1">
                                <small class="text-muted">الرقم العسكري:</small>
                                <div class="fw-bold small">${person.personnel_id}</div>
                            </div>

                            <div class="mb-1">
                                <small class="text-muted">الرتبة:</small>
                                <div class="small">
                                    <span class="badge bg-primary badge-sm">${person.rank || 'غير محدد'}</span>
                                </div>
                            </div>

                            <div class="mb-1">
                                <small class="text-muted">المستودع:</small>
                                <div class="small">${person.warehouse_name || 'غير محدد'}</div>
                            </div>

                            ${person.assignment_notes ? `
                            <div class="mb-1">
                                <small class="text-muted">ملاحظات:</small>
                                <div class="small text-info">${person.assignment_notes}</div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        personnelContainer.innerHTML = html;
    }
}

function updatePersonnelCount(count) {
    // تحديث عدد الأفراد في البطاقة الإحصائية
    const infoValues = document.querySelectorAll('.info-value');
    infoValues.forEach(element => {
        if (element.textContent.includes('فرد')) {
            element.textContent = count + ' فرد';
        }
    });

    // تحديث العدد في البطاقة الإحصائية الأخرى
    const successHeaders = document.querySelectorAll('h4.text-success');
    successHeaders.forEach(element => {
        const nextElement = element.nextElementSibling;
        if (nextElement && nextElement.textContent.includes('الأفراد المرتبطين')) {
            element.textContent = count;
        }
    });
}

function removePersonnel(personnelId, personnelName) {
    const nameElement = document.getElementById('personnelNameToRemove');
    if (nameElement) {
        nameElement.textContent = personnelName;
    }

    const modalElement = document.getElementById('removePersonnelModal');
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    }

    const confirmBtn = document.getElementById('confirmRemovePersonnel');
    if (confirmBtn) {
        confirmBtn.onclick = function() {
        fetch(`/locations/{{ location.id }}/personnel/${personnelId}/remove`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم إزالة الفرد من الموقع بنجاح', 'success');

                // إغلاق النافذة المنبثقة
                const modal = bootstrap.Modal.getInstance(document.getElementById('removePersonnelModal'));
                if (modal) {
                    modal.hide();
                }

                // تحديث قائمة الأفراد فوراً
                refreshPersonnelList();
            } else {
                showNotification(data.message || 'حدث خطأ أثناء إزالة الفرد', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء إزالة الفرد', 'error');
        });

        modal.hide();
        };
    }
}

// Allow Enter key to trigger search - moved to modal show event
function setupSearchKeyListener() {
    const searchInput = document.getElementById('searchTerm');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchPersonnel();
            }
        });
    }
}
</script>

<script>
function deleteLocation(id, name) {
    document.getElementById('locationNameToDelete').textContent = name;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
    
    document.getElementById('confirmDelete').onclick = function() {
        fetch(`/locations/${id}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم حذف الموقع بنجاح', 'success');
                setTimeout(() => {
                    window.location.href = "{{ url_for('locations.index') }}";
                }, 1500);
            } else {
                showNotification(data.message || 'حدث خطأ أثناء حذف الموقع', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء حذف الموقع', 'error');
        });
        
        modal.hide();
    };
}
</script>
{% endblock %}
