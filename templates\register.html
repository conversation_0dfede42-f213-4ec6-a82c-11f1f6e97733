{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>إضافة مستخدم جديد</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة المستخدمين
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-user-plus"></i> بيانات المستخدم</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('auth.register') }}">
            {{ form.hidden_tag() }}
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.username.label }}
                        {{ form.username(class="form-control") }}
                        {% if form.username.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.username.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.full_name.label }}
                        {{ form.full_name(class="form-control") }}
                        {% if form.full_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.full_name.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.email.label }}
                        {{ form.email(class="form-control") }}
                        {% if form.email.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.email.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.password.label }}
                        {{ form.password(class="form-control") }}
                        {% if form.password.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.confirm_password.label }}
                        {{ form.confirm_password(class="form-control") }}
                        {% if form.confirm_password.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.confirm_password.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.role.label }}
                        {{ form.role(class="form-control") }}
                        {% if form.role.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.role.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6" id="warehouse-field">
                    <div class="form-group">
                        {{ form.warehouses.label }}
                        {{ form.warehouses(class="form-control") }}
                        {% if form.warehouses.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.warehouses.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted admin-note" style="display: none;">مدير النظام لديه صلاحية على جميع المستودعات تلقائياً</small>
                    </div>
                </div>
            </div>
            <div class="form-group mt-4">
                {{ form.submit(class="btn btn-primary") }}
                <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary ml-2">إلغاء</a>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const roleSelect = document.getElementById('role');
                    const warehouseField = document.getElementById('warehouse-field');
                    const adminNote = document.querySelector('.admin-note');

                    // إخفاء خيار "مدير نظام" إذا كان المستخدم الحالي هو مدير مستودعات
                    {% if current_user.is_warehouse_manager and not current_user.is_admin_role %}
                    // إزالة خيار مدير النظام من القائمة
                    for (let i = 0; i < roleSelect.options.length; i++) {
                        if (roleSelect.options[i].value === 'admin') {
                            roleSelect.remove(i);
                            break;
                        }
                    }
                    {% endif %}

                    function updateWarehouseField() {
                        if (roleSelect.value === 'admin' || roleSelect.value === 'warehouse_manager') {
                            // بدلاً من تعطيل الحقل، نقوم بإخفائه ولكن نترك قيمته الافتراضية
                            // هذا يضمن إرسال قيمة صالحة مع النموذج
                            warehouseField.style.display = 'none';
                            adminNote.style.display = 'block';
                            if (roleSelect.value === 'warehouse_manager') {
                                adminNote.textContent = 'مدير المستودعات لديه صلاحية على جميع المستودعات تلقائياً';
                            } else {
                                adminNote.textContent = 'مدير النظام لديه صلاحية على جميع المستودعات تلقائياً';
                            }
                        } else if (roleSelect.value === 'company_duty') {
                            // إخفاء حقل المستودع لمناوب السرية
                            warehouseField.style.display = 'none';
                            adminNote.style.display = 'block';
                            adminNote.textContent = 'مناوب السرية لا يحتاج إلى مستودعات (يعمل على كشف الاستلامات وإدارة المواقع)';
                        } else {
                            warehouseField.style.display = 'block';
                            warehouseField.querySelector('select').disabled = false;
                            adminNote.style.display = 'none';
                        }
                    }

                    // تنفيذ عند تحميل الصفحة
                    updateWarehouseField();

                    // تنفيذ عند تغيير الدور
                    roleSelect.addEventListener('change', updateWarehouseField);
                });
            </script>
        </form>
    </div>
</div>
{% endblock %}