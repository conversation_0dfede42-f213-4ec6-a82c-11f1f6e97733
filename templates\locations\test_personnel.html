<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأفراد التجريبيين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 50px auto;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
        }
        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
        }
        .btn-danger {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
        }
        .location-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
            transition: all 0.3s ease;
        }
        .location-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center">
                <h2><i class="fas fa-users-cog"></i> إدارة الأفراد التجريبيين</h2>
                <p class="mb-0">أضف أو احذف أفراد تجريبيين للمواقع للاختبار</p>
            </div>
            <div class="card-body">
                <!-- رسائل التنبيه -->
                <div id="alertContainer"></div>
                
                <!-- أزرار التحكم العامة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100" onclick="checkTables()">
                            <i class="fas fa-database"></i> فحص قاعدة البيانات
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-success w-100" onclick="fixPersonnelCount()">
                            <i class="fas fa-tools"></i> إصلاح عدد الأفراد
                        </button>
                    </div>
                </div>

                <!-- قائمة المواقع -->
                <h4><i class="fas fa-map-marker-alt"></i> المواقع المتاحة:</h4>
                <div id="locationsContainer">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل المواقع...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحميل المواقع عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadLocations();
        });

        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.appendChild(alertDiv);
            
            // إزالة التنبيه بعد 5 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function loadLocations() {
            fetch('/locations/api/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayLocations(data.locations);
                    } else {
                        showAlert('خطأ في تحميل المواقع: ' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('خطأ في الاتصال: ' + error.message, 'danger');
                });
        }

        function displayLocations(locations) {
            const container = document.getElementById('locationsContainer');
            
            if (locations.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">لا توجد مواقع متاحة</div>';
                return;
            }

            let html = '';
            locations.forEach(location => {
                html += `
                    <div class="location-card">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5><i class="fas fa-map-marker-alt text-primary"></i> ${location.name}</h5>
                                <small class="text-muted">${location.type} - ${location.description || 'لا يوجد وصف'}</small>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-success btn-sm me-2" onclick="addTestPersonnel(${location.id}, '${location.name}')">
                                    <i class="fas fa-plus"></i> إضافة أفراد
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="clearTestPersonnel(${location.id}, '${location.name}')">
                                    <i class="fas fa-trash"></i> حذف الأفراد
                                </button>
                                <a href="/locations/${location.id}" class="btn btn-primary btn-sm ms-2" target="_blank">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function checkTables() {
            fetch('/locations/check-tables')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let message = `
                            <strong>نتائج فحص قاعدة البيانات:</strong><br>
                            - عدد الجداول: ${data.tables.length}<br>
                            - جدول personnel موجود: ${data.personnel_table_exists ? 'نعم' : 'لا'}<br>
                            - عدد المواقع: ${data.locations_count}<br>
                            - عدد الأفراد المرتبطين: ${data.location_personnel_count}
                        `;
                        showAlert(message, 'info');
                    } else {
                        showAlert('خطأ في فحص قاعدة البيانات: ' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('خطأ في الاتصال: ' + error.message, 'danger');
                });
        }

        function fixPersonnelCount() {
            fetch('/locations/fix-personnel-count')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert(data.message, 'success');
                    } else {
                        showAlert('خطأ في إصلاح البيانات: ' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('خطأ في الاتصال: ' + error.message, 'danger');
                });
        }

        function addTestPersonnel(locationId, locationName) {
            if (confirm(`هل تريد إضافة أفراد تجريبيين للموقع "${locationName}"؟`)) {
                fetch(`/locations/add-test-personnel/${locationId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showAlert(data.message, 'success');
                        } else {
                            showAlert('خطأ في إضافة الأفراد: ' + data.message, 'danger');
                        }
                    })
                    .catch(error => {
                        showAlert('خطأ في الاتصال: ' + error.message, 'danger');
                    });
            }
        }

        function clearTestPersonnel(locationId, locationName) {
            if (confirm(`هل تريد حذف جميع الأفراد من الموقع "${locationName}"؟\nهذا الإجراء لا يمكن التراجع عنه!`)) {
                fetch(`/locations/clear-test-personnel/${locationId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showAlert(data.message, 'success');
                        } else {
                            showAlert('خطأ في حذف الأفراد: ' + data.message, 'danger');
                        }
                    })
                    .catch(error => {
                        showAlert('خطأ في الاتصال: ' + error.message, 'danger');
                    });
            }
        }
    </script>
</body>
</html>
