/* Archivo CSS personalizado para aumentar el tamaño de los números en las barras de progreso */

/* Aumentar el tamaño de los números en las etiquetas de progreso */
.progress-label span:last-child {
    font-size: 1.5rem;
    font-weight: bold;
}

/* Aumentar el tamaño de las etiquetas de texto */
.progress-label span:first-child {
    font-size: 1.2rem;
    font-weight: bold;
}

/* Aumentar la altura de las barras de progreso */
.progress-bar-container {
    height: 15px !important;
    margin-bottom: 15px;
}

/* Aumentar el espacio entre las barras de progreso */
.progress-container {
    margin-bottom: 1.5rem !important;
}

/* Estilo para la leyenda del gráfico de donut con números */
.chart-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 1.5rem;
}

.legend-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0.5rem 1rem;
    text-align: center;
    min-width: 60px;
}

.legend-color-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-left: 0.5rem;
}

.legend-label {
    font-size: 1rem;
    font-weight: bold;
}

.legend-count {
    font-size: 1.2rem;
    font-weight: bold;
    margin-top: 0.25rem;
}
