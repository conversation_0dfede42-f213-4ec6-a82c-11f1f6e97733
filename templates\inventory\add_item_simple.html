{% extends "base.html" %}

{% block title %}إضافة صنف جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-plus text-primary"></i>
                    إضافة صنف جديد للمخزون
                </h2>
                <a href="{{ url_for('inventory_management.items') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        بيانات الصنف الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('inventory_management.add_item') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.item_code.label(class="form-label required") }}
                                    {{ form.item_code(class="form-control", placeholder="مثال: CLO-001") }}
                                    {% if form.item_code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.item_code.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">كود فريد للصنف (يمكن استخدامه كباركود)</small>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label required") }}
                                    {{ form.name(class="form-control", placeholder="مثال: زي عسكري صيفي") }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.category.label(class="form-label required") }}
                                    {{ form.category(class="form-control", id="category") }}
                                    {% if form.category.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.category.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.subcategory.label(class="form-label") }}
                                    {{ form.subcategory(class="form-control", placeholder="مثال: زي صيفي") }}
                                    {% if form.subcategory.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.subcategory.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.quantity_in_stock.label(class="form-label required") }}
                                    {{ form.quantity_in_stock(class="form-control", placeholder="0") }}
                                    {% if form.quantity_in_stock.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.quantity_in_stock.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.size.label(class="form-label") }}
                                    {{ form.size(class="form-control", placeholder="مثال: كبير، متوسط، صغير") }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.color.label(class="form-label") }}
                                    {{ form.color(class="form-control", placeholder="مثال: أخضر، بني، أسود") }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-group">
                                    {{ form.notes.label(class="form-label") }}
                                    {{ form.notes(class="form-control", rows="3", placeholder="أي ملاحظات إضافية حول الصنف...") }}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{{ url_for('inventory_management.items') }}" class="btn btn-secondary me-2">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    {{ form.submit(class="btn btn-primary") }}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تفعيل التركيز على حقل الكود عند تحميل الصفحة
    $('#item_code').focus();
    
    // إنشاء كود تلقائي بناءً على الفئة
    $('#category').change(function() {
        var category = $(this).val();
        var codeField = $('#item_code');
        
        if (category && !codeField.val()) {
            var prefix = '';
            switch(category) {
                case 'ملبوسات':
                    prefix = 'CLO-';
                    break;
                case 'معدات':
                    prefix = 'EQP-';
                    break;
                case 'ذخيرة':
                    prefix = 'AMM-';
                    break;
                default:
                    prefix = 'ITM-';
            }
            
            // إنشاء رقم عشوائي
            var randomNum = Math.floor(Math.random() * 9000) + 1000;
            codeField.val(prefix + randomNum);
        }
    });
    
    // دعم مسح الباركود
    var barcodeBuffer = '';
    var barcodeTimeout;
    
    $(document).keypress(function(e) {
        // إذا كان المستخدم يكتب في حقل الكود
        if ($('#item_code').is(':focus')) {
            return;
        }
        
        // تجميع الأحرف للباركود
        barcodeBuffer += String.fromCharCode(e.which);
        
        clearTimeout(barcodeTimeout);
        barcodeTimeout = setTimeout(function() {
            if (barcodeBuffer.length > 3) {
                $('#item_code').val(barcodeBuffer);
                $('#item_code').focus();
            }
            barcodeBuffer = '';
        }, 100);
    });
});
</script>
{% endblock %}
