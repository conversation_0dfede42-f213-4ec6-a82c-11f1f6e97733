{% extends "base.html" %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/backup-dashboard.css') }}">
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            <i class="fas fa-server"></i> النسخ الاحتياطي واستعادة البيانات
        </h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى التقارير
        </a>
    </div>
</div>

<!-- لوحة الإحصائيات -->
<div class="backup-dashboard mb-4">
    <div class="row">
        <div class="col-md-3">
            <div class="stats-card success">
                <div class="card-icon">
                    <i class="fas fa-save"></i>
                </div>
                <div class="card-title">إجمالي النسخ الاحتياطية</div>
                <div class="card-value">{{ backup_records|length }}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card info">
                <div class="card-icon">
                    <i class="fas fa-database"></i>
                </div>
                <div class="card-title">إجمالي الحجم</div>
                <div class="card-value">{{ (backup_records|sum(attribute='file_size') / (1024*1024))|round(2) }} ميجابايت</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card warning">
                <div class="card-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="card-title">جدولات نشطة</div>
                <div class="card-value">{{ backup_schedules|selectattr('is_active', 'equalto', true)|list|length }}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="card-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="card-title">آخر نسخة احتياطية</div>
                <div class="card-value">{{ backup_records[0].timestamp.strftime('%Y-%m-%d') if backup_records else 'لا يوجد' }}</div>
            </div>
        </div>
    </div>
</div>

<!-- التنبيهات العامة -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="stats-card info">
            <div class="card-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="card-title">معلومات النسخ الاحتياطي</div>
            <p class="mt-2">يتم حفظ النسخ الاحتياطية في مجلد آمن على الخادم. يمكنك تنزيل النسخ الاحتياطية أو استعادتها في أي وقت.</p>
        </div>
    </div>
    <div class="col-md-6">
        <div class="stats-card warning">
            <div class="card-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="card-title">تنبيه</div>
            <p class="mt-2">استعادة النسخة الاحتياطية ستؤدي إلى استبدال جميع البيانات الحالية بالبيانات الموجودة في النسخة الاحتياطية.</p>
        </div>
    </div>
</div>

<!-- Nav tabs -->
<ul class="nav nav-tabs mb-4" id="backupTabs" role="tablist">
    <li class="nav-item">
        <a class="nav-link active" id="manual-tab" data-bs-toggle="tab" href="#manual" role="tab" aria-controls="manual" aria-selected="true">
            <i class="fas fa-download"></i> النسخ الاحتياطي اليدوي
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" id="schedule-tab" data-bs-toggle="tab" href="#schedule" role="tab" aria-controls="schedule" aria-selected="false">
            <i class="fas fa-calendar-alt"></i> جدولة النسخ الاحتياطي
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" id="history-tab" data-bs-toggle="tab" href="#history" role="tab" aria-controls="history" aria-selected="false">
            <i class="fas fa-history"></i> سجل النسخ الاحتياطية
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" id="restore-tab" data-bs-toggle="tab" href="#restore" role="tab" aria-controls="restore" aria-selected="false">
            <i class="fas fa-undo-alt"></i> استعادة البيانات
        </a>
    </li>
</ul>

<!-- Tab panes -->
<div class="tab-content">
    <!-- Manual Backup Tab -->
    <div class="tab-pane fade show active" id="manual" role="tabpanel" aria-labelledby="manual-tab">
        <div class="row">
            <div class="col-md-8">
                <div class="backup-table">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-download"></i> إنشاء نسخة احتياطية جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('reports.backup') }}" id="backup-form">
                            <input type="hidden" name="action" value="backup">
                            <input type="hidden" name="csrf_token" value="{{csrf_token()}}">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="warehouse_id">المستودع</label>
                                        <select name="warehouse_id" id="warehouse_id" class="form-control">
                                            <option value="0">جميع المستودعات</option>
                                            {% for warehouse in warehouses %}
                                            <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <small class="form-text text-muted">اختر المستودع الذي تريد إنشاء نسخة احتياطية له، أو اختر "جميع المستودعات" لإنشاء نسخة احتياطية كاملة.</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>الحجم المقدر</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="fas fa-hdd"></i></span>
                                            </div>
                                            <input type="text" class="form-control" id="estimated-size" value="2.5 ميجابايت" readonly>
                                        </div>
                                        <small class="form-text text-muted">الحجم التقريبي للنسخة الاحتياطية بناءً على الخيارات المحددة.</small>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <button type="button" id="advanced-options-toggle" class="btn btn-sm btn-link">
                                    <i class="fas fa-chevron-down"></i> عرض الخيارات المتقدمة
                                </button>
                            </div>

                            <div id="advanced-options" style="display: none;" class="advanced-options mb-4">
                                <h6 class="mb-3">محتوى النسخة الاحتياطية</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input backup-option" type="checkbox" id="option-weapons" name="include_weapons" checked>
                                            <label class="form-check-label" for="option-weapons">
                                                الأسلحة والذخائر
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input backup-option" type="checkbox" id="option-personnel" name="include_personnel" checked>
                                            <label class="form-check-label" for="option-personnel">
                                                الأفراد
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input backup-option" type="checkbox" id="option-transactions" name="include_transactions" checked>
                                            <label class="form-check-label" for="option-transactions">
                                                المعاملات
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input backup-option" type="checkbox" id="option-logs" name="include_logs" checked>
                                            <label class="form-check-label" for="option-logs">
                                                سجلات النشاط
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <h6 class="mb-3">خيارات إضافية</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input backup-option" type="checkbox" id="option-compression" name="compression" checked>
                                            <label class="form-check-label" for="option-compression">
                                                ضغط النسخة الاحتياطية
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input backup-option" type="checkbox" id="option-encryption" name="encryption">
                                            <label class="form-check-label" for="option-encryption">
                                                تشفير النسخة الاحتياطية
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="retention-slider">الاحتفاظ بالنسخ (أيام)</label>
                                            <div class="d-flex align-items-center">
                                                <input type="range" class="form-control-range" id="retention-slider" name="retention_days" min="1" max="90" value="30">
                                                <span id="retention-value" class="ml-2">30</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="progress backup-progress" id="backup-progress" style="display: none;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                            </div>

                            <div class="form-group text-center mt-4">
                                <button type="submit" id="create-backup-btn" class="btn btn-primary btn-lg">
                                    <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="backup-table">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-download"></i> إنشاء نسخة احتياطية</h5>
                    </div>
                    <div class="card-body text-center">
                        <form method="POST" action="{{ url_for('reports.backup') }}" id="quick-backup-form">
                            <input type="hidden" name="action" value="backup">
                            <input type="hidden" name="csrf_token" value="{{csrf_token()}}">
                            <input type="hidden" name="warehouse_id" value="0">

                            <p class="mb-4">قم بإنشاء نسخة احتياطية كاملة لجميع البيانات بنقرة واحدة</p>

                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="backup-table">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-upload"></i> رفع نسخة احتياطية</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('reports.backup') }}" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="upload_backup">
                            <input type="hidden" name="csrf_token" value="{{csrf_token()}}">

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="backup_file">اختر ملف النسخة الاحتياطية</label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="backup_file" name="backup_file" accept=".json" required>
                                            <label class="custom-file-label" for="backup_file">اختر ملف...</label>
                                        </div>
                                        <small class="form-text text-muted">يجب أن يكون الملف بصيغة JSON</small>
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <div class="form-group mb-0 w-100">
                                        <button type="submit" class="btn btn-primary btn-block">
                                            <i class="fas fa-upload"></i> رفع النسخة الاحتياطية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Backup Tab -->
    <div class="tab-pane fade" id="schedule" role="tabpanel" aria-labelledby="schedule-tab">
        <div class="row">
            <div class="col-md-5">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-calendar-plus"></i> إنشاء جدولة جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('reports.backup') }}">
                            <input type="hidden" name="action" value="create_schedule">
                            <input type="hidden" id="csrf_token" name="csrf_token" value="{{csrf_token()}}">

                            <div class="form-group">
                                <label for="schedule_type">{{ schedule_form.schedule_type.label }}</label>
                                {{ schedule_form.schedule_type(class="form-control", id="schedule_type") }}
                            </div>

                            <div class="form-group" id="day_of_week_group" style="display: none;">
                                <label for="day_of_week">{{ schedule_form.day_of_week.label }}</label>
                                {{ schedule_form.day_of_week(class="form-control", id="day_of_week") }}
                            </div>

                            <div class="form-group" id="day_of_month_group" style="display: none;">
                                <label for="day_of_month">{{ schedule_form.day_of_month.label }}</label>
                                {{ schedule_form.day_of_month(class="form-control", id="day_of_month") }}
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="hour">{{ schedule_form.hour.label }}</label>
                                    {{ schedule_form.hour(class="form-control", id="hour") }}
                                </div>
                                <div class="form-group col-md-6">
                                    <label for="minute">{{ schedule_form.minute.label }}</label>
                                    {{ schedule_form.minute(class="form-control", id="minute") }}
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="backup_type">{{ schedule_form.backup_type.label }}</label>
                                {{ schedule_form.backup_type(class="form-control", id="backup_type") }}
                            </div>

                            <div class="form-group" id="warehouse_id_group" style="display: none;">
                                <label for="warehouse_id_schedule">{{ schedule_form.warehouse_id.label }}</label>
                                {{ schedule_form.warehouse_id(class="form-control", id="warehouse_id_schedule") }}
                            </div>

                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    {{ schedule_form.is_active(class="custom-control-input", id="is_active") }}
                                    <label class="custom-control-label" for="is_active">{{ schedule_form.is_active.label }}</label>
                                </div>
                            </div>

                            <div class="form-group text-center">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ schedule_form.submit.label }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-7">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> جدولة النسخ الاحتياطي</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>النوع</th>
                                        <th>التكرار</th>
                                        <th>الوقت</th>
                                        <th>المستودع</th>
                                        <th>الحالة</th>
                                        <th>التشغيل التالي</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for schedule in backup_schedules %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            {% if schedule.backup_type == 'full' %}
                                            <span class="badge badge-primary">كامل</span>
                                            {% else %}
                                            <span class="badge badge-secondary">مستودع</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if schedule.schedule_type == 'daily' %}
                                            <span class="badge badge-info">يومي</span>
                                            {% elif schedule.schedule_type == 'weekly' %}
                                            <span class="badge badge-info">أسبوعي</span>
                                            ({{ ['الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'][schedule.day_of_week] }})
                                            {% elif schedule.schedule_type == 'monthly' %}
                                            <span class="badge badge-info">شهري</span>
                                            (يوم {{ schedule.day_of_month }})
                                            {% endif %}
                                        </td>
                                        <td>{{ schedule.hour }}:{{ '%02d'|format(schedule.minute) }}</td>
                                        <td>{{ schedule.warehouse.name if schedule.warehouse else 'جميع المستودعات' }}</td>
                                        <td>
                                            {% if schedule.is_active %}
                                            <span class="badge badge-success">مفعل</span>
                                            {% else %}
                                            <span class="badge badge-danger">معطل</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ schedule.next_run.strftime('%Y-%m-%d %H:%M') if schedule.next_run else 'غير محدد' }}</td>
                                        <td>
                                            <form method="POST" action="{{ url_for('reports.backup') }}" style="display: inline;">
                                                <input type="hidden" name="action" value="update_schedule">
                                                <input type="hidden" id="csrf_token" name="csrf_token" value="{{csrf_token()}}">
                                                <input type="hidden" name="schedule_id" value="{{ schedule.id }}">
                                                <input type="checkbox" name="is_active" {% if schedule.is_active %}checked{% endif %} onchange="this.form.submit()" style="display: none;">
                                                <button type="submit" class="btn btn-sm {% if schedule.is_active %}btn-warning{% else %}btn-success{% endif %}">
                                                    <i class="fas {% if schedule.is_active %}fa-pause{% else %}fa-play{% endif %}"></i>
                                                    {% if schedule.is_active %}إيقاف{% else %}تفعيل{% endif %}
                                                </button>
                                            </form>
                                            <form method="POST" action="{{ url_for('reports.backup') }}" style="display: inline;">
                                                <input type="hidden" name="action" value="delete_schedule">
                                                <input type="hidden" id="csrf_token" name="csrf_token" value="{{csrf_token()}}">
                                                <input type="hidden" name="schedule_id" value="{{ schedule.id }}">
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الجدولة؟');">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center py-3">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle"></i> لم يتم إنشاء أي جدولة للنسخ الاحتياطي حتى الآن
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup History Tab -->
    <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
        <div class="backup-table">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-history"></i> سجل النسخ الاحتياطية</h5>
                <div>
                    <button class="btn btn-sm btn-light" id="refresh-history">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>المستودع</th>
                                <th>الحجم</th>
                                <th>المستخدم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for backup in backup_records %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ backup.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    {% if backup.backup_type == 'full' %}
                                    <span class="badge badge-primary">كامل</span>
                                    {% else %}
                                    <span class="badge badge-secondary">{{ backup.backup_type }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ backup.warehouse.name if backup.warehouse else 'جميع المستودعات' }}</td>
                                <td>{{ (backup.file_size / 1024)|round(1) }} KB</td>
                                <td>{{ backup.user.username }}</td>
                                <td>
                                    <div class="d-flex justify-content-between">
                                        <form method="POST" action="{{ url_for('reports.backup') }}" class="mx-1">
                                            <input type="hidden" name="action" value="restore">
                                            <input type="hidden" name="csrf_token" value="{{csrf_token()}}">
                                            <input type="hidden" name="backup_id" value="{{ backup.id }}">
                                            <button type="submit" class="btn btn-sm btn-warning restore-btn"
                                                onclick="return confirm('هل أنت متأكد من استعادة هذه النسخة؟ سيتم استبدال البيانات الحالية!');">
                                                <i class="fas fa-undo"></i> استعادة
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ url_for('reports.backup') }}" class="mx-1">
                                            <input type="hidden" name="action" value="download">
                                            <input type="hidden" name="csrf_token" value="{{csrf_token()}}">
                                            <input type="hidden" name="backup_id" value="{{ backup.id }}">
                                            <button type="submit" class="btn btn-sm btn-info">
                                                <i class="fas fa-download"></i> تنزيل
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ url_for('reports.backup') }}" class="mx-1">
                                            <input type="hidden" name="action" value="delete_backup">
                                            <input type="hidden" name="csrf_token" value="{{csrf_token()}}">
                                            <input type="hidden" name="backup_id" value="{{ backup.id }}">
                                            <button type="submit" class="btn btn-sm btn-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذه العملية!');">
                                                <i class="fas fa-trash"></i> حذف
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center py-3">
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle"></i> لم يتم إنشاء أي نسخة احتياطية حتى الآن
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="progress backup-progress" id="restore-progress" style="display: none; margin-top: 20px;">
            <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
        </div>
    </div>

    <!-- Restore Tab -->
    <div class="tab-pane fade" id="restore" role="tabpanel" aria-labelledby="restore-tab">
        <div class="row">
            <div class="col-md-8">
                <div class="backup-table">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-undo-alt"></i> استعادة البيانات</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> اختر نسخة احتياطية من القائمة أدناه لاستعادة البيانات.
                        </div>

                        <form method="POST" action="{{ url_for('reports.backup') }}" id="restore-form">
                            <input type="hidden" name="action" value="restore">
                            <input type="hidden" id="csrf_token" name="csrf_token" value="{{csrf_token()}}">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="backup_id">اختر نسخة احتياطية</label>
                                        <select name="backup_id" id="backup_id" class="form-control" required>
                                            <option value="">اختر نسخة احتياطية...</option>
                                            {% for backup in backup_records %}
                                            <option value="{{ backup.id }}">
                                                {{ backup.timestamp.strftime('%Y-%m-%d %H:%M') }} -
                                                {% if backup.backup_type == 'full' %}كامل{% else %}{{ backup.warehouse.name }}{% endif %} -
                                                {{ (backup.file_size / 1024)|round(1) }} KB
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="confirm-restore" name="confirm_restore" required>
                                            <label class="form-check-label" for="confirm-restore">
                                                أؤكد أنني أفهم أن هذه العملية ستؤدي إلى استبدال البيانات الحالية
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>خيارات الاستعادة</label>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="restore-weapons" name="restore_weapons" checked>
                                            <label class="form-check-label" for="restore-weapons">
                                                استعادة بيانات الأسلحة والذخائر
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="restore-personnel" name="restore_personnel" checked>
                                            <label class="form-check-label" for="restore-personnel">
                                                استعادة بيانات الأفراد
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="restore-transactions" name="restore_transactions" checked>
                                            <label class="form-check-label" for="restore-transactions">
                                                استعادة بيانات المعاملات
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="restore-logs" name="restore_logs">
                                            <label class="form-check-label" for="restore-logs">
                                                استعادة سجلات النشاط
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group text-center mt-4">
                                <button type="submit" class="btn btn-warning btn-lg" id="restore-btn">
                                    <i class="fas fa-undo-alt"></i> استعادة البيانات
                                </button>
                            </div>

                            <div class="progress backup-progress" id="restore-form-progress" style="display: none;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="row">
                    <div class="col-md-12 mb-4">
                        <div class="stats-card danger" style="max-height: 180px;">
                            <div class="card-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-title">تحذير هام</div>
                            <p class="mt-2">استعادة النسخة الاحتياطية ستؤدي إلى استبدال جميع البيانات الحالية.</p>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="stats-card info" style="max-height: 180px;">
                            <div class="card-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="card-title">معلومات الاستعادة</div>
                            <p class="mt-2">يمكنك استعادة البيانات بشكل جزئي باختيار البيانات التي تريد استعادتها فقط.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/backup-dashboard.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show/hide fields based on schedule type
        const scheduleTypeSelect = document.getElementById('schedule_type');
        const dayOfWeekGroup = document.getElementById('day_of_week_group');
        const dayOfMonthGroup = document.getElementById('day_of_month_group');

        function updateScheduleFields() {
            const scheduleType = scheduleTypeSelect.value;
            dayOfWeekGroup.style.display = scheduleType === 'weekly' ? 'block' : 'none';
            dayOfMonthGroup.style.display = scheduleType === 'monthly' ? 'block' : 'none';
        }

        scheduleTypeSelect.addEventListener('change', updateScheduleFields);
        updateScheduleFields(); // Initial update

        // Show/hide warehouse field based on backup type
        const backupTypeSelect = document.getElementById('backup_type');
        const warehouseIdGroup = document.getElementById('warehouse_id_group');

        function updateBackupFields() {
            const backupType = backupTypeSelect.value;
            warehouseIdGroup.style.display = backupType === 'warehouse' ? 'block' : 'none';
        }

        backupTypeSelect.addEventListener('change', updateBackupFields);
        updateBackupFields(); // Initial update

        // تحديث سجل النسخ الاحتياطية
        const refreshHistoryBtn = document.getElementById('refresh-history');
        if (refreshHistoryBtn) {
            refreshHistoryBtn.addEventListener('click', function() {
                window.location.href = "{{ url_for('reports.backup') }}" + "?tab=history";
            });
        }

        // التبديل إلى علامة التبويب المحددة في الرابط
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');
        if (tabParam) {
            const tabEl = document.querySelector('#backupTabs a[href="#' + tabParam + '"]');
            if (tabEl) {
                tabEl.click();
            }
        }

        // عرض اسم الملف المختار في نموذج رفع الملفات
        const backupFileInput = document.getElementById('backup_file');
        const backupFileLabel = document.querySelector('.custom-file-label');

        if (backupFileInput && backupFileLabel) {
            backupFileInput.addEventListener('change', function() {
                if (this.files && this.files.length > 0) {
                    backupFileLabel.textContent = this.files[0].name;
                } else {
                    backupFileLabel.textContent = 'اختر ملف...';
                }
            });
        }
    });
</script>
{% endblock %}