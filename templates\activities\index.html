{% extends "base.html" %}

{% block title %}سجل النشاطات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>سجل النشاطات</h3>
    </div>
    <div class="col-md-4 text-right d-flex justify-content-end">
        {% if current_user.is_admin_role %}
        <button type="button" class="btn btn-danger me-2" data-bs-toggle="modal" data-bs-target="#deleteAllModal">
            <i class="fas fa-trash-alt"></i> حذف جميع السجلات
        </button>
        {% endif %}
        <a href="{{ url_for('activities.export') }}{% if request.args.get('q') %}?q={{ request.args.get('q') }}{% endif %}" class="btn btn-success me-2">
            <i class="fas fa-file-export"></i> تصدير
        </a>
        <a href="{{ url_for('warehouse.dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى لوحة التحكم
        </a>
    </div>
</div>

<!-- Modal for Delete All Confirmation -->
<div class="modal fade" id="deleteAllModal" tabindex="-1" aria-labelledby="deleteAllModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteAllModalLabel">تأكيد حذف جميع السجلات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> تحذير: سيتم حذف جميع سجلات النشاطات بشكل نهائي. هذا الإجراء لا يمكن التراجع عنه.
                </div>
                <p id="deleteConfirmText">هل أنت متأكد من رغبتك في حذف جميع سجلات النشاطات؟</p>
                <div id="recordCountInfo" class="text-muted small" style="display: none;">
                    <i class="fas fa-info-circle"></i> عدد السجلات التي سيتم حذفها: <span id="recordCount">0</span>
                </div>
                <div id="loadingInfo" class="text-muted small">
                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل معلومات السجلات...
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelDeleteBtn" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('activities.delete_all') }}" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn">تأكيد الحذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div class="me-0">
            <h5 class="mb-0"><i class="fas fa-history"></i> سجل النشاطات</h5>
        </div>
        <div>
            <form action="{{ url_for('activities.index') }}" method="GET" class="d-flex" id="searchForm">
                <input type="text" name="q" class="form-control ms-2" placeholder="بحث..."
                    value="{{ request.args.get('q', '') }}" id="searchInput" autocomplete="off">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>النشاط</th>
                        <th>الوصف</th>
                        <th>المستودع</th>
                        <th>بواسطة</th>
                        <th>التاريخ</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in activities.items %}
                    <tr>
                        <td>
                            {% if 'إضافة' in log.action %}
                            <span class="badge bg-success">{{ log.action }}</span>
                            {% elif 'تعديل' in log.action %}
                            <span class="badge bg-primary">{{ log.action }}</span>
                            {% elif 'حذف' in log.action %}
                            <span class="badge bg-danger">{{ log.action }}</span>
                            {% elif 'نقل' in log.action %}
                            <span class="badge bg-info">{{ log.action }}</span>
                            {% elif 'تسليم' in log.action %}
                            <span class="badge bg-warning">{{ log.action }}</span>
                            {% elif 'استلام' in log.action %}
                            <span class="badge bg-success">{{ log.action }}</span>
                            {% elif 'تغيير حالة' in log.action %}
                            <span class="badge bg-primary">{{ log.action }}</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ log.action }}</span>
                            {% endif %}
                        </td>
                        <td>{{ log.description }}</td>
                        <td>
                            {% if 'جهاز' in log.action or not log.warehouse %}
                            <span class="text-muted">-</span>
                            {% else %}
                            {{ log.warehouse.name }}
                            {% endif %}
                        </td>
                        <td>
                            {% if log.user %}
                            {{ log.user.full_name or log.user.username }}
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>{{ format_datetime_12h(log.timestamp) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-center mt-4 mb-4">
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    {% if activities.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('activities.index', page=activities.prev_num, q=request.args.get('q', '')) }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in activities.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                        {% if page_num %}
                            {% if page_num == activities.page %}
                            <li class="page-item active">
                                <a class="page-link" href="{{ url_for('activities.index', page=page_num, q=request.args.get('q', '')) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('activities.index', page=page_num, q=request.args.get('q', '')) }}">{{ page_num }}</a>
                            </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">...</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if activities.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('activities.index', page=activities.next_num, q=request.args.get('q', '')) }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأكد من تحميل Bootstrap بشكل صحيح
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap غير محمل بشكل صحيح');
            return;
        }
        // إصلاح مشكلة ظهور النافذة المنبثقة تلقائيًا
        // التحقق من وجود أي نوافذ منبثقة مفتوحة وإغلاقها
        const openModals = document.querySelectorAll('.modal.show');
        openModals.forEach(function(modal) {
            if (window.bootstrap && window.bootstrap.Modal) {
                const modalInstance = window.bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            } else {
                modal.classList.remove('show');
                modal.style.display = 'none';
                document.body.classList.remove('modal-open');
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) backdrop.remove();
            }
        });

        // إزالة أي إشعارات تظهر تلقائيًا عند تحميل الصفحة
        setTimeout(function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                if (notification.textContent.includes('حذف جميع سجلات النشاطات')) {
                    notification.remove();
                }
            });
        }, 100);

        // معالج لنافذة حذف جميع السجلات
        const deleteAllModal = document.getElementById('deleteAllModal');
        if (deleteAllModal) {
            let modalInstance = null;

            // إنشاء instance للنافذة
            modalInstance = new bootstrap.Modal(deleteAllModal, {
                backdrop: true,
                keyboard: true
            });

            // معالج فتح النافذة
            deleteAllModal.addEventListener('show.bs.modal', function() {
                resetModalState();
                loadRecordCount();
            });

            // معالج إغلاق النافذة
            deleteAllModal.addEventListener('hide.bs.modal', function() {
                resetModalState();
            });

            // معالج إضافي للتأكد من إغلاق النافذة
            deleteAllModal.addEventListener('hidden.bs.modal', function() {
                resetModalState();
                // إزالة أي backdrop متبقي
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
                // إزالة class modal-open من body
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            });

            // معالج زر الإلغاء
            const cancelBtn = document.getElementById('cancelDeleteBtn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                });
            }

            // دالة إعادة تعيين حالة النافذة
            function resetModalState() {
                const loadingInfo = document.getElementById('loadingInfo');
                const recordCountInfo = document.getElementById('recordCountInfo');
                const confirmButton = document.getElementById('confirmDeleteBtn');
                const confirmText = document.getElementById('deleteConfirmText');

                if (loadingInfo) loadingInfo.style.display = 'none';
                if (recordCountInfo) recordCountInfo.style.display = 'none';
                if (confirmButton) confirmButton.style.display = 'none';
                if (confirmText) confirmText.textContent = 'هل أنت متأكد من رغبتك في حذف جميع سجلات النشاطات؟';
            }

            // دالة تحميل عدد السجلات
            function loadRecordCount() {
                const loadingInfo = document.getElementById('loadingInfo');
                const recordCountInfo = document.getElementById('recordCountInfo');
                const confirmButton = document.getElementById('confirmDeleteBtn');

                if (loadingInfo) loadingInfo.style.display = 'block';

                // جلب عدد السجلات عند فتح النافذة
                fetch('{{ url_for("activities.count") }}')
                    .then(response => response.json())
                    .then(data => {
                        // إخفاء رسالة التحميل
                        if (loadingInfo) loadingInfo.style.display = 'none';

                        if (data.count !== undefined) {
                            const recordCountElement = document.getElementById('recordCount');
                            const confirmText = document.getElementById('deleteConfirmText');

                            if (recordCountElement && recordCountInfo && confirmText) {
                                recordCountElement.textContent = data.count;
                                recordCountInfo.style.display = 'block';

                                if (data.count === 0) {
                                    confirmText.textContent = 'لا توجد سجلات للحذف.';
                                    // إخفاء زر التأكيد إذا لم توجد سجلات
                                    if (confirmButton) {
                                        confirmButton.style.display = 'none';
                                    }
                                } else {
                                    confirmText.textContent = `هل أنت متأكد من رغبتك في حذف جميع سجلات النشاطات (${data.count} سجل)؟`;
                                    // إظهار زر التأكيد
                                    if (confirmButton) {
                                        confirmButton.style.display = 'inline-block';
                                    }
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في جلب عدد السجلات:', error);
                        // إخفاء رسالة التحميل وإظهار رسالة خطأ
                        if (loadingInfo) loadingInfo.style.display = 'none';
                        const confirmText = document.getElementById('deleteConfirmText');
                        if (confirmText) {
                            confirmText.textContent = 'حدث خطأ في تحميل معلومات السجلات. هل تريد المتابعة؟';
                        }
                        if (confirmButton) {
                            confirmButton.style.display = 'inline-block';
                        }
                    });
            }
        }

        // معالج إضافي للتأكد من عدم تجمد الصفحة
        document.addEventListener('click', function(e) {
            // إذا تم النقر خارج النافذة، تأكد من إغلاقها
            if (e.target.classList.contains('modal-backdrop')) {
                const openModals = document.querySelectorAll('.modal.show');
                openModals.forEach(modal => {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                });
            }
        });

        // معالج مفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const openModals = document.querySelectorAll('.modal.show');
                openModals.forEach(modal => {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                });
            }
        });

        const searchInput = document.getElementById('searchInput');
        const searchForm = document.getElementById('searchForm');

        if (searchInput && searchForm) {
            // تعيين التركيز على حقل البحث عند تحميل الصفحة ووضع المؤشر في نهاية النص
            searchInput.focus();
            // وضع المؤشر في نهاية النص
            const inputValue = searchInput.value;
            searchInput.value = '';
            searchInput.value = inputValue;

            // متغير لتخزين الوقت المستغرق بين الإدخالات
            let typingTimer;
            // الفترة الزمنية بالمللي ثانية قبل إرسال النموذج (300 مللي ثانية = 0.3 ثانية)
            const doneTypingInterval = 300;

            // عند الكتابة في حقل البحث
            searchInput.addEventListener('input', function() {
                // إعادة ضبط المؤقت في كل مرة يتم فيها الكتابة
                clearTimeout(typingTimer);

                // إذا كان الحقل غير فارغ، ابدأ المؤقت
                if (searchInput.value) {
                    typingTimer = setTimeout(submitForm, doneTypingInterval);
                }
            });

            // وظيفة إرسال النموذج
            function submitForm() {
                searchForm.submit();
            }

            // عند مسح الحقل بالكامل، قم بإرسال النموذج أيضًا للعودة إلى القائمة الكاملة
            searchInput.addEventListener('keyup', function(e) {
                if (searchInput.value === '' && e.key === 'Backspace') {
                    submitForm();
                }
            });
        }
    });
</script>
{% endblock %}
