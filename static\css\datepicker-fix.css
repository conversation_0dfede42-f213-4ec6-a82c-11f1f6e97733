/* إصلاح موضع منتقي التاريخ (datepicker) - تحديث شامل */

/* إعادة تعريف كاملة لمنتقي التاريخ لضمان عدم تأثره بأي قواعد أخرى */
.datepicker-dropdown {
    position: fixed !important;
    z-index: 99999 !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    transform: none !important;
    margin: 0 !important;
    padding: 4px !important;
    border-radius: 4px !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2) !important;
    max-width: 300px !important;
    width: auto !important;
}

/* تأكد من أن منتقي التاريخ يظهر فوق جميع العناصر الأخرى */
.datepicker {
    z-index: 99999 !important;
}

/* إصلاح موضع منتقي التاريخ بالنسبة للحقل */
.datepicker-dropdown.datepicker-orient-bottom:before,
.datepicker-dropdown.datepicker-orient-bottom:after {
    top: -7px !important;
    bottom: auto !important;
}

.datepicker-dropdown.datepicker-orient-top:before,
.datepicker-dropdown.datepicker-orient-top:after {
    bottom: -7px !important;
    top: auto !important;
}

/* تأكد من أن منتقي التاريخ يظهر بشكل صحيح في الوضع RTL */
.datepicker-rtl .datepicker-dropdown {
    right: auto !important;
}

/* تأكد من أن منتقي التاريخ لا يتأثر بالقواعد الأخرى */
.dropdown-menu.datepicker-dropdown {
    display: block !important;
    position: fixed !important;
    inset: auto !important;
    transform: none !important;
    margin: 0 !important;
}

/* تأكد من أن منتقي التاريخ يظهر بشكل صحيح في الوضع المظلم */
body .datepicker-dropdown {
    background-color: var(--bg-secondary, #242424) !important;
    color: var(--text-primary, #ffffff) !important;
    border: 1px solid var(--border-color, #404040) !important;
}

/* تأكد من أن منتقي التاريخ يظهر بشكل صحيح في الوضع النهاري */
body.light-theme .datepicker-dropdown {
    background-color: white !important;
    color: #212529 !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
}

/* إصلاح خاص لصفحة إضافة جهاز جديد */
body .datepicker-dropdown.datepicker-position-fixed {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* تأكد من أن الجدول داخل منتقي التاريخ يظهر بشكل صحيح */
.datepicker table {
    width: 100% !important;
    margin: 0 !important;
}

/* تأكد من أن خلايا الجدول داخل منتقي التاريخ تظهر بشكل صحيح */
.datepicker td, .datepicker th {
    text-align: center !important;
    width: 30px !important;
    height: 30px !important;
    border-radius: 4px !important;
    border: none !important;
    padding: 5px !important;
}

/* تحسين مظهر الأزرار داخل منتقي التاريخ */
.datepicker .datepicker-switch,
.datepicker .prev,
.datepicker .next,
.datepicker tfoot tr th {
    cursor: pointer !important;
    padding: 5px 10px !important;
}

/* تحسين مظهر الأيام داخل منتقي التاريخ */
.datepicker table tr td.day {
    padding: 5px !important;
    cursor: pointer !important;
}

/* تحسين مظهر اليوم المحدد داخل منتقي التاريخ */
.datepicker table tr td.active {
    background-color: var(--accent-color, #0d6efd) !important;
    color: white !important;
}

/* تأكد من أن منتقي التاريخ يظهر بشكل صحيح عند تفعيله */
.datepicker.datepicker-dropdown.dropdown-menu.show {
    display: block !important;
}
