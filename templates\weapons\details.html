{% extends 'base.html' %}

{% block title %}تفاصيل السلاح - {{ weapon.name }}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h3 class="mb-0">تفاصيل السلاح</h3>
    <div class="weapon-actions">
        <a href="{{ url_for('weapons.edit', weapon_id=weapon.id) }}" class="btn btn-sm btn-outline-primary weapon-btn">
            <i class="fas fa-edit"></i> تعديل
        </a>

        {% if current_user.is_admin %}
        <form method="POST" action="{{ url_for('weapons.delete', weapon_id=weapon.id) }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
            <button type="submit" class="btn btn-sm btn-outline-danger weapon-btn">
                <i class="fas fa-trash"></i> حذف
            </button>
        </form>
        {% else %}
        <button class="btn btn-sm btn-outline-secondary weapon-btn" disabled>
            <i class="fas fa-trash"></i> حذف
        </button>
        {% endif %}

        <div class="dropdown">
            <button type="button" class="btn btn-sm btn-outline-info dropdown-toggle weapon-btn" data-bs-toggle="dropdown"
                aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-qrcode"></i> الباركود / QR
            </button>
            <div class="dropdown-menu dropdown-menu-end">
                <a class="dropdown-item" href="{{ url_for('weapons.get_qr_code', weapon_id=weapon.id) }}">QR Code</a>
                <a class="dropdown-item" href="{{ url_for('weapons.barcode', weapon_id=weapon.id) }}">Barcode</a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item"
                    href="{{ url_for('weapons.get_qr_code', weapon_id=weapon.id, download=1) }}">تنزيل QR Code</a>
                <a class="dropdown-item" href="{{ url_for('weapons.barcode', weapon_id=weapon.id, download=1) }}">تنزيل
                    الباركود</a>
            </div>
        </div>

        <div class="dropdown">
            <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle weapon-btn" data-bs-toggle="dropdown"
                aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-tasks"></i> إدارة
            </button>
            <div class="dropdown-menu dropdown-menu-end">
                <button class="dropdown-item maintenance-modal-link"
                    onclick="$('#checkoutModal').modal('show'); return false;">تسليم السلاح</button>
                <button class="dropdown-item maintenance-modal-link"
                    onclick="$('#transferModal').modal('show'); return false;">نقل السلاح</button>
                <button class="dropdown-item maintenance-modal-link"
                    onclick="$('#maintenanceModal').modal('show'); return false;">إضافة سجل صيانة</button>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item"
                    href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='نشط') }}">تعيين كنشط</a>
                <a class="dropdown-item"
                    href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='إجازة') }}">تعيين في
                    إجازة</a>
                <a class="dropdown-item"
                    href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='مهمة') }}">تعيين في مهمة</a>
                <a class="dropdown-item"
                    href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='صيانة') }}">تعيين في
                    صيانة</a>
                <a class="dropdown-item"
                    href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='تالف') }}">تعيين كتالف</a>
                <a class="dropdown-item"
                    href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='مستلم') }}">تعيين كمستلم</a>
                <a class="dropdown-item"
                    href="{{ url_for('weapons.update_status', weapon_id=weapon.id, status='رماية') }}">تعيين في رماية</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card shadow-sm border-0">
            <div class="card-body p-0">
                <div class="row no-gutters">
                    <div class="col-md-8">
                        <div class="p-4">
                            <div class="d-flex justify-content-between mb-3">
                                <h4 class="card-title mb-0">{{ weapon.name }}</h4>
                                <span
                                    class="badge badge-{{ weapon.status|status_color }} badge rounded-pill px-3 py-2">{{
                                    weapon.status }}</span>
                            </div>
                            <table class="table table-details">
                                <tbody>
                                    <tr>
                                        <th>اسم السلاح:</th>
                                        <td>{{ weapon.name }}</td>
                                    </tr>
                                    <tr>
                                        <th>رقم الحفظ:</th>
                                        <td>{{ weapon.weapon_number or 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <th>الرقم التسلسلي:</th>
                                        <td>{{ weapon.serial_number }}</td>
                                    </tr>
                                    <tr>
                                        <th>رقم السلاح:</th>
                                        <td>{% if weapon.type == 'pistol' %}مسدس{% elif weapon.type == 'rifle' %}بندقية{% elif weapon.type == 'sniper' %}قناص{% elif weapon.type == 'machine_gun' %}رشاش{% else %}{{ weapon.type }}{% endif %}</td>
                                    </tr>
                                    <tr>
                                        <th>المستودع:</th>
                                        <td>{{ weapon.warehouse.name }}</td>
                                    </tr>
                                    <tr>
                                        <th>الحالة الفنية:</th>
                                        <td>{{ weapon.condition }}</td>
                                    </tr>
                                    {% if weapon.weapon_document %}
                                    <tr>
                                        <th>سند السلاح:</th>
                                        <td>
                                            <a href="{{ url_for('weapons.download_document', weapon_id=weapon.id) }}"
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-file-pdf"></i> عرض/تحميل سند السلاح
                                            </a>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% if weapon.notes %}
                                    <tr>
                                        <th>ملاحظات:</th>
                                        <td>{{ weapon.notes }}</td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <th>تاريخ الإضافة:</th>
                                        <td>{{ weapon.created_at | format_saudi_time }}</td>
                                    </tr>
                                    <tr>
                                        <th>آخر تحديث:</th>
                                        <td>{{ weapon.updated_at | format_saudi_time }}</td>
                                    </tr>

                                    {% if weapon.personnel %}
                                    <tr>
                                        <th>الفرد المرتبط:</th>
                                        <td>
                                            {% for person in weapon.personnel %}
                                            <div class="d-flex align-items-center mb-1">
                                                <a href="{{ url_for('personnel.details', personnel_id=person.id) }}" class="text-decoration-none">
                                                    <strong>{{ person.name }}</strong> ({{ person.personnel_id }})
                                                </a>
                                                <span class="badge badge-{{ person.status|status_color }} ms-2">{{ person.status }}</span>
                                            </div>
                                            {% endfor %}
                                        </td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4 bg-themed d-flex align-items-center justify-content-center">
                        <div class="text-center p-4">
                            <div class="d-flex flex-column align-items-center">
                                <!-- QR Code -->
                                <div class="mb-3">
                                    <h6 class="text-muted mb-2">QR Code</h6>
                                    <div id="qrcode" class="mx-auto"></div>
                                </div>

                                <p class="qr-code-serial" style="color: #000000; background-color: #f8f9fa; padding: 5px; border-radius: 5px; display: inline-block;">{{ weapon.serial_number }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction and Maintenance Cards in Tabs -->
    <div class="col-md-12 mb-4">
        <ul class="nav nav-tabs" id="weaponDetailsTabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link active show" id="transactions-tab" data-bs-toggle="tab" href="#transactions"
                    role="tab" aria-controls="transactions" aria-selected="true">
                    <i class="fas fa-exchange-alt me-1"></i> سجل المعاملات
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="maintenance-tab" data-bs-toggle="tab" href="#maintenance" role="tab"
                    aria-controls="maintenance" aria-selected="false">
                    <i class="fas fa-tools me-1"></i> سجل الصيانة
                </a>
            </li>
        </ul>
        <div class="tab-content p-3 border border-top-0 bg-white rounded-bottom" id="weaponDetailsTabsContent">
            <!-- Transactions Tab -->
            <div class="tab-pane fade show active" id="transactions" role="tabpanel" aria-labelledby="transactions-tab">
                {% if transactions %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الشخص</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>بواسطة</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span
                                        class="badge badge rounded-pill {% if transaction.transaction_type == 'checkout' %}badge-warning{% elif transaction.transaction_type == 'return' %}badge-success{% else %}badge-info{% endif %}">
                                        {% if transaction.transaction_type == 'checkout' %}
                                        صرف
                                        {% elif transaction.transaction_type == 'return' %}
                                        إعادة
                                        {% elif transaction.transaction_type == 'transfer' %}
                                        نقل
                                        {% endif %}
                                    </span>
                                </td>
                                <td>{{ transaction.personnel.name }}</td>
                                <td>{{ transaction.source_warehouse.name }}</td>
                                <td>{{ transaction.target_warehouse.name if transaction.target_warehouse else 'غير محدد'
                                    }}</td>
                                <td>{{ transaction.user.username }}</td>
                                <td>
                                    {% if transaction.transaction_type == 'checkout' and current_user.is_admin %}
                                    <button type="button" class="btn btn-sm btn-outline-primary maintenance-modal-link"
                                        onclick="$('#returnModal{{ transaction.id }}').modal('show'); return false;">
                                        استلام
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i> لا توجد معاملات لهذا السلاح.
                </div>
                {% endif %}
            </div>

            <!-- Maintenance Tab -->
            <div class="tab-pane fade" id="maintenance" role="tabpanel" aria-labelledby="maintenance-tab">
                {% if maintenance_records %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الحالة</th>
                                <th>الوصف</th>
                                <th>بواسطة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in maintenance_records %}
                            <tr>
                                <td>{{ record.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ translate_maintenance_type(record.maintenance_type) }}</td>
                                <td>
                                    <span
                                        class="badge badge rounded-pill {% if record.status == 'ongoing' %}badge-warning{% elif record.status == 'completed' %}badge-success{% else %}badge-danger{% endif %}">
                                        {{ translate_maintenance_status(record.status) }}
                                    </span>
                                </td>
                                <td>{{ record.description }}</td>
                                <td>{{ record.user.username }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i> لا توجد سجلات صيانة لهذا السلاح.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Checkout Modal -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="checkoutModal" tabindex="-1"
    aria-labelledby="checkoutModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('weapons.checkout', weapon_id=weapon.id) }}">
                {{ checkout_form.hidden_tag() }}
                <div class="modal-header">
                    <h5 class="modal-title" id="checkoutModalLabel">تسليم السلاح</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="personnel_id" class="form-label">{{ checkout_form.personnel_id.label }}</label>
                        {{ checkout_form.personnel_id(class="form-control") }}
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">{{ checkout_form.notes.label }}</label>
                        {{ checkout_form.notes(class="form-control") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ checkout_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="transferModal" tabindex="-1"
    aria-labelledby="transferModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('weapons.transfer', weapon_id=weapon.id) }}">
                {{ transfer_form.hidden_tag() }}
                <div class="modal-header">
                    <h5 class="modal-title" id="transferModalLabel">نقل السلاح</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="target_warehouse_id" class="form-label">{{ transfer_form.target_warehouse_id.label
                            }}</label>
                        {{ transfer_form.target_warehouse_id(class="form-control") }}
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">{{ transfer_form.notes.label }}</label>
                        {{ transfer_form.notes(class="form-control") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ transfer_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Maintenance Modal -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="maintenanceModal" tabindex="-1"
    aria-labelledby="maintenanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('weapons.add_maintenance', weapon_id=weapon.id) }}">
                {{ maintenance_form.hidden_tag() }}
                <div class="modal-header">
                    <h5 class="modal-title" id="maintenanceModalLabel">إضافة سجل صيانة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="maintenance_type" class="form-label">{{ maintenance_form.maintenance_type.label
                            }}</label>
                        {{ maintenance_form.maintenance_type(class="form-control") }}
                    </div>
                    <div class="form-group">
                        <label for="description" class="form-label">{{ maintenance_form.description.label }}</label>
                        {{ maintenance_form.description(class="form-control", rows="3") }}
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="start_date" class="form-label">{{ maintenance_form.start_date.label
                                    }}</label>
                                {{ maintenance_form.start_date(class="form-control datepicker") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="end_date" class="form-label">{{ maintenance_form.end_date.label }}</label>
                                {{ maintenance_form.end_date(class="form-control datepicker") }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status" class="form-label">{{ maintenance_form.status.label }}</label>
                                {{ maintenance_form.status(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="cost" class="form-label">{{ maintenance_form.cost.label }}</label>
                                {{ maintenance_form.cost(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">{{ maintenance_form.notes.label }}</label>
                        {{ maintenance_form.notes(class="form-control", rows="3") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ maintenance_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Return Modals for each checkout transaction -->
{% for transaction in transactions %}
{% if transaction.transaction_type == 'checkout' %}
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="returnModal{{ transaction.id }}"
    tabindex="-1" aria-labelledby="returnModalLabel{{ transaction.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST"
                action="{{ url_for('weapons.return_weapon', weapon_id=weapon.id, transaction_id=transaction.id) }}">
                {{ return_form.hidden_tag() }}
                <input type="hidden" name="transaction_id" value="{{ transaction.id }}">
                <div class="modal-header">
                    <h5 class="modal-title" id="returnModalLabel{{ transaction.id }}">استلام السلاح من {{
                        transaction.personnel.name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="condition" class="form-label">{{ return_form.condition.label }}</label>
                        {{ return_form.condition(class="form-control") }}
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">{{ return_form.notes.label }}</label>
                        {{ return_form.notes(class="form-control", rows="3") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ return_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}



<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Generate QR code with error handling
        try {
            var qrContainer = document.getElementById("qrcode");
            if (!qrContainer) {
                console.error('QR container not found');
                return;
            }

            // Check if QRCode library is available
            if (typeof QRCode === 'undefined') {
                console.error('QRCode library not loaded');
                qrContainer.innerHTML = '<div class="alert alert-warning">مكتبة QR Code غير متوفرة</div>';
                return;
            }

            // Clear any existing content
            qrContainer.innerHTML = '';

            // Generate QR code with full serial number using optimized settings
            var qrData = "{{ weapon.serial_number }}";
            console.log('Generating QR code with data:', qrData);

            var qrcode = new QRCode(qrContainer, {
                text: qrData,
                width: 120,
                height: 120,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L,  // Lowest error correction for maximum data
                typeNumber: 4  // Use version 4 which supports more data
            });

            console.log('✅ QR code generated successfully');

        } catch (error) {
            console.error('❌ Error generating QR code:', error);
            var qrContainer = document.getElementById("qrcode");
            if (qrContainer) {
                qrContainer.innerHTML = '<div class="alert alert-danger">خطأ في إنشاء QR Code</div>';
            }
        }
    });
</script>
{% endblock %}