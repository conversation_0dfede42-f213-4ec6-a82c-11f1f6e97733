// Bootstrap bundle with Popper.js
// This script just redirects to the local version - actual file is loaded from local storage

// Updated to use local Bootstrap bundle
if (typeof window.bootstrap === "undefined") {
  console.log("Loading Bootstrap bundle from local file...");
  const script = document.createElement("script");
  script.src = "static/js/bootstrap.bundle.min.js";
  document.head.appendChild(script);
}
