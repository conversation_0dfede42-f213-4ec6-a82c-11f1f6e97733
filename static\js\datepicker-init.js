document.addEventListener('DOMContentLoaded', function() {
    // تهيئة حقول التاريخ - تحديث شامل
    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        language: 'ar',
        rtl: true,
        clearBtn: true,
        todayBtn: 'linked',
        todayHighlight: true,
        zIndexOffset: 99999,
        container: 'body',
        orientation: 'auto',
        templates: {
            leftArrow: '<i class="fas fa-chevron-right"></i>',
            rightArrow: '<i class="fas fa-chevron-left"></i>'
        }
    });

    // استبدال حقول التاريخ العادية بمنتقي التاريخ
    document.querySelectorAll('input.datepicker').forEach(function(input) {
        // إعداد الحقل كمنتقي تاريخ
        input.setAttribute('autocomplete', 'off');

        // إضافة أيقونة التقويم
        const parent = input.parentElement;
        if (parent && !parent.querySelector('.input-group-append')) {
            // إنشاء مجموعة الإدخال إذا لم تكن موجودة
            if (!parent.classList.contains('input-group')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'input-group';
                wrapper.style.position = 'relative'; // إضافة موضع نسبي للحاوية
                input.insertAdjacentElement('beforebegin', wrapper);
                wrapper.appendChild(input);

                // إضافة أيقونة التقويم
                const appendSpan = document.createElement('div');
                appendSpan.className = 'input-group-append';
                appendSpan.innerHTML = `
                    <span class="input-group-text">
                        <i class="fas fa-calendar-alt"></i>
                    </span>
                `;
                wrapper.appendChild(appendSpan);

                // فتح منتقي التاريخ عند النقر على الأيقونة
                appendSpan.addEventListener('click', function(event) {
                    event.preventDefault();
                    event.stopPropagation();

                    // إغلاق أي منتقي تاريخ مفتوح
                    $('.datepicker').datepicker('hide');

                    // فتح منتقي التاريخ لهذا الحقل
                    setTimeout(function() {
                        $(input).datepicker('show');
                    }, 50);
                });
            }
        }
    });

    // إصلاح موضع منتقي التاريخ عند فتحه - تحديث شامل
    $('.datepicker').on('show', function(e) {
        const input = this;

        // تأخير قصير للتأكد من أن منتقي التاريخ قد تم إنشاؤه
        setTimeout(function() {
            const datepickerDropdown = document.querySelector('.datepicker-dropdown');
            if (!datepickerDropdown) return;

            // إضافة فئة خاصة لتحديد موضع منتقي التاريخ
            datepickerDropdown.classList.add('datepicker-position-fixed');

            // تعيين z-index عالي جدًا
            datepickerDropdown.style.zIndex = '99999';

            // الحصول على موضع الحقل
            const inputRect = input.getBoundingClientRect();

            // تحديد موضع منتقي التاريخ بناءً على موضع الحقل
            // نحاول وضعه فوق الحقل مباشرة
            const isDevicePage = window.location.href.includes('create_device') ||
                                window.location.href.includes('edit_device') ||
                                window.location.href.includes('devices/create') ||
                                window.location.href.includes('devices/edit');

            if (isDevicePage) {
                // في صفحات الأجهزة، نضع منتقي التاريخ في وسط الشاشة
                datepickerDropdown.style.position = 'fixed';
                datepickerDropdown.style.top = '50%';
                datepickerDropdown.style.left = '50%';
                datepickerDropdown.style.transform = 'translate(-50%, -50%)';
            } else {
                // في الصفحات الأخرى، نضع منتقي التاريخ بالقرب من الحقل
                datepickerDropdown.style.position = 'fixed';
                datepickerDropdown.style.top = (inputRect.bottom + window.scrollY + 5) + 'px';
                datepickerDropdown.style.left = (inputRect.left + window.scrollX) + 'px';
                datepickerDropdown.style.transform = 'none';
            }

            // التأكد من أن منتقي التاريخ مرئي
            datepickerDropdown.style.display = 'block';
        }, 50);
    });

    // إغلاق منتقي التاريخ عند النقر خارجه
    document.addEventListener('click', function(event) {
        const datepickerDropdown = document.querySelector('.datepicker-dropdown');
        if (!datepickerDropdown) return;

        // التحقق مما إذا كان النقر خارج منتقي التاريخ وخارج حقول التاريخ
        const isDatepickerClick = datepickerDropdown.contains(event.target);
        const isDatepickerInputClick = Array.from(document.querySelectorAll('.datepicker')).some(input =>
            input.contains(event.target) ||
            (input.parentElement && input.parentElement.contains(event.target))
        );

        if (!isDatepickerClick && !isDatepickerInputClick) {
            $('.datepicker').datepicker('hide');
        }
    });
});
