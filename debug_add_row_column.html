<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة إضافة الصفوف والأعمدة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .debug-section {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(40, 167, 69, 0.8); 
            border-left-color: #28a745;
        }
        .error { 
            background: rgba(220, 53, 69, 0.8); 
            border-left-color: #dc3545;
        }
        .warning { 
            background: rgba(255, 193, 7, 0.8); 
            color: #000; 
            border-left-color: #ffc107;
        }
        .info { 
            background: rgba(23, 162, 184, 0.8); 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #dc3545, #6f42c1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        .success-btn {
            background: linear-gradient(45deg, #28a745, #218838);
        }
        .log {
            background: rgba(0,0,0,0.6);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .debug-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .debug-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .emoji {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .code-block {
            background: rgba(0,0,0,0.4);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            border-left: 3px solid #dc3545;
        }
        .step {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 3px solid #6f42c1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة فقدان البيانات</h1>
        <p>أداة تشخيص متقدمة لمراقبة ما يحدث عند إضافة الصفوف والأعمدة</p>
        
        <div class="debug-grid">
            <div class="debug-card">
                <div class="emoji">🚨</div>
                <h3>المشكلة الحالية</h3>
                <p>فقدان البيانات عند إضافة صف أو عمود جديد</p>
            </div>
            <div class="debug-card">
                <div class="emoji">🔧</div>
                <h3>الحل المطبق</h3>
                <p>نظام حفظ واستعادة البيانات</p>
            </div>
            <div class="debug-card">
                <div class="emoji">📊</div>
                <h3>المراقبة</h3>
                <p>تسجيل مفصل لجميع العمليات</p>
            </div>
            <div class="debug-card">
                <div class="emoji">🎯</div>
                <h3>الهدف</h3>
                <p>الحفاظ على البيانات 100%</p>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>🔬 خطوات التشخيص</h3>
            <div class="step">
                <strong>الخطوة 1:</strong> افتح صفحة كشف الواجبات في تبويب جديد
            </div>
            <div class="step">
                <strong>الخطوة 2:</strong> املأ بعض البيانات (مواقع، أفراد، ملاحظات)
            </div>
            <div class="step">
                <strong>الخطوة 3:</strong> افتح وحدة التحكم في المتصفح (F12)
            </div>
            <div class="step">
                <strong>الخطوة 4:</strong> اضغط على "إضافة صف" أو "إضافة عمود"
            </div>
            <div class="step">
                <strong>الخطوة 5:</strong> راقب الرسائل في وحدة التحكم
            </div>
        </div>
        
        <div class="debug-section">
            <h3>📋 الرسائل المتوقعة في وحدة التحكم</h3>
            <div class="code-block">
🔧 [DEBUG] 💾 بدء حفظ البيانات الحالية مؤقتاً...
📊 البيانات: {dutyRows: 6, patrolRows: 2, shiftsRows: 2}
🔧 [DEBUG] 📍 حفظ موقع للصف 0: البوابة الرئيسية
🔧 [DEBUG] 👤 حفظ فرد للصف 0, العمود 0: أحمد محمد
🔄 إضافة صف جديد...
🔧 [DEBUG] 🔄 استعادة البيانات المحفوظة...
✅ تم استعادة بيانات الجدول الرئيسي من المصفوفة
🔧 [DEBUG] 🔄 استعادة بيانات DOM...
📍 استعادة موقع للصف 0: البوابة الرئيسية
👤 استعادة فرد للصف 0, العمود 0: أحمد محمد
✅ تم إضافة الصف مع الحفاظ على البيانات
            </div>
        </div>
        
        <div class="debug-section">
            <h3>🚨 علامات المشكلة</h3>
            <div class="code-block">
❌ إذا رأيت هذه الرسائل، فهناك مشكلة:
- "⚠️ لا توجد بيانات مؤقتة للاستعادة"
- "❌ فشل في استعادة البيانات"
- عدم ظهور رسائل الحفظ والاستعادة
- اختفاء البيانات رغم الرسائل الإيجابية
            </div>
        </div>
        
        <div class="debug-section">
            <h3>🔧 أدوات التشخيص</h3>
            <div>
                <button onclick="openDuties()">📋 فتح كشف الواجبات</button>
                <button onclick="openConsole()">🔍 فتح وحدة التحكم</button>
                <button onclick="showDebugInfo()">📊 عرض معلومات التشخيص</button>
                <button onclick="testDataPersistence()">🧪 اختبار ثبات البيانات</button>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>📊 نتائج التشخيص</h3>
            <div id="debugStatus">
                <div class="status info">⏳ جاري انتظار بدء التشخيص...</div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>📋 سجل التشخيص</h3>
            <div id="debugLog" class="log">
                جاري تحميل أدوات التشخيص...
            </div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
            <button onclick="exportLog()">📤 تصدير السجل</button>
        </div>
        
        <div class="debug-section">
            <h3>🔍 تحليل المشكلة</h3>
            <div class="code-block">
<strong>السبب المحتمل:</strong>
1. التوقيت - قد تكون دوال الاستعادة تعمل قبل اكتمال إنشاء الجدول
2. مراجع DOM - قد تكون المراجع للعناصر تتغير بعد إعادة الإنشاء
3. البيانات المحفوظة - قد لا تُحفظ البيانات بالشكل الصحيح
4. تضارب العمليات - قد تتداخل عمليات الحفظ والاستعادة

<strong>الحلول المقترحة:</strong>
1. زيادة فترات التأخير
2. تحسين آلية حفظ البيانات
3. إضافة فحوصات للتأكد من اكتمال العمليات
4. استخدام MutationObserver لمراقبة تغييرات DOM
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('debugLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = `<div style="color: ${colors[type]}; margin: 3px 0; padding: 8px; border-left: 3px solid ${colors[type]}; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += entry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('debugStatus');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function openDuties() {
            window.open('/duties/', '_blank');
            log('🔗 تم فتح صفحة كشف الواجبات في تبويب جديد', 'info');
            updateStatus('تم فتح صفحة كشف الواجبات - ابدأ الاختبار', 'info');
        }
        
        function openConsole() {
            log('🔍 تعليمات فتح وحدة التحكم:', 'info');
            log('- اضغط F12 أو Ctrl+Shift+I', 'info');
            log('- أو انقر بالزر الأيمن واختر "فحص العنصر"', 'info');
            log('- ثم اذهب إلى تبويب "Console"', 'info');
            updateStatus('راجع التعليمات لفتح وحدة التحكم', 'warning');
        }
        
        function showDebugInfo() {
            log('📊 معلومات التشخيص:', 'info');
            log('- المتصفح: ' + navigator.userAgent, 'info');
            log('- الوقت: ' + new Date().toLocaleString('ar-SA'), 'info');
            log('- حالة localStorage: ' + (localStorage ? 'متاح' : 'غير متاح'), 'info');
            log('- حالة console: ' + (console ? 'متاح' : 'غير متاح'), 'info');
            updateStatus('تم عرض معلومات التشخيص', 'success');
        }
        
        function testDataPersistence() {
            log('🧪 اختبار ثبات البيانات...', 'info');
            
            // محاكاة حفظ واستعادة البيانات
            const testData = {
                location: 'البوابة الرئيسية',
                personnel: 'أحمد محمد',
                notes: 'ملاحظة تجريبية'
            };
            
            // حفظ
            localStorage.setItem('testData', JSON.stringify(testData));
            log('💾 تم حفظ البيانات التجريبية', 'success');
            
            // استعادة
            setTimeout(() => {
                const retrieved = JSON.parse(localStorage.getItem('testData'));
                if (retrieved && retrieved.location === testData.location) {
                    log('✅ نجح اختبار ثبات البيانات', 'success');
                    updateStatus('اختبار ثبات البيانات نجح - المشكلة في مكان آخر', 'success');
                } else {
                    log('❌ فشل اختبار ثبات البيانات', 'error');
                    updateStatus('فشل اختبار ثبات البيانات - مشكلة في التخزين', 'error');
                }
                localStorage.removeItem('testData');
            }, 100);
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
            log('🧹 تم مسح السجل', 'info');
        }
        
        function exportLog() {
            const logContent = document.getElementById('debugLog').innerText;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'debug-log-' + new Date().toISOString().slice(0, 19) + '.txt';
            a.click();
            URL.revokeObjectURL(url);
            log('📤 تم تصدير السجل', 'success');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل أداة التشخيص', 'success');
            updateStatus('أداة التشخيص جاهزة - ابدأ الاختبار', 'info');
            
            log('📋 خطوات التشخيص:', 'info');
            log('1. افتح صفحة كشف الواجبات', 'info');
            log('2. املأ بعض البيانات', 'info');
            log('3. افتح وحدة التحكم (F12)', 'info');
            log('4. اضغط إضافة صف/عمود', 'info');
            log('5. راقب الرسائل في وحدة التحكم', 'info');
            
            // اختبار تلقائي للنظام
            setTimeout(() => {
                showDebugInfo();
                setTimeout(() => {
                    testDataPersistence();
                }, 1000);
            }, 2000);
        });
        
        // مراقبة رسائل وحدة التحكم (إذا كان ممكناً)
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args[0] && typeof args[0] === 'string' && args[0].includes('DEBUG')) {
                log('🔧 ' + args.join(' '), 'info');
            }
        };
        
        const originalError = console.error;
        console.error = function(...args) {
            originalError.apply(console, args);
            log('❌ خطأ: ' + args.join(' '), 'error');
        };
    </script>
</body>
</html>
