{% extends 'base.html' %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card bg-dark text-white">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4>تعديل جهاز: {{ device.name }}</h4>
                    <a href="{{ url_for('inventory.device_details', device_id=device.id) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i> العودة للتفاصيل
                    </a>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        {{ form.hidden_tag() }}
                        <!-- بيانات أساسية -->
                        <h5 class="mb-3"><i class="fas fa-info-circle"></i> معلومات الجهاز الأساسية</h5>
                        <div class="row">
                            <div class="col-md-6 form-group">
                                {{ form.name.label }}
                                {{ form.name(class="form-control", placeholder="اسم الجهاز") }}
                                {% if form.name.errors %}
                                <div class="text-danger">
                                    {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 form-group">
                                {{ form.type.label }}
                                {{ form.type(class="form-control") }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 form-group">
                                {{ form.model.label }}
                                {{ form.model(class="form-control", placeholder="الموديل (اختياري)") }}
                            </div>
                            <div class="col-md-6 form-group">
                                {{ form.manufacturer.label }}
                                {{ form.manufacturer(class="form-control", placeholder="الشركة المصنعة (اختياري)") }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 form-group">
                                {{ form.serial_number.label }}
                                {{ form.serial_number(class="form-control", placeholder="الرقم التسلسلي (اختياري)") }}
                            </div>
                            <div class="col-md-6 form-group">
                                {{ form.status.label }}
                                {{ form.status(class="form-control") }}
                            </div>
                        </div>





                        <!-- معلومات موقع الجهاز -->
                        <h5 class="mb-3 mt-4"><i class="fas fa-map-marker-alt"></i> الموقع</h5>
                        <div class="row">
                            <div class="col-md-6 form-group">
                                {{ form.device_location.label }}
                                {{ form.device_location(class="form-control", placeholder="مثل: الإدارة، التموين، المكتب الرئيسي") }}
                                <small class="text-muted">أدخل موقع الجهاز</small>
                            </div>
                            <!-- تم إزالة حقل المستودع لأن الأجهزة أصبحت مستقلة عن المستودعات -->
                        </div>

                        <!-- ملاحظات -->
                        <h5 class="mb-3 mt-4"><i class="fas fa-sticky-note"></i> ملاحظات إضافية</h5>
                        <div class="form-group">
                            {{ form.notes.label }}
                            {{ form.notes(class="form-control", rows=3, placeholder="ملاحظات إضافية (اختياري)") }}
                        </div>
                        <div class="form-group text-center mt-4">
                            <a href="{{ url_for('inventory.device_details', device_id=device.id) }}" class="btn btn-secondary">إلغاء</a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize date pickers
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            rtl: true,
            language: 'ar',
            clearBtn: true,
            todayBtn: 'linked',
            todayHighlight: true
        });
    });
</script>
{% endblock %}
