/* ===== Backup Dashboard Stylesheet ===== */
/* Author: Military Warehouse Management System
   Description: Custom CSS for the backup dashboard
*/

/* تنسيق خاص لزر رفع النسخة الاحتياطية */
.btn-upload-backup {
  display: inline-block !important;
  width: auto !important;
  white-space: nowrap !important;
  overflow: visible !important;
  padding: 0.75rem 1.5rem !important;
  margin: 0.5rem !important;
  z-index: 100 !important;
  min-width: 250px !important;
  text-align: center !important;
  font-size: 1.1rem !important;
  font-weight: bold !important;
  direction: rtl !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  border-radius: 6px !important;
}

/* إضافة تأثير عند المرور بالمؤشر */
.btn-upload-backup:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15) !important;
  background-color: #0d6efd !important;
  border-color: #0d6efd !important;
}

/* تنسيق عام للأزرار في صفحة النسخ الاحتياطي */
[id^="create-backup"] {
  width: auto;
  white-space: nowrap;
  overflow: visible;
}

/* تنسيق خاص للأزرار في الشاشة الرئيسية */
.backup-btn {
  width: auto !important;
  white-space: nowrap !important;
  overflow: visible !important;
  display: inline-block !important;
  margin: 0.5rem !important;
  min-width: 180px !important;
  text-align: center !important;
  font-weight: bold !important;
  direction: rtl !important;
  padding: 0.5rem 1.25rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  border-radius: 6px !important;
}

/* إضافة تأثير عند المرور بالمؤشر */
.backup-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15) !important;
  background-color: #0d6efd !important;
  border-color: #0d6efd !important;
}

/* ===== Variables ===== */
:root {
  /* Dashboard Colors - Dark Mode (Default) */
  --backup-bg: var(--bg-primary);
  --backup-card-bg: var(--bg-secondary);
  --backup-card-header: var(--bg-tertiary);
  --backup-accent: #3b82f6;
  --backup-success: #10b981;
  --backup-warning: #f59e0b;
  --backup-danger: #ef4444;
  --backup-info: #06b6d4;
  --backup-text: var(--text-primary);
  --backup-text-secondary: var(--text-secondary);
  --backup-border: #475569;
  --backup-shadow: rgba(0, 0, 0, 0.25);
  --backup-gradient-start: #242424;
  --backup-gradient-end: #1d1d1d;
}

/* Light Mode Colors */
body.light-theme {
  --backup-bg: var(--light-bg-primary);
  --backup-card-bg: var(--light-bg-secondary);
  --backup-card-header: var(--light-bg-tertiary);
  --backup-text: var(--light-text-primary);
  --backup-text-secondary: var(--light-text-secondary);
  --backup-border: var(--light-border-color);
  --backup-shadow: rgba(0, 0, 0, 0.1);
  --backup-gradient-start: #ffffff;
  --backup-gradient-end: #eef2f7;
  --backup-accent: #0d6efd;
  --backup-success: #198754;
  --backup-warning: #ffc107;
  --backup-danger: #dc3545;
  --backup-info: #0dcaf0;
}

/* ===== Dashboard Container ===== */
.backup-dashboard {
  background-color: var(--backup-bg);
  padding: 1.5rem;
  border-radius: 10px;
  margin-bottom: 2rem;
}

/* ===== Statistics Cards ===== */
.stats-card {
  background: var(--backup-card-bg);
  border-radius: 12px;
  padding: 1.25rem;
  height: 100%;
  box-shadow: 0 4px 8px var(--backup-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-left: 5px solid var(--backup-accent);
  max-height: 220px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.stats-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px var(--backup-shadow);
}

.stats-card.success {
  border-left-color: var(--backup-success);
}

.stats-card.warning {
  border-left-color: var(--backup-warning);
}

.stats-card.danger {
  border-left-color: var(--backup-danger);
}

.stats-card.info {
  border-left-color: var(--backup-info);
}

.stats-card .card-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  color: var(--backup-accent);
}

.stats-card.success .card-icon {
  color: var(--backup-success);
}

.stats-card.warning .card-icon {
  color: var(--backup-warning);
}

.stats-card.danger .card-icon {
  color: var(--backup-danger);
}

.stats-card.info .card-icon {
  color: var(--backup-info);
}

.stats-card .card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--backup-text);
  margin-bottom: 0.75rem;
}

.stats-card .card-value {
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--backup-text);
  margin-bottom: 0.5rem;
}

.stats-card p {
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
  color: var(--backup-text-secondary);
}

/* ===== Backup History Table ===== */
.backup-table {
  background: var(--backup-card-bg);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 8px var(--backup-shadow);
  margin-bottom: 1.5rem;
}

.backup-table .card-header {
  background-color: var(--backup-card-header);
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--backup-border);
}

.backup-table .card-header h5 {
  font-weight: 600;
  margin: 0;
}

.backup-table .card-body {
  padding: 1.25rem;
}

.backup-table .table {
  margin-bottom: 0;
  color: var(--backup-text);
}

.backup-table th {
  background-color: var(--backup-card-header);
  color: var(--backup-text);
  border-bottom: none;
  padding: 0.75rem 1rem;
  font-weight: 600;
}

.backup-table td {
  color: var(--backup-text);
  vertical-align: middle;
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--backup-border);
}

.backup-table tr:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.backup-table .table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* ===== Forms ===== */
.form-control, .form-select {
  background-color: var(--backup-bg);
  border-color: var(--backup-border);
  color: var(--backup-text);
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
  border-color: var(--backup-accent);
  box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
  background-color: var(--backup-bg);
  color: var(--backup-text);
}

.form-label {
  color: var(--backup-text);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-text {
  color: var(--backup-text-secondary);
  font-size: 0.875rem;
}

/* ===== Checkboxes ===== */
.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  position: relative;
}

.form-check-input {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: var(--backup-bg);
  border: 2px solid var(--backup-border);
  width: 1.5rem;
  height: 1.5rem;
  margin-top: 0;
  margin-right: 0.75rem;
  cursor: pointer;
  position: relative;
  flex-shrink: 0;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.form-check-input:checked {
  background-color: var(--backup-accent);
  border-color: var(--backup-accent);
  position: relative;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 1rem;
}

/* إزالة علامة الصح المكررة */
.form-check-input:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 1rem;
  line-height: 1;
}

.form-check-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
  border-color: var(--backup-accent);
  outline: none;
}

.form-check-label {
  color: var(--backup-text);
  cursor: pointer;
  user-select: none;
  margin-bottom: 0;
  font-size: 1rem;
  line-height: 1.5;
}

/* خاص بصفحة الاستعادة */
#restore .form-check, #backup-content .form-check {
  background-color: rgba(59, 130, 246, 0.05);
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid var(--backup-border);
  transition: all 0.2s ease;
  margin-bottom: 0.75rem;
}

#restore .form-check:hover, #backup-content .form-check:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

#restore .form-check-input:checked + .form-check-label,
#backup-content .form-check-input:checked + .form-check-label {
  font-weight: 500;
}

/* تنسيق مربعات الاختيار في محتوى النسخة الاحتياطية */
#backup-content .form-check, #backup-content-options .form-check {
  display: inline-flex;
  margin-right: 1rem;
  margin-bottom: 1rem;
  min-width: 120px;
  transition: all 0.2s ease;
}

#backup-content .form-check:hover, #backup-content-options .form-check:hover {
  transform: translateY(-2px);
}

#backup-content .form-check-input, #backup-content-options .form-check-input {
  width: 1.5rem;
  height: 1.5rem;
}

#backup-content .form-check-label, #backup-content-options .form-check-label {
  font-size: 0.95rem;
  padding-top: 0.15rem;
}

/* تنسيق خاص لمربعات الاختيار في محتوى النسخة الاحتياطية */
/* معالجة مشكلة تغطية مربعات الاختيار على النصوص */
.form-check-input[type="checkbox"] {
  position: relative;
  z-index: 1;
  margin-right: 1.5rem;
  margin-left: 0.5rem;
}

/* تنسيق خاص لمربعات الاختيار في صفحة الاستعادة */
#restore .form-check-input {
  margin-right: 1.5rem;
}

/* تنسيق خاص لمربعات الاختيار في محتوى النسخة الاحتياطية */
.form-check {
  padding-right: 2rem;
  padding-left: 0.5rem;
}

/* تنسيق خاص للنص بجانب مربعات الاختيار */
.form-check-label {
  position: relative;
  z-index: 0;
  padding-right: 0.5rem;
  white-space: nowrap;
  overflow: visible;
}

/* تنسيق خاص لمربعات الاختيار في محتوى النسخة الاحتياطية (الصورة الثانية) */
#backup-content .form-check-input {
  position: absolute;
  right: 0.5rem;
  margin-right: 0;
  top: 50%;
  transform: translateY(-50%);
}

#backup-content .form-check-label {
  padding-right: 2.5rem;
  display: inline-block;
  width: auto;
  text-align: right;
  margin-right: 0.5rem;
}

#backup-content .form-check {
  position: relative;
  padding-right: 0.5rem;
  margin-right: 0.5rem;
  width: auto;
  min-width: 150px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* تنسيق خاص لمربع الاختيار الخاص بتأكيد استبدال البيانات الحالية */
input[id="confirm-restore"] {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  margin: 0;
}

label[for="confirm-restore"] {
  padding-right: 3rem;
  display: block;
  width: 100%;
  text-align: right;
}

/* تنسيق خاص لمربعات الاختيار في الصورة الأولى (خيارات الاستعادة) */
.form-check-input[type="checkbox"] + .form-check-label {
  padding-right: 2.5rem;
  position: relative;
}

.form-check-input[type="checkbox"] {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  margin: 0;
  z-index: 2;
}

.restore-options-container {
  background-color: rgba(59, 130, 246, 0.03);
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px solid var(--backup-border);
  margin-top: 0.5rem;
}

.confirm-restore-check {
  background-color: rgba(245, 158, 11, 0.1) !important;
  border: 1px solid rgba(245, 158, 11, 0.3) !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
  transition: all 0.2s ease;
}

.confirm-restore-check:hover {
  background-color: rgba(245, 158, 11, 0.15) !important;
}

.confirm-restore-check .form-check-input {
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 1rem;
}

.confirm-restore-check .form-check-input:checked {
  background-color: var(--backup-warning);
  border-color: var(--backup-warning);
}

/* ===== Buttons ===== */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  background-color: var(--backup-accent);
  border-color: var(--backup-accent);
}

.btn-primary:hover {
  background-color: var(--backup-accent);
  filter: brightness(90%);
}

.btn-warning {
  background-color: var(--backup-warning);
  border-color: var(--backup-warning);
  color: #212529;
}

.btn-warning:hover {
  background-color: var(--backup-warning);
  filter: brightness(90%);
  color: #212529;
}

.btn-danger {
  background-color: var(--backup-danger);
  border-color: var(--backup-danger);
}

.btn-danger:hover {
  background-color: var(--backup-danger);
  filter: brightness(90%);
}

.btn-info {
  background-color: var(--backup-info);
  border-color: var(--backup-info);
  color: #212529;
}

.btn-info:hover {
  background-color: var(--backup-info);
  filter: brightness(90%);
  color: #212529;
}

.btn-outline-secondary {
  color: var(--backup-text-secondary);
  border-color: var(--backup-border);
}

.btn-outline-secondary:hover {
  background-color: var(--backup-border);
  color: var(--backup-text);
}

/* ===== Progress Bars ===== */
.backup-progress {
  height: 10px;
  border-radius: 5px;
  margin-top: 1rem;
  background-color: var(--backup-border);
  overflow: hidden;
}

.backup-progress .progress-bar {
  background-color: var(--backup-accent);
  border-radius: 5px;
  transition: width 0.3s ease;
}

.backup-progress .progress-bar.bg-warning {
  background-color: var(--backup-warning);
}

.backup-progress .progress-bar.bg-success {
  background-color: var(--backup-success);
}

.backup-progress .progress-bar.bg-danger {
  background-color: var(--backup-danger);
}

/* ===== Advanced Options ===== */
.advanced-options {
  background: var(--backup-card-bg);
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px var(--backup-shadow);
  margin-top: 1.5rem;
  border: 1px solid var(--backup-border);
}

.advanced-options h6 {
  color: var(--backup-text);
  font-weight: 600;
  margin-bottom: 1rem;
}

/* ===== Badges ===== */
.badge {
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 600;
  border-radius: 0.375rem;
}

.badge.bg-primary {
  background-color: var(--backup-accent) !important;
}

.badge.bg-success {
  background-color: var(--backup-success) !important;
}

.badge.bg-warning {
  background-color: var(--backup-warning) !important;
  color: #212529;
}

.badge.bg-danger {
  background-color: var(--backup-danger) !important;
}

.badge.bg-info {
  background-color: var(--backup-info) !important;
  color: #212529;
}

.badge.bg-secondary {
  background-color: var(--backup-text-secondary) !important;
}

/* ===== Alerts ===== */
.alert {
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
}

.alert-info {
  color: var(--backup-info);
  background-color: rgba(6, 182, 212, 0.1);
  border-color: rgba(6, 182, 212, 0.2);
}

.alert-warning {
  color: var(--backup-warning);
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.alert-danger {
  color: var(--backup-danger);
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.alert-success {
  color: var(--backup-success);
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}

/* ===== Restore Modal ===== */
.restore-modal .modal-content {
  background-color: var(--backup-card-bg);
  color: var(--backup-text);
}

.restore-modal .modal-header {
  border-bottom-color: var(--backup-border);
}

.restore-modal .modal-footer {
  border-top-color: var(--backup-border);
}

/* ===== Backup Status Badges ===== */
.badge-success {
  background-color: var(--backup-success);
  color: white;
}

.badge-warning {
  background-color: var(--backup-warning);
  color: white;
}

.badge-danger {
  background-color: var(--backup-danger);
  color: white;
}

.badge-info {
  background-color: var(--backup-info);
  color: white;
}

/* ===== Backup Type Icons ===== */
.backup-type-icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 768px) {
  .stats-card {
    margin-bottom: 1rem;
  }

  .stats-card .card-icon {
    font-size: 2rem;
  }

  .stats-card .card-value {
    font-size: 1.5rem;
  }
}
