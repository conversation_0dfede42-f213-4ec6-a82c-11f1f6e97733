#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import psycopg2
import os
from datetime import datetime

def restore_locations_from_backup():
    """استعادة المواقع من ملف locations.db إلى قاعدة البيانات الرئيسية"""
    
    # مسار ملف النسخة الاحتياطية
    backup_db_path = r'C:\Users\<USER>\Desktop\iMQS1\locations.db'
    
    if not os.path.exists(backup_db_path):
        print(f"❌ ملف النسخة الاحتياطية غير موجود: {backup_db_path}")
        return False
    
    try:
        # الاتصال بملف SQLite
        sqlite_conn = sqlite3.connect(backup_db_path)
        sqlite_cursor = sqlite_conn.cursor()
        
        # الاتصال بقاعدة البيانات الرئيسية PostgreSQL
        pg_conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        pg_cursor = pg_conn.cursor()
        
        print("✅ تم الاتصال بقواعد البيانات بنجاح")
        print("=" * 60)
        
        # قراءة المواقع من SQLite
        sqlite_cursor.execute("SELECT * FROM locations ORDER BY id")
        locations = sqlite_cursor.fetchall()
        
        # الحصول على أسماء الأعمدة
        sqlite_cursor.execute("PRAGMA table_info(locations)")
        columns_info = sqlite_cursor.fetchall()
        column_names = [col[1] for col in columns_info]
        
        print(f"📊 تم العثور على {len(locations)} موقع في النسخة الاحتياطية")
        print(f"📋 الأعمدة: {column_names}")
        print("-" * 60)
        
        # التحقق من المواقع الموجودة في PostgreSQL
        pg_cursor.execute("SELECT COUNT(*) FROM locations WHERE name != 'موقع افتراضي'")
        existing_count = pg_cursor.fetchone()[0]
        print(f"📊 المواقع الموجودة حالياً في PostgreSQL: {existing_count}")
        
        if existing_count > 0:
            response = input("⚠️  يوجد مواقع في قاعدة البيانات. هل تريد حذفها واستبدالها؟ (y/n): ")
            if response.lower() != 'y':
                print("❌ تم إلغاء العملية")
                return False
            
            # حذف المواقع الموجودة (عدا الافتراضي)
            pg_cursor.execute("DELETE FROM locations WHERE name != 'موقع افتراضي'")
            print("🗑️  تم حذف المواقع الموجودة")
        
        # نقل المواقع
        restored_count = 0
        failed_count = 0
        
        for location in locations:
            try:
                # تحويل البيانات إلى قاموس
                location_dict = dict(zip(column_names, location))
                
                # تحضير البيانات للإدراج
                name = location_dict.get('name', '')
                serial_number = location_dict.get('serial_number', '')
                location_type = location_dict.get('type', 'عام')
                status = location_dict.get('status', 'نشط')
                coordinates = location_dict.get('coordinates', '')
                description = location_dict.get('description', '')
                created_by = location_dict.get('created_by', None)
                
                # تحويل التواريخ
                created_at = location_dict.get('created_at', datetime.now().isoformat())
                updated_at = location_dict.get('updated_at', datetime.now().isoformat())
                
                # إدراج الموقع في PostgreSQL
                insert_query = """
                    INSERT INTO locations 
                    (name, serial_number, type, status, coordinates, description, 
                     created_at, updated_at, created_by)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                """
                
                pg_cursor.execute(insert_query, (
                    name, serial_number, location_type, status, coordinates, description,
                    created_at, updated_at, created_by
                ))
                
                new_location_id = pg_cursor.fetchone()[0]
                restored_count += 1
                
                print(f"✅ تم استعادة الموقع: {name} (ID: {new_location_id})")
                
            except Exception as e:
                failed_count += 1
                print(f"❌ فشل في استعادة الموقع {location_dict.get('name', 'غير محدد')}: {e}")
        
        # حفظ التغييرات
        pg_conn.commit()
        
        print("\n" + "=" * 60)
        print("📊 ملخص العملية:")
        print(f"   ✅ تم استعادة: {restored_count} موقع")
        print(f"   ❌ فشل في: {failed_count} موقع")
        print(f"   📍 إجمالي المواقع: {restored_count + failed_count}")
        
        # التحقق من النتيجة النهائية
        pg_cursor.execute("SELECT COUNT(*) FROM locations")
        total_locations = pg_cursor.fetchone()[0]
        print(f"   📋 إجمالي المواقع في قاعدة البيانات الآن: {total_locations}")
        
        # إغلاق الاتصالات
        sqlite_conn.close()
        pg_conn.close()
        
        print("\n🎉 تم استعادة المواقع بنجاح!")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة بيانات SQLite: {e}")
        return False
    except psycopg2.Error as e:
        print(f"❌ خطأ في قاعدة بيانات PostgreSQL: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def verify_locations_restoration():
    """التحقق من استعادة المواقع"""
    try:
        pg_conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        pg_cursor = pg_conn.cursor()
        
        print("\n🔍 التحقق من المواقع المستعادة:")
        print("=" * 50)
        
        # عرض جميع المواقع
        pg_cursor.execute("""
            SELECT id, name, serial_number, type, status 
            FROM locations 
            ORDER BY id
        """)
        
        locations = pg_cursor.fetchall()
        
        print(f"📊 إجمالي المواقع: {len(locations)}")
        print("-" * 50)
        
        for i, location in enumerate(locations, 1):
            location_id, name, serial_number, location_type, status = location
            print(f"{i:2d}. {name}")
            print(f"    🆔 ID: {location_id}")
            print(f"    🔢 الرقم التسلسلي: {serial_number}")
            print(f"    📂 النوع: {location_type}")
            print(f"    📊 الحالة: {status}")
            print()
        
        pg_conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

if __name__ == "__main__":
    print("🔄 استعادة المواقع المفقودة")
    print("=" * 60)
    
    # استعادة المواقع
    success = restore_locations_from_backup()
    
    if success:
        # التحقق من النتائج
        verify_locations_restoration()
        
        print("\n" + "=" * 60)
        print("✅ تم استعادة جميع المواقع بنجاح!")
        print("🌐 يمكنك الآن الوصول إلى إدارة المواقع في الموقع")
        print("📍 الرابط: http://localhost:5000/locations/")
    else:
        print("\n❌ فشل في استعادة المواقع")
        print("💡 تحقق من وجود ملف locations.db في المسار الصحيح")
