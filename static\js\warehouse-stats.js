/**
 * Warehouse Statistics JavaScript
 * Handles real-time updates and visualization of warehouse statistics
 */

// Configuration
const REFRESH_INTERVAL = 60000; // Update interval in milliseconds (1 minute)
let updateTimers = {}; // Store update timers by warehouse ID

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize warehouse statistics display
    initWarehouseStats();
    
    // Initialize comparison tool if present
    initWarehouseComparison();
    
    // Initialize display screens auto-refresh
    initDisplayScreens();
});

/**
 * Initialize warehouse statistics displays
 */
function initWarehouseStats() {
    const statContainers = document.querySelectorAll('[data-warehouse-stats]');
    
    statContainers.forEach(function(container) {
        const warehouseId = container.getAttribute('data-warehouse-id');
        if (warehouseId) {
            // Load initial stats
            loadWarehouseStats(warehouseId, container);
            
            // Set up auto-refresh if needed
            if (container.hasAttribute('data-auto-refresh')) {
                updateTimers[warehouseId] = setInterval(function() {
                    loadWarehouseStats(warehouseId, container);
                }, REFRESH_INTERVAL);
            }
        }
    });
}

/**
 * Load and display warehouse statistics
 * @param {string} warehouseId - The ID of the warehouse
 * @param {HTMLElement} container - The container element to update
 */
function loadWarehouseStats(warehouseId, container) {
    // Show loading indicator
    if (container.querySelector('.stats-loading')) {
        container.querySelector('.stats-loading').style.display = 'block';
    }
    
    // Fetch statistics data from the API
    fetch(`/api/warehouse/${warehouseId}/stats`)
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في تحميل البيانات');
            }
            return response.json();
        })
        .then(data => {
            updateWarehouseStatsDisplay(container, data);
        })
        .catch(error => {
            console.error('Error fetching warehouse stats:', error);
            
            // Show error message
            if (container.querySelector('.stats-error')) {
                container.querySelector('.stats-error').style.display = 'block';
                container.querySelector('.stats-error').textContent = 'فشل في تحديث البيانات: ' + error.message;
            }
        })
        .finally(() => {
            // Hide loading indicator
            if (container.querySelector('.stats-loading')) {
                container.querySelector('.stats-loading').style.display = 'none';
            }
            
            // Update last refresh time
            if (container.querySelector('.stats-last-updated')) {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA');
                container.querySelector('.stats-last-updated').textContent = `آخر تحديث: ${timeString}`;
            }
        });
}

/**
 * Update the warehouse statistics display with new data
 * @param {HTMLElement} container - The container element to update
 * @param {Object} data - The statistics data
 */
function updateWarehouseStatsDisplay(container, data) {
    // Update warehouse name if element exists
    const nameElement = container.querySelector('[data-stat="warehouse-name"]');
    if (nameElement && data.warehouse_name) {
        nameElement.textContent = data.warehouse_name;
    }
    
    // Update weapons count
    updateStatElement(container, 'weapons-count', data.weapons_count);
    
    // Update personnel count
    updateStatElement(container, 'personnel-count', data.personnel_count);
    
    // Update weapon status counts
    if (data.weapon_status) {
        updateStatElement(container, 'active-weapons', data.weapon_status.active);
        updateStatElement(container, 'leave-weapons', data.weapon_status.leave);
        updateStatElement(container, 'mission-weapons', data.weapon_status.mission);
        updateStatElement(container, 'maintenance-weapons', data.weapon_status.maintenance);
        updateStatElement(container, 'damaged-weapons', data.weapon_status.damaged);
    }
    
    // Update chart if it exists in the container
    updateStatChart(container, data);
}

/**
 * Update a statistic element with new data
 * @param {HTMLElement} container - The parent container
 * @param {string} statName - The data-stat attribute value to update
 * @param {number|string} value - The new value
 */
function updateStatElement(container, statName, value) {
    const element = container.querySelector(`[data-stat="${statName}"]`);
    if (element) {
        // Animate the counter change for number values
        if (typeof value === 'number') {
            animateCounter(element, parseFloat(element.textContent) || 0, value);
        } else {
            element.textContent = value;
        }
        
        // Update any related progress bars
        const progressBar = container.querySelector(`[data-progress="${statName}"]`);
        if (progressBar) {
            updateProgressBar(progressBar, value);
        }
    }
}

/**
 * Animate a counter from start to end value
 * @param {HTMLElement} element - The element to animate
 * @param {number} start - Starting value
 * @param {number} end - Ending value
 * @param {number} duration - Animation duration in milliseconds
 */
function animateCounter(element, start, end, duration = 800) {
    const range = end - start;
    const minValue = Math.abs(range) > 100 ? 1 : 0.1;
    const stepTime = Math.abs(Math.floor(duration / range));
    
    // Don't animate tiny changes
    if (Math.abs(range) < 1) {
        element.textContent = end;
        return;
    }
    
    let current = start;
    const step = range / (duration / 10);
    
    const timer = setInterval(function() {
        current += step;
        
        if ((step > 0 && current >= end) || (step < 0 && current <= end)) {
            element.textContent = end;
            clearInterval(timer);
        } else {
            element.textContent = Math.round(current * 10) / 10;
        }
    }, 10);
}

/**
 * Update a progress bar with new value
 * @param {HTMLElement} progressBar - The progress bar element
 * @param {number} value - The new value
 */
function updateProgressBar(progressBar, value) {
    const maxValue = progressBar.getAttribute('aria-valuemax') || 100;
    const percentage = (value / maxValue) * 100;
    
    progressBar.style.width = `${percentage}%`;
    progressBar.setAttribute('aria-valuenow', value);
}

/**
 * Update chart data and redraw
 * @param {HTMLElement} container - The container element
 * @param {Object} data - The new data
 */
function updateStatChart(container, data) {
    const chartCanvas = container.querySelector('canvas[data-chart-type]');
    if (!chartCanvas) return;
    
    const chartType = chartCanvas.getAttribute('data-chart-type');
    const chartInstance = Chart.instances[chartCanvas.id];
    
    if (!chartInstance) return;
    
    if (chartType === 'weapon-status' && data.weapon_status) {
        chartInstance.data.datasets[0].data = [
            data.weapon_status.active,
            data.weapon_status.leave,
            data.weapon_status.mission,
            data.weapon_status.maintenance,
            data.weapon_status.damaged,
            data.weapon_status.other || 0
        ];
        
        chartInstance.update();
    }
}

/**
 * Initialize warehouse comparison tool
 */
function initWarehouseComparison() {
    const comparisonContainer = document.getElementById('warehouse-comparison');
    if (!comparisonContainer) return;
    
    const warehouseSelectors = comparisonContainer.querySelectorAll('select[data-warehouse-selector]');
    if (warehouseSelectors.length === 0) return;
    
    // Set up change listeners for warehouse selectors
    warehouseSelectors.forEach(selector => {
        selector.addEventListener('change', function() {
            updateWarehouseComparison();
        });
    });
    
    // Initial update
    updateWarehouseComparison();
}

/**
 * Update the warehouse comparison display
 */
function updateWarehouseComparison() {
    const comparisonContainer = document.getElementById('warehouse-comparison');
    if (!comparisonContainer) return;
    
    const warehouseSelectors = comparisonContainer.querySelectorAll('select[data-warehouse-selector]');
    if (warehouseSelectors.length === 0) return;
    
    // Collect selected warehouse IDs
    const selectedWarehouses = Array.from(warehouseSelectors)
        .map(selector => selector.value)
        .filter(value => value && value !== '0');
    
    // If no warehouses are selected, hide the comparison
    if (selectedWarehouses.length === 0) {
        comparisonContainer.querySelector('.comparison-results').style.display = 'none';
        return;
    }
    
    // Show loading indicator
    const loadingIndicator = comparisonContainer.querySelector('.comparison-loading');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'block';
    }
    
    // Fetch data for each selected warehouse
    Promise.all(selectedWarehouses.map(warehouseId => 
        fetch(`/api/warehouse/${warehouseId}/stats`).then(response => response.json())
    ))
    .then(results => {
        displayWarehouseComparison(results);
    })
    .catch(error => {
        console.error('Error fetching comparison data:', error);
        
        // Show error message
        const errorElement = comparisonContainer.querySelector('.comparison-error');
        if (errorElement) {
            errorElement.style.display = 'block';
            errorElement.textContent = 'فشل في تحديث بيانات المقارنة: ' + error.message;
        }
    })
    .finally(() => {
        // Hide loading indicator
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
        
        // Show results container
        comparisonContainer.querySelector('.comparison-results').style.display = 'block';
    });
}

/**
 * Display warehouse comparison results
 * @param {Array} warehousesData - Array of warehouse statistics data
 */
function displayWarehouseComparison(warehousesData) {
    const comparisonContainer = document.getElementById('warehouse-comparison');
    if (!comparisonContainer) return;
    
    const tableBody = comparisonContainer.querySelector('.comparison-table tbody');
    if (!tableBody) return;
    
    // Clear existing rows
    tableBody.innerHTML = '';
    
    // Create table header with warehouse names
    const headerRow = document.createElement('tr');
    headerRow.innerHTML = '<th>المؤشر</th>';
    
    warehousesData.forEach(data => {
        headerRow.innerHTML += `<th>${data.warehouse_name}</th>`;
    });
    
    tableBody.appendChild(headerRow);
    
    // Add rows for different metrics
    addComparisonRow(tableBody, 'إجمالي الأسلحة', warehousesData.map(data => data.weapons_count));
    addComparisonRow(tableBody, 'إجمالي الأفراد', warehousesData.map(data => data.personnel_count));
    addComparisonRow(tableBody, 'أسلحة نشطة', warehousesData.map(data => data.weapon_status.active));
    addComparisonRow(tableBody, 'أسلحة في إجازة', warehousesData.map(data => data.weapon_status.leave));
    addComparisonRow(tableBody, 'أسلحة في مهمة', warehousesData.map(data => data.weapon_status.mission));
    addComparisonRow(tableBody, 'أسلحة في صيانة', warehousesData.map(data => data.weapon_status.maintenance));
    addComparisonRow(tableBody, 'أسلحة تالفة', warehousesData.map(data => data.weapon_status.damaged));
    
    // Update the comparison chart
    updateComparisonChart(warehousesData);
}

/**
 * Add a row to the comparison table
 * @param {HTMLElement} tableBody - The table body element
 * @param {string} label - Row label
 * @param {Array} values - Array of values for each warehouse
 */
function addComparisonRow(tableBody, label, values) {
    const row = document.createElement('tr');
    
    row.innerHTML = `<td>${label}</td>`;
    
    // Add cells with values, highlighting the highest value
    const maxValue = Math.max(...values);
    
    values.forEach(value => {
        const isHighest = value === maxValue && maxValue > 0;
        row.innerHTML += `<td class="${isHighest ? 'text-primary font-weight-bold' : ''}">${value}</td>`;
    });
    
    tableBody.appendChild(row);
}

/**
 * Update the comparison chart with new data
 * @param {Array} warehousesData - Array of warehouse statistics data
 */
function updateComparisonChart(warehousesData) {
    const chartCanvas = document.getElementById('warehouseComparisonChart');
    if (!chartCanvas) return;
    
    const chartData = {
        labels: warehousesData.map(data => data.warehouse_name),
        datasets: [
            {
                label: 'الأسلحة',
                data: warehousesData.map(data => data.weapons_count),
                backgroundColor: 'rgba(0, 123, 255, 0.7)'
            },
            {
                label: 'الأفراد',
                data: warehousesData.map(data => data.personnel_count),
                backgroundColor: 'rgba(40, 167, 69, 0.7)'
            }
        ]
    };
    
    // Get existing chart instance
    const chartInstance = Chart.instances[chartCanvas.id];
    
    if (chartInstance) {
        // Update existing chart
        chartInstance.data.labels = chartData.labels;
        chartInstance.data.datasets = chartData.datasets;
        chartInstance.update();
    } else {
        // Create new chart
        new Chart(chartCanvas, {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

/**
 * Initialize auto-refreshing display screens
 */
function initDisplayScreens() {
    const displayScreen = document.querySelector('.display-screen');
    if (!displayScreen) return;
    
    const warehouseId = displayScreen.getAttribute('data-warehouse-id');
    if (!warehouseId) return;
    
    // Function to update the display screen
    function updateDateTime() {
    const now = new Date();
    
    // تحديث الوقت
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = now.toLocaleTimeString('ar-SA');
    }
    
    // تحديث التاريخ
    const dateElement = document.getElementById('current-date');
    if (dateElement) {
        dateElement.textContent = now.toLocaleDateString('ar-SA', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
    
    // تحديث وقت آخر تحديث
    const lastUpdateElement = document.getElementById('last-update');
    if (lastUpdateElement) {
        lastUpdateElement.textContent = now.toLocaleTimeString('ar-SA');
    }
}

function updateDisplayScreen() {
    updateDateTime();
    setInterval(updateDateTime, 1000);
        fetch(`/api/warehouse/${warehouseId}/stats`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('فشل في تحميل البيانات');
                }
                return response.json();
            })
            .then(data => {
                // Update warehouse name
                const nameElement = document.querySelector('.display-title');
                if (nameElement && data.warehouse_name) {
                    nameElement.textContent = data.warehouse_name;
                }
                
                // Update counters
                updateStatElement(displayScreen, 'weapons-count', data.weapons_count);
                updateStatElement(displayScreen, 'personnel-count', data.personnel_count);
                
                // Update weapon status counts
                if (data.weapon_status) {
                    updateStatElement(displayScreen, 'active-weapons', data.weapon_status.active);
                    updateStatElement(displayScreen, 'leave-weapons', data.weapon_status.leave);
                    updateStatElement(displayScreen, 'mission-weapons', data.weapon_status.mission);
                    updateStatElement(displayScreen, 'maintenance-weapons', data.weapon_status.maintenance);
                    updateStatElement(displayScreen, 'damaged-weapons', data.weapon_status.damaged);
                }
                
                // Update last refresh time
                const lastUpdated = document.querySelector('.display-last-updated');
                if (lastUpdated) {
                    const now = new Date();
                    const timeString = now.toLocaleTimeString('ar-SA');
                    lastUpdated.textContent = `آخر تحديث: ${timeString}`;
                }
            })
            .catch(error => {
                console.error('Error updating display screen:', error);
            });
    }
    
    // Initial update
    updateDisplayScreen();
    
    // Set interval for updates (every 30 seconds)
    setInterval(updateDisplayScreen, 30000);
}

/**
 * Clean up timers when page is unloaded
 */
window.addEventListener('beforeunload', function() {
    // Clear all update timers
    Object.values(updateTimers).forEach(timer => clearInterval(timer));
});
