#!/usr/bin/env python3
"""
سكريبت تنظيف ملفات SQLite المؤقتة بعد نقل البيانات إلى PostgreSQL
"""

import os
import sys
import shutil
from datetime import datetime

def backup_sqlite_files():
    """إنشاء نسخة احتياطية من ملفات SQLite قبل الحذف"""
    
    print("📦 إنشاء نسخة احتياطية من ملفات SQLite...")
    
    # إنشاء مجلد النسخ الاحتياطية
    backup_dir = f"sqlite_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        print(f"   📁 تم إنشاء مجلد النسخ الاحتياطية: {backup_dir}")
        
        # قائمة ملفات SQLite المطلوب نسخها
        sqlite_files = [
            'military_warehouse.db',
            'locations.db',
            'instance/military_warehouse.db'
        ]
        
        backed_up_files = []
        
        for file_path in sqlite_files:
            if os.path.exists(file_path):
                try:
                    # تحديد اسم الملف في النسخة الاحتياطية
                    backup_filename = file_path.replace('/', '_').replace('\\', '_')
                    backup_path = os.path.join(backup_dir, backup_filename)
                    
                    # نسخ الملف
                    shutil.copy2(file_path, backup_path)
                    backed_up_files.append(file_path)
                    print(f"   ✅ تم نسخ: {file_path} -> {backup_path}")
                    
                except Exception as e:
                    print(f"   ❌ فشل في نسخ {file_path}: {str(e)}")
            else:
                print(f"   ⚠️  الملف غير موجود: {file_path}")
        
        if backed_up_files:
            print(f"   📊 تم نسخ {len(backed_up_files)} ملف بنجاح")
            return backup_dir, backed_up_files
        else:
            print("   ⚠️  لم يتم العثور على أي ملفات SQLite للنسخ")
            # حذف المجلد الفارغ
            os.rmdir(backup_dir)
            return None, []
            
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return None, []

def verify_postgresql_data():
    """التحقق من وجود البيانات في PostgreSQL قبل الحذف"""
    
    print("\n🔍 التحقق من البيانات في PostgreSQL...")
    
    try:
        from sqlalchemy import create_engine, text
        
        DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/military_warehouse")
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as conn:
            # فحص الجداول والبيانات
            tables_to_check = [
                'receipt_data',
                'receipt_locations', 
                'patrol_data',
                'patrol_locations',
                'shifts_data',
                'shifts_locations'
            ]
            
            total_records = 0
            all_tables_ok = True
            
            for table in tables_to_check:
                try:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.scalar()
                    total_records += count
                    print(f"   ✅ {table}: {count} سجل")
                except Exception as e:
                    print(f"   ❌ {table}: خطأ - {str(e)}")
                    all_tables_ok = False
            
            if all_tables_ok and total_records > 0:
                print(f"   📊 إجمالي السجلات في PostgreSQL: {total_records}")
                print("   ✅ البيانات متوفرة في PostgreSQL")
                return True
            else:
                print("   ❌ مشكلة في البيانات في PostgreSQL")
                return False
                
    except Exception as e:
        print(f"   ❌ خطأ في التحقق من PostgreSQL: {str(e)}")
        return False

def cleanup_sqlite_files(backup_dir, backed_up_files):
    """حذف ملفات SQLite المؤقتة"""
    
    print("\n🗑️  حذف ملفات SQLite المؤقتة...")
    
    deleted_files = []
    
    for file_path in backed_up_files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                deleted_files.append(file_path)
                print(f"   ✅ تم حذف: {file_path}")
            else:
                print(f"   ⚠️  الملف غير موجود: {file_path}")
                
        except Exception as e:
            print(f"   ❌ فشل في حذف {file_path}: {str(e)}")
    
    # حذف ملفات إضافية قد تكون موجودة
    additional_files = [
        'temp_databases_analysis.json',
        'analyze_temp_databases.py',
        'migrate_temp_data.py',
        'apply_migration.py'
    ]
    
    print("\n🧹 تنظيف الملفات المساعدة...")
    
    for file_path in additional_files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                deleted_files.append(file_path)
                print(f"   ✅ تم حذف: {file_path}")
        except Exception as e:
            print(f"   ❌ فشل في حذف {file_path}: {str(e)}")
    
    print(f"\n📊 تم حذف {len(deleted_files)} ملف")
    
    if backup_dir:
        print(f"💾 النسخة الاحتياطية محفوظة في: {backup_dir}")
    
    return deleted_files

def update_gitignore():
    """تحديث ملف .gitignore لتجاهل ملفات SQLite"""
    
    print("\n📝 تحديث ملف .gitignore...")
    
    gitignore_entries = [
        "# SQLite databases",
        "*.db",
        "*.sqlite",
        "*.sqlite3",
        "instance/*.db",
        "",
        "# Temporary migration files",
        "sqlite_backup_*/",
        "temp_databases_analysis.json"
    ]
    
    try:
        gitignore_path = '.gitignore'
        
        # قراءة المحتوى الحالي
        existing_content = ""
        if os.path.exists(gitignore_path):
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                existing_content = f.read()
        
        # التحقق من وجود الإدخالات
        entries_to_add = []
        for entry in gitignore_entries:
            if entry.strip() and entry not in existing_content:
                entries_to_add.append(entry)
        
        if entries_to_add:
            with open(gitignore_path, 'a', encoding='utf-8') as f:
                f.write('\n')
                f.write('\n'.join(entries_to_add))
                f.write('\n')
            
            print(f"   ✅ تم إضافة {len(entries_to_add)} إدخال إلى .gitignore")
        else:
            print("   ✅ .gitignore محدث بالفعل")
            
    except Exception as e:
        print(f"   ❌ خطأ في تحديث .gitignore: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🧹 بدء تنظيف ملفات SQLite المؤقتة")
    print("="*60)
    
    # التحقق من البيانات في PostgreSQL
    if not verify_postgresql_data():
        print("\n❌ لا يمكن المتابعة - البيانات غير متوفرة في PostgreSQL")
        print("   يرجى التأكد من نجاح عملية النقل قبل الحذف")
        return 1
    
    # إنشاء نسخة احتياطية
    backup_dir, backed_up_files = backup_sqlite_files()
    
    if not backed_up_files:
        print("\n⚠️  لم يتم العثور على ملفات SQLite للحذف")
        return 0
    
    # طلب تأكيد من المستخدم
    print(f"\n⚠️  سيتم حذف {len(backed_up_files)} ملف SQLite:")
    for file_path in backed_up_files:
        print(f"   - {file_path}")
    
    print(f"\n💾 النسخة الاحتياطية محفوظة في: {backup_dir}")
    
    # حذف الملفات
    deleted_files = cleanup_sqlite_files(backup_dir, backed_up_files)
    
    # تحديث .gitignore
    update_gitignore()
    
    print(f"\n{'='*60}")
    print("🎉 تم تنظيف ملفات SQLite بنجاح!")
    print(f"📊 تم حذف {len(deleted_files)} ملف")
    print("✅ النظام يستخدم الآن PostgreSQL فقط")
    
    if backup_dir:
        print(f"💾 النسخة الاحتياطية: {backup_dir}")
        print("   يمكنك حذف هذا المجلد لاحقاً إذا كنت متأكداً من عدم الحاجة إليه")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
