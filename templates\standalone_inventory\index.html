{% extends "base.html" %}

{% block title %}المخزون{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="mb-0">
                    <i class="fas fa-warehouse text-primary"></i>
                    المخزون
                </h1>
                <div>
                    <a href="{{ url_for('standalone_inventory.add_item') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة صنف جديد
                    </a>
                    <a href="{{ url_for('standalone_inventory.items') }}" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> البحث في المخزون
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_items }}</h4>
                            <p class="mb-0">إجمالي الأصناف</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ categories_stats.get('ملبوسات', 0) }}</h4>
                            <p class="mb-0">ملبوسات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tshirt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ categories_stats.get('معدات', 0) }}</h4>
                            <p class="mb-0">معدات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tools fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ categories_stats.get('ذخيرة', 0) }}</h4>
                            <p class="mb-0">ذخيرة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bomb fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Add Items Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle"></i>
                        إضافة أصناف جديدة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-success h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-tshirt fa-3x text-success mb-3"></i>
                                    <h5 class="card-title">الملبوسات</h5>
                                    <p class="card-text text-muted">إضافة ملبوسات مثل الزي الرسمي، الأحذية، القبعات</p>
                                    <a href="{{ url_for('standalone_inventory.add_clothing') }}" class="btn btn-success">
                                        <i class="fas fa-plus"></i> إضافة ملبوسات
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-info h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-tools fa-3x text-info mb-3"></i>
                                    <h5 class="card-title">المعدات</h5>
                                    <p class="card-text text-muted">إضافة معدات مثل الخوذات، السترات الواقية، المعدات التقنية</p>
                                    <a href="{{ url_for('standalone_inventory.add_equipment') }}" class="btn btn-info">
                                        <i class="fas fa-plus"></i> إضافة معدات
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-danger h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-bomb fa-3x text-danger mb-3"></i>
                                    <h5 class="card-title">الذخيرة</h5>
                                    <p class="card-text text-muted">إضافة أنواع مختلفة من الذخيرة والمواد المتفجرة</p>
                                    <a href="{{ url_for('standalone_inventory.add_ammunition') }}" class="btn btn-danger">
                                        <i class="fas fa-plus"></i> إضافة ذخيرة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Items -->
    {% if recent_items %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock"></i>
                        آخر الأصناف المضافة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>كود الصنف</th>
                                    <th>اسم الصنف</th>
                                    <th>الفئة</th>
                                    <th>الكمية</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in recent_items %}
                                <tr>
                                    <td><strong>{{ item.item_code }}</strong></td>
                                    <td>{{ item.name }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if item.category == 'ملبوسات' %}bg-success
                                            {% elif item.category == 'معدات' %}bg-info
                                            {% elif item.category == 'ذخيرة' %}bg-danger
                                            {% else %}bg-secondary{% endif %}">
                                            {{ item.category }}
                                        </span>
                                    </td>
                                    <td>{{ item.quantity_in_stock }}</td>
                                    <td>{{ item.created_at.strftime('%Y-%m-%d %H:%M') if item.created_at else '-' }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('standalone_inventory.item_details', item_id=item.id) }}"
                                               class="btn btn-outline-primary" title="التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('standalone_inventory.edit_item', item_id=item.id) }}"
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-danger" title="حذف"
                                                    onclick="confirmDelete({{ item.id }}, '{{ item.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('standalone_inventory.items') }}" class="btn btn-outline-primary">
                            <i class="fas fa-list"></i> عرض جميع الأصناف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تحديث الصفحة كل 5 دقائق للحصول على أحدث الإحصائيات
    setTimeout(function() {
        location.reload();
    }, 300000);
});

// تأكيد الحذف
function confirmDelete(itemId, itemName) {
    if (confirm('هل أنت متأكد من حذف الصنف "' + itemName + '"؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // إنشاء نموذج مخفي لإرسال طلب الحذف
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("standalone_inventory.delete_item", item_id=0) }}'.replace('0', itemId);

        // إضافة CSRF token إذا كان متوفراً
        var csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            var csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
