{% extends "base.html" %}

{% block title %}تعديل الصنف - {{ item.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-edit text-warning"></i>
                    تعديل الصنف: {{ item.name }}
                </h2>
                <div>
                    <a href="{{ url_for('standalone_inventory.item_details', item_id=item.id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للتفاصيل
                    </a>
                    <a href="{{ url_for('standalone_inventory.items') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list"></i> قائمة الأصناف
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        تعديل بيانات الصنف
                        <span class="badge 
                            {% if item.category == 'ملبوسات' %}bg-success
                            {% elif item.category == 'معدات' %}bg-info
                            {% elif item.category == 'ذخيرة' %}bg-danger
                            {% else %}bg-secondary{% endif %}">
                            {{ item.category }}
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.item_code.label(class="form-label required") }}
                                    {{ form.item_code(class="form-control", placeholder="مثال: CLO-001") }}
                                    {% if form.item_code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.item_code.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label required") }}
                                    {{ form.name(class="form-control", placeholder="اسم الصنف") }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.subcategory.label(class="form-label") }}
                                    {{ form.subcategory(class="form-control", placeholder="الفئة الفرعية") }}
                                    {% if form.subcategory.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.subcategory.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.quantity.label(class="form-label required") }}
                                    {{ form.quantity(class="form-control", placeholder="0") }}
                                    {% if form.quantity.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.quantity.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- حقول خاصة بالملبوسات -->
                        {% if item.category == 'ملبوسات' %}
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.size.label(class="form-label") }}
                                    {{ form.size(class="form-control", placeholder="المقاس") }}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.color.label(class="form-label") }}
                                    {{ form.color(class="form-control", placeholder="اللون") }}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.material.label(class="form-label") }}
                                    {{ form.material(class="form-control", placeholder="المادة") }}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- حقول خاصة بالمعدات -->
                        {% if item.category == 'معدات' %}
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.brand.label(class="form-label") }}
                                    {{ form.brand(class="form-control", placeholder="الماركة") }}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.model.label(class="form-label") }}
                                    {{ form.model(class="form-control", placeholder="الموديل") }}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.serial_number.label(class="form-label") }}
                                    {{ form.serial_number(class="form-control", placeholder="الرقم التسلسلي") }}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- حقول خاصة بالذخيرة -->
                        {% if item.category == 'ذخيرة' %}
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.caliber.label(class="form-label") }}
                                    {{ form.caliber(class="form-control", placeholder="العيار") }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.batch_number.label(class="form-label") }}
                                    {{ form.batch_number(class="form-control", placeholder="رقم الدفعة") }}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-group">
                                    {{ form.notes.label(class="form-label") }}
                                    {{ form.notes(class="form-control", rows="3", placeholder="ملاحظات إضافية...") }}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{{ url_for('standalone_inventory.item_details', item_id=item.id) }}" class="btn btn-secondary me-2">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    {{ form.submit(class="btn btn-warning") }}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Info Alert -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> معلومات التعديل:</h6>
                <ul class="mb-0">
                    <li>يمكنك تعديل جميع بيانات الصنف عدا الفئة الرئيسية</li>
                    <li>تأكد من صحة كود الصنف قبل الحفظ</li>
                    <li>سيتم حفظ تاريخ آخر تعديل تلقائياً</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تفعيل التركيز على حقل الاسم
    $('#name').focus();
    
    // تأكيد قبل الحفظ
    $('form').submit(function(e) {
        if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
