# 🛡️ دليل التحديث الآمن لقاعدة البيانات

## 📋 نظرة عامة

هذه مجموعة من السكريبتات الآمنة لتحديث قاعدة البيانات **بدون فقدان أي بيانات موجودة**. السكريبتات تقوم بإضافة الجداول والأعمدة الناقصة فقط.

## 🔧 الملفات المتوفرة

### 1. `check_existing_data.py`
- **الغرض**: فحص البيانات الموجودة في قاعدة البيانات
- **الوظيفة**: عرض تقرير مفصل عن جميع البيانات المحفوظة
- **الأمان**: لا يقوم بأي تعديل، فقط قراءة

### 2. `safe_database_update.py`
- **الغرض**: تحديث مخطط قاعدة البيانات بأمان
- **الوظيفة**: إضافة الجداول والأعمدة الناقصة فقط
- **الأمان**: لا يحذف أو يعدل أي بيانات موجودة

## 🚀 كيفية الاستخدام

### الخطوة 1: فحص البيانات الموجودة
```bash
python check_existing_data.py
```

**ما يحدث:**
- ✅ يعرض تقرير شامل عن البيانات الموجودة
- ✅ يظهر عدد الصفوف في كل جدول
- ✅ يعرض عينة من البيانات المحفوظة
- ✅ لا يقوم بأي تعديل

**مثال على الناتج:**
```
📊 users (المستخدمين): 5 صف
📊 receipts_patrol_data (بيانات جدول الدوريات): 12 صف
📊 receipts_shifts_data (بيانات جدول المناوبين): 8 صف
```

### الخطوة 2: التحديث الآمن
```bash
python safe_database_update.py
```

**ما يحدث:**
- 🔍 يفحص حالة قاعدة البيانات أولاً
- ❓ يطلب تأكيد منك قبل المتابعة
- ➕ يضيف الجداول الناقصة فقط
- ➕ يضيف الأعمدة الناقصة فقط
- 🛡️ يحافظ على جميع البيانات الموجودة

## 🔒 ضمانات الأمان

### ✅ ما يفعله السكريبت:
- إضافة جداول جديدة إذا لم تكن موجودة
- إضافة أعمدة جديدة إذا لم تكن موجودة
- إنشاء فهارس للأداء
- عرض تقرير مفصل عن التغييرات

### ❌ ما لا يفعله السكريبت:
- **لا يحذف** أي جداول موجودة
- **لا يحذف** أي أعمدة موجودة
- **لا يعدل** أي بيانات موجودة
- **لا يغير** أي إعدادات موجودة

## 📊 الجداول التي يتم إضافتها (إذا لم تكن موجودة)

### جداول كشف الاستلامات:
- `receipts_patrol_data` - بيانات جدول الدوريات
- `receipts_shifts_data` - بيانات جدول المناوبين
- `receipts_patrol_locations` - مواقع الدوريات
- `receipts_shifts_locations` - مواقع المناوبين

### أعمدة إضافية:
- `locations.instructions_pdf` - ملفات PDF للتعليمات
- `locations.pdf_filename` - أسماء ملفات PDF
- `users.last_login` - آخر تسجيل دخول
- `users.is_active` - حالة المستخدم

## 🔧 متطلبات التشغيل

### 1. Python وحزم مطلوبة:
```bash
pip install psycopg2-binary
```

### 2. إعدادات قاعدة البيانات:
- **الخادم**: localhost
- **المنفذ**: 5432
- **قاعدة البيانات**: military_warehouse
- **المستخدم**: postgres
- **كلمة المرور**: postgres

## 🚨 تعليمات مهمة

### قبل التشغيل:
1. **تأكد من إغلاق الموقع** أثناء التحديث
2. **قم بنسخة احتياطية** من قاعدة البيانات (اختياري للأمان الإضافي)
3. **تأكد من صحة إعدادات قاعدة البيانات**

### أثناء التشغيل:
1. **اقرأ التقارير** التي تظهر بعناية
2. **أكد المتابعة** فقط إذا كنت متأكد
3. **لا تقاطع العملية** أثناء التنفيذ

### بعد التشغيل:
1. **تحقق من التقرير النهائي**
2. **شغل الموقع** وتأكد من عمله بشكل طبيعي
3. **تحقق من البيانات** في الصفحات المختلفة

## 🔍 استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات:
```
❌ خطأ في الاتصال بقاعدة البيانات
```
**الحل:**
- تأكد من تشغيل PostgreSQL
- تحقق من إعدادات الاتصال
- تأكد من صحة كلمة المرور

### خطأ في الصلاحيات:
```
❌ خطأ في الصلاحيات
```
**الحل:**
- تأكد من صلاحيات المستخدم postgres
- شغل السكريبت كمدير

## 📞 الدعم

إذا واجهت أي مشاكل:
1. **شغل** `check_existing_data.py` أولاً لفهم حالة قاعدة البيانات
2. **احفظ** رسائل الخطأ كاملة
3. **تأكد** من إعدادات قاعدة البيانات
4. **اطلب المساعدة** مع تفاصيل الخطأ

## ✅ مثال على التشغيل الناجح

```bash
$ python check_existing_data.py
🔍 تقرير شامل عن البيانات الموجودة في قاعدة البيانات
📊 users (المستخدمين): 5 صف
📊 receipts_patrol_data (بيانات جدول الدوريات): 12 صف
✅ قاعدة البيانات تحتوي على بيانات مهمة!

$ python safe_database_update.py
🛡️ سكريبت التحديث الآمن لقاعدة البيانات
📋 إنشاء جدول جديد: receipts_shifts_locations
✅ تم التحديث بنجاح!
📱 جميع البيانات الموجودة محفوظة ولم تتأثر
```

---

**🔒 تذكر: هذه السكريبتات مصممة لتكون آمنة تماماً ولن تؤثر على بياناتك الموجودة!**
