<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات الأخيرة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #28a745 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .fix-section {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(40, 167, 69, 0.8); 
            border-left-color: #28a745;
        }
        .error { 
            background: rgba(220, 53, 69, 0.8); 
            border-left-color: #dc3545;
        }
        .warning { 
            background: rgba(255, 193, 7, 0.8); 
            color: #000; 
            border-left-color: #ffc107;
        }
        .info { 
            background: rgba(23, 162, 184, 0.8); 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #dc3545, #28a745);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }
        .log {
            background: rgba(0,0,0,0.4);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .fix-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .fix-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .emoji {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .code-block {
            background: rgba(0,0,0,0.4);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            border-left: 3px solid #28a745;
        }
        .error-block {
            background: rgba(220, 53, 69, 0.2);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            border-left: 3px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح الأخطاء الأخيرة</h1>
        <p>تم إصلاح الأخطاء المتبقية في النظام</p>
        
        <div class="fix-grid">
            <div class="fix-card">
                <div class="emoji">🔧</div>
                <h3>إصلاح دوال الأفراد</h3>
                <p>تم توحيد دوال تحميل الأفراد</p>
            </div>
            <div class="fix-card">
                <div class="emoji">📊</div>
                <h3>إصلاح الفهرسة</h3>
                <p>تم إصلاح خطأ الوصول للمصفوفات</p>
            </div>
            <div class="fix-card">
                <div class="emoji">➕</div>
                <h3>إضافة الصفوف</h3>
                <p>إضافة الصفوف تعمل الآن بشكل صحيح</p>
            </div>
            <div class="fix-card">
                <div class="emoji">📋</div>
                <h3>جميع الجداول</h3>
                <p>الرئيسي، الدوريات، والمناوبين</p>
            </div>
        </div>
        
        <div class="fix-section">
            <h3>❌ الأخطاء التي تم إصلاحها</h3>
            <div class="error-block">
<strong>1. خطأ دالة غير موجودة:</strong>
❌ loadPatrolPersonnelForLocation is not defined
❌ loadShiftsPersonnelForLocation is not defined

<strong>الحل:</strong>
✅ تم توحيد جميع الدوال لتستخدم loadPersonnelForLocation

<strong>2. خطأ الفهرسة:</strong>
❌ Cannot set properties of undefined (setting '0')

<strong>الحل:</strong>
✅ تم إضافة فحص وإنشاء الصفوف المفقودة تلقائياً
            </div>
        </div>
        
        <div class="fix-section">
            <h3>✅ الإصلاحات المطبقة</h3>
            <div class="code-block">
<strong>1. توحيد دوال تحميل الأفراد:</strong>
// قبل الإصلاح - دوال غير موجودة
loadPatrolPersonnelForLocation(location.id, rowIndex); // ❌
loadShiftsPersonnelForLocation(location.id, rowIndex); // ❌

// بعد الإصلاح - دالة موحدة
loadPersonnelForLocation(location.id, rowIndex); // ✅

<strong>2. إصلاح مشكلة الفهرسة:</strong>
// قبل الإصلاح - خطأ في الوصول
dutyData.rows[rowIndex][0] = value; // ❌ إذا لم يكن الصف موجود

// بعد الإصلاح - فحص وإنشاء آمن
while (dutyData.rows.length < rows.length) {
    dutyData.rows.push(Array(dutyData.headers.length).fill(''));
}
if (!dutyData.rows[rowIndex]) {
    dutyData.rows[rowIndex] = Array(dutyData.headers.length).fill('');
}
dutyData.rows[rowIndex][0] = value; // ✅ آمن الآن

<strong>3. تطبيق الإصلاح على جميع الجداول:</strong>
✅ الجدول الرئيسي (dutyData)
✅ جدول الدوريات (patrolData)  
✅ جدول المناوبين (shiftsData)
            </div>
        </div>
        
        <div class="fix-section">
            <h3>🧪 اختبار الإصلاحات</h3>
            <div>
                <button onclick="openDuties()" class="primary">📋 فتح كشف الواجبات</button>
                <button onclick="testAddRow()">➕ اختبار إضافة صف</button>
                <button onclick="testAddColumn()">📊 اختبار إضافة عمود</button>
                <button onclick="testAllTables()">📋 اختبار جميع الجداول</button>
            </div>
        </div>
        
        <div class="fix-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testStatus">
                <div class="status info">⏳ جاري انتظار بدء الاختبار...</div>
            </div>
        </div>
        
        <div class="fix-section">
            <h3>📋 سجل الاختبار</h3>
            <div id="testLog" class="log">
                جاري تحميل أدوات الاختبار...
            </div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
        </div>
        
        <div class="fix-section">
            <h3>📖 تعليمات الاختبار</h3>
            <div class="code-block">
<strong>خطوات الاختبار:</strong>

1. افتح صفحة كشف الواجبات
2. افتح وحدة التحكم (F12) لمراقبة الأخطاء
3. اختبر الجدول الرئيسي:
   - املأ بعض البيانات
   - اضغط "إضافة صف" - يجب ألا ترى أخطاء
   - اضغط "إضافة عمود" - يجب ألا ترى أخطاء

4. اختبر جدول الدوريات:
   - املأ بعض البيانات
   - اضغط "إضافة صف" - يجب ألا ترى أخطاء

5. اختبر جدول المناوبين:
   - املأ بعض البيانات
   - اضغط "إضافة صف" - يجب ألا ترى أخطاء

<strong>علامات النجاح:</strong>
✅ لا توجد رسائل خطأ في وحدة التحكم
✅ الصفوف والأعمدة تُضاف بنجاح
✅ البيانات تبقى محفوظة
✅ اختيار الأفراد يعمل في الصفوف الجديدة
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = `<div style="color: ${colors[type]}; margin: 3px 0; padding: 8px; border-left: 3px solid ${colors[type]}; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += entry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('testStatus');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function openDuties() {
            window.open('/duties/', '_blank');
            log('🔗 تم فتح صفحة كشف الواجبات', 'info');
            updateStatus('تم فتح صفحة كشف الواجبات - ابدأ الاختبار', 'info');
        }
        
        function testAddRow() {
            log('🧪 اختبار إضافة صف...', 'info');
            log('1. افتح صفحة كشف الواجبات', 'info');
            log('2. املأ بعض البيانات في الصف الأول', 'info');
            log('3. اضغط على زر "إضافة صف" (أيقونة +)', 'info');
            log('4. راقب وحدة التحكم - يجب ألا ترى أخطاء', 'info');
            log('5. تأكد من إضافة صف جديد والحفاظ على البيانات', 'info');
            updateStatus('جاري اختبار إضافة الصفوف...', 'warning');
        }
        
        function testAddColumn() {
            log('🧪 اختبار إضافة عمود...', 'info');
            log('1. في صفحة كشف الواجبات', 'info');
            log('2. املأ بعض البيانات', 'info');
            log('3. اضغط على زر "إضافة عمود" في رأس الجدول', 'info');
            log('4. راقب وحدة التحكم - يجب ألا ترى أخطاء', 'info');
            log('5. تأكد من إضافة عمود جديد والحفاظ على البيانات', 'info');
            updateStatus('جاري اختبار إضافة الأعمدة...', 'warning');
        }
        
        function testAllTables() {
            log('🧪 اختبار جميع الجداول...', 'info');
            updateStatus('جاري اختبار جميع الجداول...', 'info');
            
            const tables = [
                'الجدول الرئيسي (كشف الواجبات)',
                'جدول الدوريات',
                'جدول المناوبين'
            ];
            
            tables.forEach((table, index) => {
                setTimeout(() => {
                    log(`${index + 1}. اختبار ${table}...`, 'info');
                    log(`   - املأ البيانات في ${table}`, 'info');
                    log(`   - اضغط "إضافة صف" في ${table}`, 'info');
                    log(`   - تأكد من عدم وجود أخطاء`, 'info');
                    
                    if (index === tables.length - 1) {
                        setTimeout(() => {
                            log('✅ تم إكمال اختبار جميع الجداول', 'success');
                            updateStatus('تم إكمال اختبار جميع الجداول', 'success');
                        }, 1000);
                    }
                }, (index + 1) * 2000);
            });
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('🧹 تم مسح السجل', 'info');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل أداة اختبار الإصلاحات الأخيرة', 'success');
            updateStatus('أداة الاختبار جاهزة', 'info');
            
            log('🔧 الإصلاحات المطبقة:', 'info');
            log('✅ إصلاح خطأ loadPatrolPersonnelForLocation is not defined', 'success');
            log('✅ إصلاح خطأ loadShiftsPersonnelForLocation is not defined', 'success');
            log('✅ إصلاح خطأ Cannot set properties of undefined', 'success');
            log('✅ تحسين فحص وإنشاء الصفوف في المصفوفات', 'success');
            log('✅ تطبيق الإصلاحات على جميع الجداول', 'success');
            
            log('📋 النظام جاهز للاختبار!', 'info');
            log('⚠️ تأكد من فتح وحدة التحكم (F12) لمراقبة الأخطاء', 'warning');
        });
    </script>
</body>
</html>
