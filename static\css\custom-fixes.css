/* إصلاحات مخصصة لضمان عمل القوائم المنسدلة بشكل صحيح */

/* تأكيد مواضع القوائم المنسدلة */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: auto;
    right: 0;
    z-index: 1000;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 1rem;
    color: #212529;
    text-align: right;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.25rem;
    display: none;
}

/* تأكيد عرض القوائم المنسدلة عند التفعيل */
.dropdown-menu.show {
    display: block !important;
}

/* تأكيد تصميم عناصر القائمة المنسدلة */
.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.25rem 1.5rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

.dropdown-item:hover, .dropdown-item:focus {
    color: #16181b;
    text-decoration: none;
    background-color: #f8f9fa;
}

/* التأكد من أن زر التبديل مرئي دائمًا */
.nav-link.dropdown-toggle, 
.btn.nav-link {
    cursor: pointer;
    display: inline-block;
}

/* إصلاح مواضع القوائم المنسدلة على الشاشات الصغيرة */
@media (max-width: 768px) {
    .dropdown-menu {
        position: absolute;
        left: 0;
        right: auto;
    }
}

/* تأكيد تنسيق المستند */
html[dir="rtl"] .dropdown-menu {
    text-align: right;
    left: auto;
    right: 0;
}

/* تعزيز الرؤية للقوائم المنسدلة */
.dropdown-menu {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* تنسيق المنسدلة بطريقة تضمن رؤيتها دائمًا */
.navbar .dropdown-menu {
    position: absolute;
    top: 100%;
    left: auto;
    right: 0;
    float: left;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* إزالة التأثير الافتراضي للنقر على العناصر */
.nav-link, .dropdown-toggle {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
