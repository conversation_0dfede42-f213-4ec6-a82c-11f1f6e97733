/* ===== Main Stylesheet ===== */
/* Author: Military Warehouse Management System
   Description: Custom CSS for the Military Warehouse Management System
*/

/* ===== Variables ===== */
:root {
  /* Dark Theme Colors (Default) */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2d2d2d;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --accent-color: #0d6efd;
  --accent-hover: #0b5ed7;
  --success-color: #28a745; /* لون أخضر لحالة نشط */
  --danger-color: #dc3545; /* لون أحمر لحالة دورة */
  --warning-color: #ffc107; /* لون أصفر لحالة إجازة */
  --info-color: #0dcaf0;
  --mission-color: #fd7e14; /* لون برتقالي لحالة مهمة */
  --recipient-color: #0d6efd; /* لون أزرق لحالة مستلم */
  --maintenance-color: #FAAFBE; /* لون وردي لحالة صيانة */
  --vacant-color: #6c757d; /* لون رمادي لحالة شاغر */
  --shooting-color: #C8BBBE; /* لون Lavender Blush3 لحالة رماية */
  --border-color: #404040;

  /* Light Theme Colors */
  --light-bg-primary: #ffffff;
  --light-bg-secondary: #f0f2f5;
  --light-bg-tertiary: #e4e6e9;
  --light-text-primary: #1a1a1a;
  --light-text-secondary: #4a4a4a;
  --light-border-color: #cfd4da;
  --light-accent-color: #0d6efd;
  --light-accent-hover: #0b5ed7;
  --light-success-color: #28a745; /* لون أخضر لحالة نشط */
  --light-danger-color: #dc3545; /* لون أحمر لحالة دورة */
  --light-warning-color: #ffc107; /* لون أصفر لحالة إجازة */
  --light-info-color: #0dcaf0;
  --light-mission-color: #fd7e14; /* لون برتقالي لحالة مهمة */
  --light-recipient-color: #0d6efd; /* لون أزرق لحالة مستلم */
  --light-maintenance-color: #FAAFBE; /* لون وردي لحالة صيانة */
  --light-vacant-color: #6c757d; /* لون رمادي لحالة شاغر */
  --light-shooting-color: #C8BBBE; /* لون Lavender Blush3 لحالة رماية */
}

/* Light Theme Override */
body.light-theme {
  --bg-primary: var(--light-bg-primary);
  --bg-secondary: var(--light-bg-secondary);
  --bg-tertiary: var(--light-bg-tertiary);
  --text-primary: var(--light-text-primary);
  --text-secondary: var(--light-text-secondary);
  --border-color: var(--light-border-color);
  --accent-color: var(--light-accent-color);
  --accent-hover: var(--light-accent-hover);
  --success-color: var(--light-success-color);
  --danger-color: var(--light-danger-color);
  --warning-color: var(--light-warning-color);
  --info-color: var(--light-info-color);
  --mission-color: var(--light-mission-color);
  --recipient-color: var(--light-recipient-color);
  --maintenance-color: var(--light-maintenance-color);
  --shooting-color: var(--light-shooting-color);
}

/* تنسيقات إضافية لضمان التباين المناسب في الوضع الليلي */
body:not(.light-theme) .btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

body:not(.light-theme) .btn-secondary {
  background-color: #4b4b4b;
  border-color: #4b4b4b;
  color: var(--text-primary);
}

/* تنسيقات إضافية لضمان التباين المناسب في الوضع النهاري */
body.light-theme .btn-primary {
  background-color: var(--light-accent-color);
  border-color: var(--light-accent-color);
  color: white;
}

body.light-theme .btn-secondary {
  background-color: #e0e0e0;
  border-color: #d0d0d0;
  color: var(--light-text-primary);
}

/* تنسيقات الأيقونات في الوضع النهاري */
body.light-theme .fas,
body.light-theme .far,
body.light-theme .fab {
  color: inherit;
}

/* ===== General Styles ===== */
html {
  direction: rtl;
  font-family: "Cairo", "Tajawal", sans-serif;
}

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  font-family: "Cairo", "Tajawal", sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Set up scrollbar styling for dark theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-color);
}

a {
  color: var(--accent-color);
  transition: color 0.2s ease;
}

a:hover {
  color: var(--accent-hover);
  text-decoration: none;
}

/* ===== Typography ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 1rem;
  font-weight: 700;
}

.text-primary {
  color: var(--accent-color) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-success {
  color: var(--success-color) !important;
}

.text-danger {
  color: var(--danger-color) !important;
}

.text-warning {
  color: var(--warning-color) !important;
}

.text-info {
  color: var(--info-color) !important;
}

/* ===== Layout ===== */
.container,
.container-fluid {
  padding: 20px;
}

.row {
  margin-left: -10px;
  margin-right: -10px;
}

.col,
.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12,
.col-sm,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-md,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-lg,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-xl,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12 {
  padding-left: 10px;
  padding-right: 10px;
}

/* ===== Navigation ===== */
.sidebar {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 250px;
  background-color: var(--bg-secondary);
  border-left: 1px solid var(--border-color);
  padding: 10px 0;
  z-index: 1000;
  transition: all 0.3s ease;
  overflow-y: auto;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-brand {
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 15px;
}

.sidebar-brand img {
  display: block;
  transition: transform 0.3s ease;
  margin: 0 auto;
}

.sidebar-brand img:hover {
  transform: scale(1.05);
}

.sidebar-brand h3 {
  font-family: 'Cairo', sans-serif;
  font-weight: 600;
  font-size: 1.3rem;
  margin: 10px 0 5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.sidebar.collapsed .sidebar-brand h3 {
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

.sidebar-nav {
  padding: 0;
  list-style: none;
  margin-top: 10px;
}

.sidebar-nav .nav-item {
  margin-bottom: 2px;
}

.sidebar-nav .nav-link {
  padding: 8px 20px;
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.sidebar-nav .nav-link i {
  margin-left: 8px;
  font-size: 1.1em;
  width: 24px;
  text-align: center;
}

.sidebar-nav .nav-link span {
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-nav .nav-link span {
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

.main-content {
  margin-right: 250px;
  padding: 20px;
  transition: margin-right 0.3s ease;
}

.main-content.expanded {
  margin-right: 80px;
}

.navbar {
  background-color: var(--bg-secondary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  padding: 10px 20px;
}

.navbar-brand {
  display: flex;
  align-items: center;
}

.navbar-brand img {
  max-height: 30px;
  max-width: 30px;
  margin-left: 10px;
}

.navbar-nav .nav-link {
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--text-primary);
}

.toggle-sidebar {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.2em;
  cursor: pointer;
  padding: 5px;
  margin-left: 15px;
}

.toggle-sidebar:hover {
  color: var(--text-primary);
}

/* ===== Cards ===== */
.card {
  background-color: #242424;
  border: none;
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #1a1a1a;
  border-bottom: none;
  padding: 15px 20px;
  font-weight: 600;
  border-radius: 6px 6px 0 0;
}

.card-body {
  padding: 20px;
}

.card-footer {
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  padding: 15px 20px;
}

/* ===== Stat Cards ===== */
.stat-card {
  display: flex;
  align-items: center;
}

.stat-card .icon {
  font-size: 2.5em;
  margin-left: 20px;
  color: var(--accent-color);
}

.stat-card .info h4 {
  margin: 0;
  font-size: 1.8em;
  font-weight: 700;
}

.stat-card .info span {
  font-size: 0.9em;
  color: var(--text-secondary);
}

/* ===== Buttons ===== */
.btn {
  border-radius: 4px;
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-primary:hover {
  background-color: var(--accent-hover);
  border-color: var(--accent-hover);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background-color: var(--border-color);
  border-color: var(--text-secondary);
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: #212529;
}

.btn-info {
  background-color: var(--info-color);
  border-color: var(--info-color);
}

/* ===== Dropdown Menus ===== */
.dropdown-menu {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.dropdown-item {
  color: var(--text-primary) !important;
  background-color: transparent !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

.dropdown-item.active,
.dropdown-item:active {
  background-color: var(--accent-color) !important;
  color: white !important;
}

.dropdown-divider {
  border-color: var(--border-color) !important;
}

.btn-outline-primary {
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-outline-primary:hover {
  background-color: var(--accent-color);
  color: white;
}

.btn-outline-secondary {
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.btn-outline-secondary:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* ===== Forms ===== */
.form-control {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  border-radius: 4px;
  padding: 8px 12px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  background-color: var(--bg-tertiary);
  border-color: var(--accent-color);
  color: var(--text-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control::placeholder {
  color: var(--text-secondary) !important;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary) !important;
}

.input-group-text {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-secondary) !important;
}

/* تحسينات إضافية للقوائم المنسدلة */
select {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

select:focus {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--accent-color) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

select option {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  padding: 8px;
}

.form-select {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.form-select:focus {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--accent-color) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

.form-select option {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* ===== Tables ===== */
.table {
  color: var(--text-primary);
}

.table thead th {
  border-bottom: 2px solid var(--border-color);
  border-top: none;
  background-color: var(--bg-tertiary);
}

.table td,
.table th {
  padding: 12px;
  vertical-align: middle;
  border-top: 1px solid var(--border-color);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.table-bordered {
  border: 1px solid var(--border-color);
}

.table-bordered td,
.table-bordered th {
  border: 1px solid var(--border-color);
}

/* ===== Alerts ===== */
.alert {
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
}

.alert-primary {
  background-color: rgba(0, 123, 255, 0.45);
  border-color: rgba(0, 123, 255, 0.6);
  color: #cce5ff;
}

.alert-secondary {
  background-color: rgba(108, 117, 125, 0.45);
  border-color: rgba(108, 117, 125, 0.6);
  color: #d6d8db;
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.45);
  border-color: rgba(40, 167, 69, 0.6);
  color: #c3e6cb;
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.45);
  border-color: rgba(220, 53, 69, 0.6);
  color: #f8d7da;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.45);
  border-color: rgba(255, 193, 7, 0.6);
  color: #ffeeba;
}

.alert-info {
  background-color: rgba(23, 162, 184, 0.45);
  border-color: rgba(23, 162, 184, 0.6);
  color: #bee5eb;
}

/* ===== Badges ===== */
.badge {
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: 600;
}

.badge-primary {
  background-color: var(--accent-color);
}

.badge-secondary {
  background-color: var(--text-secondary);
}

.badge-success {
  background-color: var(--success-color);
}

.badge-danger {
  background-color: var(--danger-color);
}

.badge-warning {
  background-color: var(--warning-color);
  color: #212529;
}

.badge-info {
  background-color: var(--info-color);
}

.badge-mission {
  background-color: var(--mission-color);
  color: white;
}

.badge-dark {
  background-color: #343a40;
  color: white;
}

.badge-recipient {
  background-color: var(--recipient-color);
  color: white;
}

.badge-maintenance {
  background-color: var(--maintenance-color);
  color: black;
}

.badge-cycle {
  background-color: var(--danger-color);
  color: white;
}

.badge-vacant {
  background-color: var(--vacant-color);
  color: white;
}

.badge-shooting {
  background-color: var(--shooting-color);
  color: black;
}

/* ===== Quick Actions ===== */
.list-group-item-action {
  transition: all 0.2s ease;
  padding: 12px 16px;
  background-color: #2d2d2d;
  border: none;
  margin-bottom: 2px;
}

.list-group-item-action:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: var(--light-bg-secondary);
  z-index: 1;
}

.list-group-item-action i {
  font-size: 1.2em;
  width: 24px;
  text-align: center;
}

.list-group-item-action span {
  font-weight: 500;
}

body.light-theme .list-group-item {
  background-color: white;
  border-color: var(--light-border-color);
}

body.light-theme .list-group-item-action:hover {
  background-color: var(--light-bg-secondary);
}

.quick-action.success i {
  color: var(--success-color);
}

.quick-action.warning i {
  color: var(--warning-color);
}

.quick-action.info i {
  color: var(--info-color);
}

.quick-action.danger i {
  color: var(--danger-color);
}

/* ===== Utility Classes ===== */
.border {
  border: 1px solid var(--border-color) !important;
}

.border-top {
  border-top: 1px solid var(--border-color) !important;
}

.border-right {
  border-right: 1px solid var(--border-color) !important;
}

.border-bottom {
  border-bottom: 1px solid var(--border-color) !important;
}

.border-left {
  border-left: 1px solid var(--border-color) !important;
}

/* ===== Search Box ===== */
.search-box {
  position: relative;
  margin-bottom: 20px;
}

.search-box .form-control {
  padding-right: 40px;
  border-radius: 20px;
}

.search-box .search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

/* ===== Dashboard Specific ===== */
.status-card {
  border-right: 4px solid var(--accent-color);
  transition: all 0.2s ease;
}

.status-card:hover {
  border-right-width: 8px;
}

.status-card.active {
  border-right-color: var(--success-color);
}

.status-card.leave {
  border-right-color: var(--warning-color);
}

.status-card.mission {
  border-right-color: var(--mission-color);
}

.status-card.maintenance {
  border-right-color: var(--maintenance-color);
}

.status-card.cycle {
  border-right-color: var(--danger-color);
}

.status-card.vacant {
  border-right-color: var(--vacant-color);
}

.status-card.recipient {
  border-right-color: var(--recipient-color);
}

.status-card.shooting {
  border-right-color: var(--shooting-color);
}

.status-card.damaged {
  border-right-color: var(--danger-color);
}

/* ===== Charts ===== */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* ===== Pagination ===== */
.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 4px;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: var(--accent-color);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

.page-link:hover {
  z-index: 2;
  color: var(--accent-hover);
  text-decoration: none;
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.page-link:focus {
  z-index: 3;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.page-item.disabled .page-link {
  color: var(--text-secondary);
  pointer-events: none;
  cursor: auto;
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

/* ===== Dropdown Menu ===== */
.dropdown-menu:not(.datepicker) {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  left: auto !important;
  right: auto !important;
  position: absolute !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.dropdown-item {
  color: var(--text-primary) !important;
  background-color: transparent !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

.dropdown-item.active,
.dropdown-item:active {
  background-color: var(--accent-color) !important;
  color: white !important;
}

/* ===== Modal ===== */
.modal-content {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

.modal-header {
  border-bottom: 1px solid var(--border-color);
}

.modal-footer {
  border-top: 1px solid var(--border-color);
}

.modal-title {
  color: var(--text-primary);
}

.close {
  color: var(--text-secondary);
  text-shadow: none;
}

.close:hover {
  color: var(--text-primary);
}

/* ===== Login Page ===== */
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: var(--bg-primary);
}

.login-card {
  width: 400px;
  max-width: 90%;
  padding: 30px;
  margin: 0 auto;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  text-align: center;
  text-align: center;
}

.login-logo {
  text-align: center;
  margin-bottom: 30px;
}

.login-logo img {
  max-width: 100px;
  max-height: 100px;
}

/* ===== Display Screens ===== */
.display-screen {
  background-color: var(--bg-primary);
  min-height: 100vh;
  padding: 0;
}

.display-header {
  background: linear-gradient(
    to bottom,
    var(--bg-secondary),
    var(--bg-primary)
  );
  padding: 20px;
  border-bottom: 2px solid var(--accent-color);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo {
  width: 80px;
  height: auto;
}

.time-section {
  text-align: left;
}

.current-time {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--accent-color);
}

.current-date {
  font-size: 1.2rem;
  color: var(--text-secondary);
}

.status-banner {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.05);
  padding: 15px;
  border-radius: 10px;
  margin-top: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-item i {
  font-size: 1.5rem;
  color: var(--accent-color);
}

.stat-panel {
  background: var(--bg-secondary);
  border-radius: 15px;
  margin: 20px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat-panel:hover {
  transform: translateY(-5px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.panel-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.panel-header .count {
  font-size: 2rem;
  font-weight: bold;
  color: var(--accent-color);
}

.display-header {
  text-align: center;
  margin-bottom: 30px;
}

.display-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.display-stat {
  text-align: center;
  font-size: 1.2rem;
}

.display-stat h2 {
  font-size: 3rem;
  margin: 10px 0;
}

.display-stat p {
  color: var(--text-secondary);
}

/* ===== QR Code ===== */
.qr-code-container {
  text-align: center;
  margin: 20px 0;
}

.qr-code-container img {
  max-width: 200px;
  max-height: 200px;
  border: 10px solid white;
  border-radius: 4px;
}

/* ===== Light Theme ===== */
body.light-theme {
  background-color: var(--light-bg-primary);
  color: var(--light-text-primary);
}

body.light-theme .sidebar {
  background-color: var(--light-bg-secondary);
  border-left-color: var(--light-border-color);
}

body.light-theme .sidebar-nav .nav-link {
  color: var(--light-text-secondary);
}

body.light-theme .sidebar-nav .nav-link:hover,
body.light-theme .sidebar-nav .nav-link.active {
  background-color: var(--light-bg-tertiary);
  color: var(--light-text-primary);
}

body.light-theme .card {
  background-color: white;
  border: 1px solid var(--light-border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

body.light-theme .card-header,
body.light-theme .card-footer {
  background-color: var(--light-bg-secondary);
  border-color: var(--light-border-color);
}

body.light-theme .table {
  color: var(--light-text-primary);
}

body.light-theme .table thead th {
  border-bottom-color: var(--light-border-color);
  background-color: var(--light-bg-secondary);
}

body.light-theme .table td,
body.light-theme .table th {
  border-top-color: var(--light-border-color);
}

body.light-theme .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

body.light-theme .table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

body.light-theme .form-control {
  background-color: white;
  border-color: var(--light-border-color);
  color: var(--light-text-primary);
}

body.light-theme .form-control:focus {
  background-color: white;
  color: var(--light-text-primary);
}

body.light-theme .form-control::placeholder {
  color: var(--light-text-secondary);
}

body.light-theme .input-group-text {
  background-color: var(--light-bg-secondary);
  border-color: var(--light-border-color);
  color: var(--light-text-secondary);
}

body.light-theme .navbar {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

body.light-theme .navbar-nav .nav-link {
  color: var(--light-text-secondary);
}

body.light-theme .navbar-nav .nav-link:hover,
body.light-theme .navbar-nav .nav-link.active {
  color: var(--light-text-primary);
}

body.light-theme .page-link {
  background-color: white;
  border-color: var(--light-border-color);
}

body.light-theme .page-link:hover {
  background-color: var(--light-bg-secondary);
  border-color: var(--light-border-color);
}

body.light-theme .modal-content {
  background-color: white;
  border-color: var(--light-border-color);
}

body.light-theme .modal-header,
body.light-theme .modal-footer {
  border-color: var(--light-border-color);
}

body.light-theme .close {
  color: var(--light-text-secondary);
}

body.light-theme .close:hover {
  color: var(--light-text-primary);
}

body.light-theme .sidebar-brand {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

body.light-theme .sidebar-brand h3 {
  color: #333333;
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 992px) {
  .sidebar {
    width: 70px;
  }

  .sidebar .sidebar-brand h3,
  .sidebar .nav-link span {
    display: none;
  }

  .main-content {
    margin-right: 70px;
  }

  .toggle-sidebar {
    display: none;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    border-left: none;
    border-bottom: 1px solid var(--border-color);
  }

  .sidebar .sidebar-brand h3,
  .sidebar .nav-link span {
    display: inline;
  }

  .sidebar-nav {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  .sidebar-nav .nav-item {
    width: auto;
  }

  .main-content {
    margin-right: 0;
  }

  .display-stat h2 {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .card-body {
    padding: 15px;
  }

  .display-header h1 {
    font-size: 1.8rem;
  }

  .display-stat h2 {
    font-size: 1.5rem;
  }
}

/* ===== منع النقرات المتعددة ===== */
.dropdown-item.clicked,
.dropdown-item.opening {
  pointer-events: none !important;
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* ===== منع النقرات المتعددة ===== */
.dropdown-item.clicked,
.dropdown-item.opening {
  pointer-events: none !important;
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}
