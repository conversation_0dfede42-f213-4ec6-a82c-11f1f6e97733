"""initial migration

Revision ID: 8727fcabb738
Revises: 
Create Date: 2025-05-01 19:12:07.063624

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8727fcabb738'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_warehouse', schema=None) as batch_op:
        batch_op.drop_constraint('user_warehouse_user_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('user_warehouse_warehouse_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'])

    with op.batch_alter_table('warehouses', schema=None) as batch_op:
        batch_op.alter_column('location',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=200),
               existing_nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('warehouses', schema=None) as batch_op:
        batch_op.alter_column('location',
               existing_type=sa.String(length=200),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)

    with op.batch_alter_table('user_warehouse', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_warehouse_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('user_warehouse_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    # ### end Alembic commands ###
