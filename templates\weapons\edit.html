{% extends "base.html" %}

{% block content %}

<div class="row mb-4">
    <div class="col-md-8">
        <h3>تعديل بيانات السلاح</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('weapons.details', weapon_id=weapon.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل السلاح
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-crosshairs"></i> بيانات السلاح</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('weapons.edit', weapon_id=weapon.id) }}" enctype="multipart/form-data">
            {{ form.hidden_tag() }}
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.storage_number.label }}
                        {{ form.storage_number(class="form-control") }}
                        {% if form.storage_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.storage_number.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.serial_number.label }}
                        {{ form.serial_number(class="form-control") }}
                        {% if form.serial_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.serial_number.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.rifle_number.label }}
                        {{ form.rifle_number(class="form-control") }}
                        {% if form.rifle_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.rifle_number.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.rifle_type.label }}
                        {{ form.rifle_type(class="form-control") }}
                        {% if form.rifle_type.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.rifle_type.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.pistol_number.label }}
                        {{ form.pistol_number(class="form-control") }}
                        {% if form.pistol_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.pistol_number.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.pistol_type.label }}
                        {{ form.pistol_type(class="form-control") }}
                        {% if form.pistol_type.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.pistol_type.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.status.label }}
                        {{ form.status(class="form-control") }}
                        {% if form.status.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.status.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.condition.label }}
                        {{ form.condition(class="form-control") }}
                        {% if form.condition.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.condition.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.warehouse_id.label }}
                        {{ form.warehouse_id(class="form-control") }}
                        {% if form.warehouse_id.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.warehouse_id.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.weapon_document.label(class="form-label") }}
                        <div class="input-group">
                            {{ form.weapon_document(class="form-control", accept=".pdf,.jpg,.jpeg,.png,.doc,.docx", id="weapon_document") }}
                            <div class="input-group-append">
                                <span class="input-group-text">
                                    <i class="fas fa-file-pdf"></i>
                                </span>
                            </div>
                        </div>
                        {% if weapon.weapon_document %}
                        <small class="form-text text-info">
                            <i class="fas fa-file"></i> يوجد ملف مرفق حالياً:
                            <a href="{{ url_for('weapons.download_document', weapon_id=weapon.id) }}" target="_blank">
                                عرض الملف الحالي
                            </a>
                        </small>
                        {% endif %}
                        <small class="form-text text-muted">
                            <strong>الملفات المسموحة:</strong> PDF, JPG, PNG, DOC, DOCX<br>
                            <strong>الحد الأقصى للحجم:</strong> 25 ميجابايت<br>
                            <em>ملاحظة: رفع ملف جديد سيستبدل الملف الحالي</em>
                        </small>
                        {% if form.weapon_document.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.weapon_document.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="form-group">
                {{ form.notes.label }}
                {{ form.notes(class="form-control", rows=4) }}
                {% if form.notes.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.notes.errors %}
                    <span>{{ error }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="form-group mt-4">
                {{ form.submit(class="btn btn-primary") }}
                <a href="{{ url_for('weapons.details', weapon_id=weapon.id) }}"
                    class="btn btn-outline-secondary ml-2">إلغاء</a>
            </div>
        </form>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('weapon_document');
    const maxSize = 25 * 1024 * 1024; // 25 MB
    const allowedTypes = ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'];

    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Check file size
                if (file.size > maxSize) {
                    alert('حجم الملف كبير جداً. الحد الأقصى المسموح: 25 ميجابايت');
                    e.target.value = '';
                    return;
                }

                // Check file type
                const fileExtension = file.name.split('.').pop().toLowerCase();
                if (!allowedTypes.includes(fileExtension)) {
                    alert('نوع الملف غير مسموح. الأنواع المسموحة: PDF, JPG, PNG, DOC, DOCX');
                    e.target.value = '';
                    return;
                }

                // Show file info
                const fileInfo = document.createElement('small');
                fileInfo.className = 'form-text text-success';
                fileInfo.innerHTML = `<i class="fas fa-check"></i> تم اختيار الملف: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} ميجابايت)`;

                // Remove previous file info
                const existingInfo = fileInput.parentNode.parentNode.querySelector('.file-info');
                if (existingInfo) {
                    existingInfo.remove();
                }

                fileInfo.classList.add('file-info');
                fileInput.parentNode.parentNode.appendChild(fileInfo);
            }
        });
    }
});
</script>
{% endblock %}