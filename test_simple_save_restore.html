<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار آلية الحفظ والاستعادة البسيطة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .comparison-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .before-after {
            display: flex;
            gap: 20px;
        }
        
        .before, .after {
            flex: 1;
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        
        .after {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
        }
        
        .status-box {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container">
            <div class="text-center mb-4">
                <h1>🔄 آلية الحفظ والاستعادة البسيطة</h1>
                <p class="lead text-muted">تطبيق نفس آلية كشف الاستلامات على كشف الواجبات</p>
            </div>
            
            <!-- مقارنة الآليات -->
            <div class="comparison-section">
                <h4>📊 مقارنة الآليات</h4>
                
                <div class="before-after">
                    <div class="before">
                        <h6>❌ الآلية السابقة (معقدة)</h6>
                        <div class="code-block">
// دوال متعددة ومعقدة
saveToLocalStorage()
saveTableStructure()
saveSelectedLocations()
performAutoSave()
scheduleAutoSave()
createDataBackup()
saveAssignmentDataToServer()
savePatrolDataToServer()
saveShiftsDataToServer()

// مفاتيح متعددة
STORAGE_KEYS.ASSIGNMENT_DATA
STORAGE_KEYS.PATROL_DATA
STORAGE_KEYS.SHIFTS_DATA
tableStructure
selectedLocations
                        </div>
                        <p><strong>المشاكل:</strong></p>
                        <ul>
                            <li>دوال متعددة ومتضاربة</li>
                            <li>حفظ في أماكن مختلفة</li>
                            <li>تعقيد في الاستعادة</li>
                            <li>فقدان البيانات</li>
                        </ul>
                    </div>
                    
                    <div class="after">
                        <h6>✅ الآلية الجديدة (بسيطة)</h6>
                        <div class="code-block">
// دالة واحدة بسيطة
function autoSave() {
    localStorage.setItem('assignmentData', 
        JSON.stringify(assignmentData));
    localStorage.setItem('patrolData', 
        JSON.stringify(patrolData));
    localStorage.setItem('shiftsData', 
        JSON.stringify(shiftsData));
}

// دالة واحدة للتحميل
function loadSavedData() {
    // تحميل البيانات الثلاثة
}

// مفاتيح بسيطة
assignmentData
patrolData
shiftsData
                        </div>
                        <p><strong>المزايا:</strong></p>
                        <ul>
                            <li>دالة واحدة للحفظ</li>
                            <li>دالة واحدة للتحميل</li>
                            <li>مفاتيح بسيطة وواضحة</li>
                            <li>نفس آلية كشف الاستلامات</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- التغييرات المطبقة -->
            <div class="comparison-section">
                <h4>🔧 التغييرات المطبقة</h4>
                
                <div class="alert alert-success">
                    <h6>✅ تم تبسيط الدوال التالية:</h6>
                    <ul class="mb-0">
                        <li><code>addColumn()</code> - الآن تستخدم <code>autoSave()</code> فقط</li>
                        <li><code>deleteColumn()</code> - الآن تستخدم <code>autoSave()</code> فقط</li>
                        <li><code>addRow()</code> - الآن تستخدم <code>autoSave()</code> فقط</li>
                        <li><code>addRowAfter()</code> - الآن تستخدم <code>autoSave()</code> فقط</li>
                        <li><code>deleteRow()</code> - الآن تستخدم <code>autoSave()</code> فقط</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h6>🔄 آلية الحفظ التلقائي:</h6>
                    <ul class="mb-0">
                        <li>حفظ تلقائي عند تعديل أي خلية</li>
                        <li>حفظ تلقائي عند إضافة/حذف صف أو عمود</li>
                        <li>تأخير 1 ثانية لتجنب الحفظ المفرط</li>
                        <li>استعادة تلقائية عند تحميل الصفحة</li>
                    </ul>
                </div>
            </div>
            
            <!-- اختبار الوظائف -->
            <div class="comparison-section">
                <h4>🧪 اختبار الوظائف</h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100 mb-2" onclick="testSaveFunction()">اختبار دالة الحفظ</button>
                        <button class="btn btn-success w-100 mb-2" onclick="testLoadFunction()">اختبار دالة التحميل</button>
                        <button class="btn btn-info w-100 mb-2" onclick="checkLocalStorage()">فحص localStorage</button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-warning w-100 mb-2" onclick="simulateDataLoss()">محاكاة فقدان البيانات</button>
                        <button class="btn btn-danger w-100 mb-2" onclick="clearAllData()">مسح جميع البيانات</button>
                        <button class="btn btn-secondary w-100 mb-2" onclick="createTestData()">إنشاء بيانات اختبار</button>
                    </div>
                </div>
                
                <div id="testResults" class="status-box">
                    اضغط على أي زر لبدء الاختبار...
                </div>
            </div>
            
            <!-- خطوات الاختبار -->
            <div class="comparison-section">
                <h4>📋 خطوات الاختبار المطلوبة</h4>
                
                <div class="alert alert-warning">
                    <h6>🔍 للتأكد من عمل الآلية الجديدة:</h6>
                    <ol>
                        <li><strong>اذهب إلى صفحة كشف الواجبات:</strong> <a href="/duties/" target="_blank">/duties/</a></li>
                        <li><strong>اختر موقع في أي جدول</strong> وتأكد من حفظه</li>
                        <li><strong>أضف صف جديد</strong> وحدث الصفحة - يجب أن يبقى الصف</li>
                        <li><strong>أضف عمود جديد</strong> وحدث الصفحة - يجب أن يبقى العمود</li>
                        <li><strong>احذف صف</strong> وحدث الصفحة - يجب ألا يعود الصف</li>
                        <li><strong>احذف عمود</strong> وحدث الصفحة - يجب ألا يعود العمود</li>
                        <li><strong>عدل أي خلية</strong> وحدث الصفحة - يجب أن تبقى التعديلات</li>
                    </ol>
                </div>
                
                <div class="alert alert-success">
                    <h6>✅ النتيجة المتوقعة:</h6>
                    <p class="mb-0">جميع التغييرات (المواقع، الصفوف، الأعمدة، محتوى الخلايا) يجب أن تُحفظ وتُستعاد بشكل صحيح، تماماً مثل صفحة كشف الاستلامات.</p>
                </div>
            </div>
            
            <!-- روابط سريعة -->
            <div class="comparison-section">
                <h4>🔗 روابط الاختبار</h4>
                <div class="row text-center">
                    <div class="col-md-4">
                        <a href="/duties/" class="btn btn-outline-primary w-100" target="_blank">
                            📝 كشف الواجبات (محدث)
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="/receipts" class="btn btn-outline-success w-100" target="_blank">
                            📋 كشف الاستلامات (مرجع)
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="/test_row_column_operations.html" class="btn btn-outline-info w-100" target="_blank">
                            🧪 اختبار العمليات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة لإضافة رسالة إلى السجل
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logElement = document.getElementById('testResults');
            const messageElement = document.createElement('div');
            messageElement.className = type;
            messageElement.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(messageElement);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // اختبار دالة الحفظ
        function testSaveFunction() {
            addToLog('🧪 اختبار دالة الحفظ الجديدة...', 'info');
            
            // محاكاة بيانات اختبار
            const testData = {
                assignmentData: {
                    headers: ['الرقم', 'الموقع', 'الفترة الأولى', 'الفترة الثانية'],
                    rows: [['1', 'البوابة الرئيسية', 'أحمد', 'محمد']]
                },
                patrolData: {
                    headers: ['الرقم', 'موقع الدورية', 'المساء', 'الليل'],
                    rows: [['1', 'المبنى الإداري', 'سالم', 'خالد']]
                },
                shiftsData: {
                    headers: ['الرقم', 'موقع المناوبة', 'المناوب الأول', 'المناوب الثاني'],
                    rows: [['1', 'برج المراقبة', 'عبدالله', 'فهد']]
                }
            };
            
            try {
                // تطبيق الآلية الجديدة
                localStorage.setItem('assignmentData', JSON.stringify(testData.assignmentData));
                localStorage.setItem('patrolData', JSON.stringify(testData.patrolData));
                localStorage.setItem('shiftsData', JSON.stringify(testData.shiftsData));
                localStorage.setItem('lastSaved', new Date().toISOString());
                
                addToLog('✅ تم حفظ البيانات بنجاح باستخدام الآلية الجديدة', 'success');
                addToLog('💾 المفاتيح المحفوظة: assignmentData, patrolData, shiftsData', 'info');
                
            } catch (error) {
                addToLog(`❌ خطأ في الحفظ: ${error.message}`, 'error');
            }
        }
        
        // اختبار دالة التحميل
        function testLoadFunction() {
            addToLog('🔄 اختبار دالة التحميل...', 'info');
            
            try {
                const assignmentData = localStorage.getItem('assignmentData');
                const patrolData = localStorage.getItem('patrolData');
                const shiftsData = localStorage.getItem('shiftsData');
                
                if (assignmentData && patrolData && shiftsData) {
                    const assignment = JSON.parse(assignmentData);
                    const patrol = JSON.parse(patrolData);
                    const shifts = JSON.parse(shiftsData);
                    
                    addToLog('✅ تم تحميل البيانات بنجاح', 'success');
                    addToLog(`📊 الجدول الرئيسي: ${assignment.rows.length} صف، ${assignment.headers.length} عمود`, 'info');
                    addToLog(`🚔 جدول الدوريات: ${patrol.rows.length} صف، ${patrol.headers.length} عمود`, 'info');
                    addToLog(`👥 جدول المناوبين: ${shifts.rows.length} صف، ${shifts.headers.length} عمود`, 'info');
                } else {
                    addToLog('⚠️ لا توجد بيانات محفوظة للتحميل', 'warning');
                }
                
            } catch (error) {
                addToLog(`❌ خطأ في التحميل: ${error.message}`, 'error');
            }
        }
        
        // فحص localStorage
        function checkLocalStorage() {
            addToLog('🔍 فحص localStorage...', 'info');
            
            const allKeys = Object.keys(localStorage);
            const relevantKeys = allKeys.filter(key => 
                key === 'assignmentData' || 
                key === 'patrolData' || 
                key === 'shiftsData' ||
                key === 'lastSaved'
            );
            
            addToLog(`📊 إجمالي المفاتيح: ${allKeys.length}`, 'info');
            addToLog(`🎯 المفاتيح ذات الصلة: ${relevantKeys.length}`, 'success');
            
            relevantKeys.forEach(key => {
                const data = localStorage.getItem(key);
                const size = data ? (data.length / 1024).toFixed(2) : '0';
                addToLog(`  - ${key}: ${size} KB`, 'info');
            });
            
            if (relevantKeys.length === 0) {
                addToLog('⚠️ لا توجد بيانات محفوظة', 'warning');
            }
        }
        
        // محاكاة فقدان البيانات
        function simulateDataLoss() {
            addToLog('⚠️ محاكاة فقدان البيانات...', 'warning');
            
            // حفظ البيانات أولاً
            testSaveFunction();
            
            setTimeout(() => {
                // محاكاة إعادة تحميل الصفحة
                addToLog('🔄 محاكاة إعادة تحميل الصفحة...', 'info');
                
                setTimeout(() => {
                    // اختبار استعادة البيانات
                    testLoadFunction();
                    addToLog('✅ تم اختبار استعادة البيانات بعد إعادة التحميل', 'success');
                }, 1000);
            }, 1000);
        }
        
        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل تريد مسح جميع البيانات؟')) {
                localStorage.removeItem('assignmentData');
                localStorage.removeItem('patrolData');
                localStorage.removeItem('shiftsData');
                localStorage.removeItem('lastSaved');
                
                addToLog('🗑️ تم مسح جميع البيانات', 'warning');
            }
        }
        
        // إنشاء بيانات اختبار
        function createTestData() {
            addToLog('🔧 إنشاء بيانات اختبار...', 'info');
            testSaveFunction();
        }
        
        // تهيئة الصفحة
        window.addEventListener('load', function() {
            addToLog('🚀 تم تحميل صفحة اختبار الآلية البسيطة', 'success');
            addToLog('📋 جاهز لاختبار آلية الحفظ والاستعادة الجديدة', 'info');
            
            // فحص البيانات الموجودة
            setTimeout(checkLocalStorage, 500);
        });
    </script>
</body>
</html>
