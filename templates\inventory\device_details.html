{% extends 'base.html' %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card bg-dark text-white">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4>تفاصيل الجهاز: {{ device.name }}</h4>
                    <div>
                        <a href="{{ url_for('inventory.devices') }}" class="btn btn-secondary btn-sm ml-2">
                            <i class="fas fa-list"></i> العودة للقائمة
                        </a>
                        {% if current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager %}
                        <a href="{{ url_for('inventory.edit_device', device_id=device.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="card-title mb-4"><i class="fas fa-info-circle"></i> معلومات أساسية</h5>
                            <table class="table table-sm table-dark">
                                <tbody>
                                    <tr>
                                        <th style="width: 40%">اسم الجهاز</th>
                                        <td>{{ device.name }}</td>
                                    </tr>
                                    <tr>
                                        <th>نوع الجهاز</th>
                                        <td>{{ device.type }}</td>
                                    </tr>
                                    <tr>
                                        <th>الشركة المصنعة</th>
                                        <td>{{ device.manufacturer or 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <th>الموديل</th>
                                        <td>{{ device.model or 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <th>الرقم التسلسلي</th>
                                        <td>{{ device.serial_number or 'غير محدد' }}</td>
                                    </tr>

                                    <tr>
                                        <th>الموقع</th>
                                        <td>{{ device.location or 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <th>الحالة</th>
                                        <td>
                                            <span class="badge badge-pill
                                            {% if device.status == 'سليم' %}badge-success
                                            {% elif device.status == 'عطل بسيط' %}badge-warning
                                            {% elif device.status == 'عطل جسيم' %}badge-danger
                                            {% elif device.status == 'تحت الصيانة' %}badge-info
                                            {% elif device.status == 'خارج الخدمة' %}badge-secondary
                                            {% elif device.status == 'مفقود' %}badge-dark
                                            {% else %}badge-secondary{% endif %}">
                                                {{ device.status }}
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5 class="card-title mb-4"><i class="fas fa-calendar-alt"></i> تواريخ النظام</h5>
                            <table class="table table-sm table-dark">
                                <tbody>

                                    <tr>
                                        <th>تاريخ الإضافة للنظام</th>
                                        <td>{{ device.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                    <tr>
                                        <th>آخر تحديث</th>
                                        <td>{{ device.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h5 class="card-title mt-4 mb-3"><i class="fas fa-sticky-note"></i> ملاحظات</h5>
                            <div class="p-3 bg-secondary rounded">
                                {% if device.notes %}
                                <pre class="text-white mb-0" style="white-space: pre-wrap; background: transparent; border: none;">{{ device.notes }}</pre>
                                {% else %}
                                <em>لا توجد ملاحظات</em>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Maintenance Records Section -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card bg-secondary">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">سجل الصيانة</h5>
                                    {% if current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager %}
                                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addMaintenanceModal">
                                        <i class="fas fa-plus"></i> إضافة صيانة
                                    </button>
                                    {% endif %}
                                </div>
                                <div class="card-body">
                                    {% if maintenance_records %}
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>النوع</th>
                                                    <th>الوصف</th>
                                                    <th>تاريخ البدء</th>
                                                    <th>تاريخ الانتهاء</th>
                                                    <th>الحالة</th>
                                                    <th>التكلفة</th>
                                                    {% if current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager %}
                                                    <th>الإجراءات</th>
                                                    {% endif %}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for record in maintenance_records %}
                                                <tr>
                                                    <td>
                                                        {% if record.maintenance_type == 'routine' %}صيانة دورية
                                                        {% elif record.maintenance_type == 'repair' %}إصلاح
                                                        {% elif record.maintenance_type == 'upgrade' %}تحديث
                                                        {% elif record.maintenance_type == 'inspection' %}فحص
                                                        {% elif record.maintenance_type == 'replacement' %}طلب تعويض
                                                        {% else %}أخرى{% endif %}
                                                    </td>
                                                    <td>{{ record.description }}</td>
                                                    <td>{{ record.start_date.strftime('%Y-%m-%d') }}</td>
                                                    <td>{{ record.end_date.strftime('%Y-%m-%d') if record.end_date else '-' }}</td>
                                                    <td>
                                                        <span class="badge
                                                        {% if record.status == 'ongoing' %}badge-warning
                                                        {% elif record.status == 'completed' %}badge-success
                                                        {% else %}badge-secondary{% endif %}">
                                                            {% if record.status == 'ongoing' %}جارية
                                                            {% elif record.status == 'completed' %}مكتملة
                                                            {% elif record.status == 'cancelled' %}ملغاة
                                                            {% else %}{{ record.status }}{% endif %}
                                                        </span>
                                                    </td>
                                                    <td>{{ record.cost or '-' }}</td>
                                                    {% if current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager %}
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="{{ url_for('inventory.edit_device_maintenance', device_id=device.id, maintenance_id=record.id) }}" class="btn btn-info btn-sm" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="{{ url_for('inventory.delete_device_maintenance', device_id=device.id, maintenance_id=record.id) }}" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف سجل الصيانة هذا؟')" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>


                                                    </td>
                                                    {% endif %}
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <p class="text-center my-3">لا توجد سجلات صيانة لهذا الجهاز</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Maintenance Modal -->
{% if current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager %}
<div class="modal fade" id="addMaintenanceModal" tabindex="-1" aria-labelledby="addMaintenanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header">
                <h5 class="modal-title" id="addMaintenanceModalLabel">إضافة سجل صيانة جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('inventory.add_device_maintenance', device_id=device.id) }}">
                <div class="modal-body">
                    {{ maintenance_form.hidden_tag() }}
                    <div class="row">
                        <div class="col-md-6 form-group">
                            {{ maintenance_form.maintenance_type.label }}
                            {{ maintenance_form.maintenance_type(class="form-control") }}
                        </div>
                        <div class="col-md-6 form-group">
                            {{ maintenance_form.status.label }}
                            {{ maintenance_form.status(class="form-control") }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 form-group">
                            {{ maintenance_form.start_date.label }}
                            {{ maintenance_form.start_date(class="form-control datepicker", placeholder="YYYY-MM-DD") }}
                        </div>
                        <div class="col-md-6 form-group">
                            {{ maintenance_form.end_date.label }}
                            {{ maintenance_form.end_date(class="form-control datepicker", placeholder="YYYY-MM-DD") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ maintenance_form.description.label }}
                        {{ maintenance_form.description(class="form-control", rows=3) }}
                    </div>
                    <div class="row">
                        <div class="col-md-6 form-group">
                            {{ maintenance_form.cost.label }}
                            {{ maintenance_form.cost(class="form-control", placeholder="أدخل التكلفة (اختياري)") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ maintenance_form.notes.label }}
                        {{ maintenance_form.notes(class="form-control", rows=2) }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    {{ maintenance_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}



{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize date pickers
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            rtl: true,
            language: 'ar'
        });

        // Set today's date for start_date if empty
        if (!$('#start_date').val()) {
            $('#start_date').datepicker('update', new Date());
        }
    });


</script>
{% endblock %}
