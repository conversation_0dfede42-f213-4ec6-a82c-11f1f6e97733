/* تحسينات إضافية لشريط التنقل */

/* التباعد بين العناصر */
.navbar-left-tools {
    gap: 20px !important;
}

/* تنسيق اسم المستخدم على اليمين */
.navbar-user {
    margin-right: 15px;
}

/* تنسيق عنوان الصفحة في الشريط العلوي */
.page-title-navbar {
    margin-right: 20px;
    margin-left: auto;
}

.page-title-navbar .navbar-text {
    color: white;
    font-size: 16px;
    font-weight: bold;
}

.navbar-user .navbar-text {
    font-weight: 600;
    font-size: 0.95rem;
}

.navbar-user .fa-user-circle {
    font-size: 1.2rem;
    margin-left: 10px;
    color: var(--accent-color);
}

/* تنسيق أزرار الإعدادات والمظهر */
#settingsButton,
#theme-switcher {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.05);
    transition: all 0.2s ease;
    margin: 0 5px;
}

#settingsButton:hover,
#theme-switcher:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* تنسيق البحث */
#search-form .input-group {
    width: 240px;
    height: 40px;
}

#search-input {
    height: 40px;
    border-radius: 20px 0 0 20px;
    background-color: rgba(255, 255, 255, 0.05);
    border-color: var(--border-color);
    color: var(--text-primary);
    padding-right: 15px;
}

#search-form .btn {
    border-radius: 0 20px 20px 0;
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

#search-form .btn:hover {
    background-color: var(--accent-hover);
}

/* تنسيقات المظهر النهاري */
body.light-theme #search-input {
    background-color: rgba(0, 0, 0, 0.05);
    border-color: var(--light-border-color);
    color: var(--light-text-primary);
}

body.light-theme #settingsButton,
body.light-theme #theme-switcher {
    background-color: rgba(0, 0, 0, 0.05);
}

body.light-theme #settingsButton:hover,
body.light-theme #theme-switcher:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

body.light-theme .navbar-user .fa-user-circle {
    color: var(--light-accent-color);
}
