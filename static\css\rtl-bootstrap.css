/* <PERSON><PERSON> Bootstrap overrides for Arabic language support */

/* Text alignment */
.text-left {
  text-align: right !important;
}

.text-right {
  text-align: left !important;
}

/* Float utilities */
.float-left {
  float: right !important;
}

.float-right {
  float: left !important;
}

/* Margin and padding */
.mr-1,
.mx-1 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}

.ml-1,
.mx-1 {
  margin-left: 0 !important;
  margin-right: 0.25rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

.mr-3,
.mx-3 {
  margin-right: 0 !important;
  margin-left: 1rem !important;
}

.ml-3,
.mx-3 {
  margin-left: 0 !important;
  margin-right: 1rem !important;
}

.mr-4,
.mx-4 {
  margin-right: 0 !important;
  margin-left: 1.5rem !important;
}

.ml-4,
.mx-4 {
  margin-left: 0 !important;
  margin-right: 1.5rem !important;
}

.mr-5,
.mx-5 {
  margin-right: 0 !important;
  margin-left: 3rem !important;
}

.ml-5,
.mx-5 {
  margin-left: 0 !important;
  margin-right: 3rem !important;
}

.pr-1,
.px-1 {
  padding-right: 0 !important;
  padding-left: 0.25rem !important;
}

.pl-1,
.px-1 {
  padding-left: 0 !important;
  padding-right: 0.25rem !important;
}

.pr-2,
.px-2 {
  padding-right: 0 !important;
  padding-left: 0.5rem !important;
}

.pl-2,
.px-2 {
  padding-left: 0 !important;
  padding-right: 0.5rem !important;
}

.pr-3,
.px-3 {
  padding-right: 0 !important;
  padding-left: 1rem !important;
}

.pl-3,
.px-3 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.pr-4,
.px-4 {
  padding-right: 0 !important;
  padding-left: 1.5rem !important;
}

.pl-4,
.px-4 {
  padding-left: 0 !important;
  padding-right: 1.5rem !important;
}

.pr-5,
.px-5 {
  padding-right: 0 !important;
  padding-left: 3rem !important;
}

.pl-5,
.px-5 {
  padding-left: 0 !important;
  padding-right: 3rem !important;
}

/* Border */
.border-right {
  border-right: 0 !important;
  border-left: 1px solid #dee2e6 !important;
}

.border-left {
  border-left: 0 !important;
  border-right: 1px solid #dee2e6 !important;
}

/* List groups */
.list-group {
  padding-right: 0;
}

/* Forms */
.form-check {
  padding-left: 0;
  padding-right: 1.25rem;
}

.form-check-input {
  margin-left: 0;
  margin-right: -1.25rem;
}

/* Input groups */
.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text,
.input-group > .input-group-append:not(:last-child) > .btn,
.input-group > .input-group-append:not(:last-child) > .input-group-text,
.input-group
  > .input-group-append:last-child
  > .btn:not(:last-child):not(.dropdown-toggle),
.input-group
  > .input-group-append:last-child
  > .input-group-text:not(:last-child) {
  border-radius: 0 0.25rem 0.25rem 0;
}

.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group
  > .input-group-prepend:first-child
  > .input-group-text:not(:first-child) {
  border-radius: 0.25rem 0 0 0.25rem;
}

.input-group > .form-control:not(:last-child),
.input-group > .custom-select:not(:last-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.input-group > .form-control:not(:first-child),
.input-group > .custom-select:not(:first-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

/* Button groups */
.btn-group > .btn:first-child {
  margin-left: -1px;
  margin-right: 0;
}

.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn-group:not(:last-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* Dropdown */
.dropdown-menu {
  text-align: right;
}

.dropdown-toggle::after {
  margin-left: 0;
  margin-right: 0.255em;
}

/* Modal */
.modal-header .close {
  margin: -1rem auto -1rem -1rem;
}

/* Breadcrumb */
.breadcrumb-item + .breadcrumb-item {
  padding-left: 0;
  padding-right: 0.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  padding-right: 0;
  padding-left: 0.5rem;
}

/* Alerts */
.alert-dismissible {
  padding-right: 1.25rem;
  padding-left: 4rem;
}

.alert-dismissible .close {
  right: auto;
  left: 0;
}

/* Progress bars */
.progress-bar {
  float: right;
}

/* Pagination */
.page-item:first-child .page-link {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.page-item:last-child .page-link {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

/* Cards */
.card-header-pills,
.card-header-tabs {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}

/* List group */
.list-group {
  padding-right: 0;
}

/* Tables */
.table th,
.table td {
  text-align: right;
}

/* Form validation */
.form-check-inline {
  padding-right: 0;
  margin-right: 0;
  margin-left: 0.75rem;
}

.custom-control {
  padding-left: 0;
  padding-right: 1.5rem;
}

.custom-control-label::before,
.custom-control-label::after {
  left: auto;
  right: -1.5rem;
}

.form-check-input {
  margin-left: 0;
  margin-right: -1.25rem;
}

.custom-file-label {
  text-align: right;
}

.custom-file-label::after {
  right: auto;
  left: 0;
  border-right: inherit;
  border-left: none;
  border-radius: 0.25rem 0 0 0.25rem;
}

/* RTL for our custom components */
.sidebar {
  right: 0;
  left: auto;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  border-right: none;
}

.sidebar.collapsed {
  right: -180px;
  left: auto;
}

.main-content {
  margin-right: 250px;
  margin-left: 0;
}

.main-content.expanded {
  margin-right: 70px;
  margin-left: 0;
}

.navbar-brand img {
  margin-left: 10px;
  margin-right: 0;
}

.search-box .search-icon {
  right: 15px;
  left: auto;
}

.status-card {
  border-left: none;
  border-right: 4px solid var(--accent-color);
}
