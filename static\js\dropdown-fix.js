/**
 * حل مباشر للتأكد من عمل القوائم المنسدلة في أقرب وقت ممكن
 */
(function () {
  // تنفيذ الوظيفة على الفور
  function fixDropdowns() {
    var dropdowns = document.querySelectorAll(".dropdown-toggle");

    // إضافة معالجات الأحداث لكل منسدلة
    for (var i = 0; i < dropdowns.length; i++) {
      dropdowns[i].addEventListener("click", function (e) {
        e.preventDefault();
        e.stopPropagation();

        // إغلاق القوائم المنسدلة الأخرى
        var otherDropdowns = document.querySelectorAll(".dropdown-menu");
        for (var j = 0; j < otherDropdowns.length; j++) {
          otherDropdowns[j].classList.remove("show");
        }

        // تبديل فئة "show" على العنصر الأصل والقائمة المنسدلة
        // var nextMenu = this.nextElementSibling;
        // if (nextMenu && nextMenu.classList.contains("dropdown-menu")) {
        //   nextMenu.classList.toggle("show");
        // }
      });
    }

    // إغلاق القوائم المنسدلة عند النقر في أي مكان آخر
    document.addEventListener("click", function (e) {
      if (!e.target.closest(".dropdown")) {
        var openDropdowns = document.querySelectorAll(".dropdown-menu.show");
        for (var k = 0; k < openDropdowns.length; k++) {
          openDropdowns[k].classList.remove("show");
          openDropdowns[k].parentNode.classList.remove("show");
        }
      }
    });
  }

  // إذا تم تحميل DOM بالفعل، قم بتنفيذ الإصلاح على الفور
  if (
    document.readyState === "complete" ||
    document.readyState === "interactive"
  ) {
    fixDropdowns();
  } else {
    // وإلا، انتظر حتى يتم تحميل مستند HTML بالكامل
    document.addEventListener("DOMContentLoaded", fixDropdowns);
  }

  // أيضاً، قم بتنفيذ الإصلاح بعد تحميل الصفحة بالكامل (للتأكد)
  window.addEventListener("load", fixDropdowns);

  // إضافة طريقة تنفيذ للتطبيق في الحالات الأخرى
  window.fixDropdowns = fixDropdowns;
})();
