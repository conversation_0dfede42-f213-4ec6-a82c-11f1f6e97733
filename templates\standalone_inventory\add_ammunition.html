{% extends "base.html" %}

{% block title %}إضافة ذخيرة جديدة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-bomb text-danger"></i>
                    إضافة ذخيرة جديدة
                </h2>
                <div>
                    <a href="{{ url_for('standalone_inventory.items', category='ذخيرة') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للذخيرة
                    </a>
                    <a href="{{ url_for('standalone_inventory.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Warning Alert -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> تحذير هام:</h6>
                <p class="mb-0">يرجى توخي الحذر الشديد عند التعامل مع الذخيرة والمواد المتفجرة. تأكد من اتباع جميع إجراءات السلامة المطلوبة.</p>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        بيانات الذخيرة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('standalone_inventory.add_ammunition') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.item_code.label(class="form-label required") }}
                                    {{ form.item_code(class="form-control", placeholder="مثال: AMM-001", id="item_code") }}
                                    {% if form.item_code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.item_code.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">كود فريد للذخيرة (يمكن استخدامه كباركود)</small>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label required") }}
                                    {{ form.name(class="form-control", placeholder="مثال: رصاص حي 9 ملم") }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.subcategory.label(class="form-label required") }}
                                    {{ form.subcategory(class="form-control", id="subcategory") }}
                                    {% if form.subcategory.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.subcategory.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.danger_level.label(class="form-label") }}
                                    {{ form.danger_level(class="form-control") }}
                                    {% if form.danger_level.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.danger_level.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.caliber.label(class="form-label") }}
                                    {{ form.caliber(class="form-control", placeholder="مثال: 9 ملم، 7.62 ملم") }}
                                    {% if form.caliber.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.caliber.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.batch_number.label(class="form-label") }}
                                    {{ form.batch_number(class="form-control", placeholder="مثال: BATCH-2024-001") }}
                                    {% if form.batch_number.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.batch_number.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.quantity.label(class="form-label required") }}
                                    {{ form.quantity(class="form-control", placeholder="0") }}
                                    {% if form.quantity.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.quantity.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.manufacture_date.label(class="form-label") }}
                                    {{ form.manufacture_date(class="form-control", placeholder="YYYY-MM-DD") }}
                                    {% if form.manufacture_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.manufacture_date.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.expiry_date.label(class="form-label") }}
                                    {{ form.expiry_date(class="form-control", placeholder="YYYY-MM-DD") }}
                                    {% if form.expiry_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.expiry_date.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-group">
                                    {{ form.notes.label(class="form-label") }}
                                    {{ form.notes(class="form-control", rows="3", placeholder="أي ملاحظات إضافية حول الذخيرة، إجراءات السلامة، شروط التخزين...") }}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{{ url_for('standalone_inventory.items', category='ذخيرة') }}" class="btn btn-secondary me-2">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    {{ form.submit(class="btn btn-danger") }}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Add Examples -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb"></i>
                        أمثلة سريعة للذخيرة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger btn-sm w-100" onclick="fillExample('رصاص حي', 'رصاص حي 9 ملم', '9 ملم', 'عالي')">
                                رصاص حي 9 ملم
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger btn-sm w-100" onclick="fillExample('رصاص تدريب', 'رصاص تدريب 7.62 ملم', '7.62 ملم', 'متوسط')">
                                رصاص تدريب
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger btn-sm w-100" onclick="fillExample('قنابل دخان', 'قنبلة دخان', '', 'متوسط')">
                                قنبلة دخان
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger btn-sm w-100" onclick="fillExample('قنابل صوت', 'قنبلة صوت', '', 'عالي')">
                                قنبلة صوت
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Safety Guidelines -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-danger">
                <h6><i class="fas fa-shield-alt"></i> إرشادات السلامة:</h6>
                <ul class="mb-0">
                    <li>تأكد من صحة جميع البيانات المدخلة خاصة تواريخ الصلاحية</li>
                    <li>احرص على تسجيل مستوى الخطورة بدقة</li>
                    <li>تأكد من اتباع إجراءات التخزين الآمن</li>
                    <li>قم بمراجعة البيانات مع المسؤول المختص قبل الحفظ</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تفعيل التركيز على حقل الكود عند تحميل الصفحة
    $('#item_code').focus();
    
    // إنشاء كود تلقائي بناءً على نوع الذخيرة
    $('#subcategory').change(function() {
        var subcategory = $(this).val();
        var codeField = $('#item_code');
        
        if (subcategory && !codeField.val()) {
            var prefix = 'AMM-';
            var randomNum = Math.floor(Math.random() * 9000) + 1000;
            codeField.val(prefix + randomNum);
        }
    });
    
    // دعم مسح الباركود
    var barcodeBuffer = '';
    var barcodeTimeout;
    
    $(document).keypress(function(e) {
        if ($('input[type="text"], textarea, select').is(':focus')) {
            return;
        }
        
        barcodeBuffer += String.fromCharCode(e.which);
        
        clearTimeout(barcodeTimeout);
        barcodeTimeout = setTimeout(function() {
            if (barcodeBuffer.length > 3) {
                $('#item_code').val(barcodeBuffer);
                $('#item_code').focus();
                checkExistingItem(barcodeBuffer);
            }
            barcodeBuffer = '';
        }, 100);
    });
    
    // التحقق من وجود الذخيرة عند تغيير الكود
    $('#item_code').blur(function() {
        var code = $(this).val();
        if (code) {
            checkExistingItem(code);
        }
    });
    
    function checkExistingItem(code) {
        $.get('{{ url_for("standalone_inventory.search_barcode") }}', {barcode: code})
        .done(function(data) {
            if (data.found) {
                showAlert('تحذير: هذا الكود موجود مسبقاً للصنف: ' + data.item.name, 'warning');
            }
        });
    }
    
    function showAlert(message, type) {
        var alertClass = 'alert-' + type;
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        
        $('.alert').not('.alert-info, .alert-warning, .alert-danger').remove();
        $('.container-fluid').prepend(alertHtml);
        
        setTimeout(function() {
            $('.alert-' + type).fadeOut();
        }, 5000);
    }
});

// ملء مثال سريع
function fillExample(subcategory, name, caliber, dangerLevel) {
    $('#subcategory').val(subcategory);
    $('#name').val(name);
    $('#caliber').val(caliber);
    $('#danger_level').val(dangerLevel);
    
    // إنشاء كود تلقائي
    var randomNum = Math.floor(Math.random() * 9000) + 1000;
    $('#item_code').val('AMM-' + randomNum);
}
</script>
{% endblock %}
