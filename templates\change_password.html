{% extends "base.html" %}

{% block title %}تغيير كلمة المرور{% endblock %}

{% block page_title %}تغيير كلمة المرور{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">تغيير كلمة المرور</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.change_password') }}">
                    {{ form.hidden_tag() }}
                    
                    <div class="form-group">
                        {{ form.current_password.label(class="form-control-label") }}
                        {{ form.current_password(class="form-control") }}
                        {% if form.current_password.errors %}
                            <div class="text-danger">
                                {% for error in form.current_password.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.new_password.label(class="form-control-label") }}
                        {{ form.new_password(class="form-control") }}
                        {% if form.new_password.errors %}
                            <div class="text-danger">
                                {% for error in form.new_password.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.confirm_password.label(class="form-control-label") }}
                        {{ form.confirm_password(class="form-control") }}
                        {% if form.confirm_password.errors %}
                            <div class="text-danger">
                                {% for error in form.confirm_password.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group mt-4">
                        {{ form.submit(class="btn btn-primary btn-block") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}