{% extends "base.html" %}

{% block title %}أصناف المخزون{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-list text-primary"></i>
                    أصناف المخزون
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('inventory_management.add_item') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة صنف جديد
                    </a>
                    <a href="{{ url_for('inventory_management.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search"></i>
                        البحث والفلاتر
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('inventory_management.items') }}">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="{{ search }}" placeholder="اسم الصنف، الكود، الباركود، أو الفئة الفرعية">
                                <small class="form-text text-muted">يمكنك البحث بالاسم أو الكود أو مسح الباركود مباشرة</small>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="category" class="form-label">الفئة</label>
                                <select class="form-control" id="category" name="category">
                                    <option value="">جميع الفئات</option>
                                    {% for cat in categories %}
                                    <option value="{{ cat }}" {% if category == cat %}selected{% endif %}>
                                        {{ cat }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    {% for stat in statuses %}
                                    <option value="{{ stat }}" {% if status == stat %}selected{% endif %}>
                                        {{ stat }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="{{ url_for('inventory_management.items') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table"></i>
                        قائمة الأصناف ({{ items|length }} صنف)
                    </h5>
                </div>
                <div class="card-body">
                    {% if items %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>كود الصنف</th>
                                    <th>اسم الصنف</th>
                                    <th>الفئة</th>
                                    <th>المقاس</th>
                                    <th>اللون</th>
                                    <th>الكمية المتوفرة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items %}
                                <tr>
                                    <td>
                                        <strong>{{ item.item_code }}</strong>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('inventory_management.item_details', item_id=item.id) }}"
                                           class="text-decoration-none">
                                            {{ item.name }}
                                        </a>
                                        {% if item.subcategory %}
                                        <br><small class="text-muted">{{ item.subcategory }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if item.category == 'ملبوسات' %}bg-success
                                            {% elif item.category == 'معدات' %}bg-info
                                            {% elif item.category == 'ذخيرة' %}bg-danger
                                            {% else %}bg-secondary{% endif %}">
                                            {{ item.category }}
                                        </span>
                                    </td>
                                    <td>{{ item.size or '-' }}</td>
                                    <td>{{ item.color or '-' }}</td>
                                    <td>
                                        <span class="{% if item.is_low_stock %}text-danger fw-bold{% endif %}">
                                            {{ item.quantity_in_stock }}
                                        </span>
                                        {% if item.is_low_stock %}
                                        <i class="fas fa-exclamation-triangle text-warning" title="مخزون منخفض"></i>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if item.status == 'متوفر' %}bg-success
                                            {% elif item.status == 'مُصدر' %}bg-warning
                                            {% elif item.status == 'صيانة' %}bg-info
                                            {% elif item.status == 'تالف' %}bg-danger
                                            {% elif item.status == 'منتهي الصلاحية' %}bg-dark
                                            {% else %}bg-secondary{% endif %}">
                                            {{ item.status }}
                                        </span>
                                        {% if item.is_expired %}
                                        <br><small class="text-danger">منتهي الصلاحية</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('inventory_management.item_details', item_id=item.id) }}"
                                               class="btn btn-outline-primary" title="التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('inventory_management.edit_item', item_id=item.id) }}"
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أصناف</h5>
                        <p class="text-muted">لم يتم العثور على أصناف تطابق معايير البحث</p>
                        <a href="{{ url_for('inventory_management.add_item') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة صنف جديد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تفعيل DataTables للجدول
    if ($('table tbody tr').length > 0) {
        $('table').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "pageLength": 25,
            "order": [[0, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": -1 }
            ]
        });
    }

    // دعم مسح الباركود
    var barcodeBuffer = '';
    var barcodeTimeout;

    $(document).keypress(function(e) {
        // إذا كان المستخدم يكتب في حقل البحث
        if ($('#search').is(':focus')) {
            return;
        }

        // تجميع الأحرف للباركود
        barcodeBuffer += String.fromCharCode(e.which);

        clearTimeout(barcodeTimeout);
        barcodeTimeout = setTimeout(function() {
            if (barcodeBuffer.length > 3) {
                // وضع الباركود في حقل البحث وتنفيذ البحث
                $('#search').val(barcodeBuffer);
                $('form').submit();
            }
            barcodeBuffer = '';
        }, 100);
    });

    // تفعيل البحث السريع عند الضغط على Enter
    $('#search').keypress(function(e) {
        if (e.which == 13) {
            $('form').submit();
        }
    });
});
</script>
{% endblock %}
