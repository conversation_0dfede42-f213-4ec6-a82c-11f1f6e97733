#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
import json

def check_locations_db():
    """فحص ملف locations.db"""
    db_path = r'C:\Users\<USER>\Desktop\iMQS1\locations.db'
    
    if not os.path.exists(db_path):
        print(f"❌ ملف locations.db غير موجود في: {db_path}")
        return False
    
    print(f"✅ تم العثور على ملف locations.db")
    print(f"📊 حجم الملف: {os.path.getsize(db_path)} بايت")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على أسماء الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📋 الجداول الموجودة: {[t[0] for t in tables]}")
        
        # فحص كل جدول
        for table in tables:
            table_name = table[0]
            print(f"\n🔍 فحص جدول {table_name}:")
            print("-" * 30)
            
            # عدد الصفوف
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"📊 عدد الصفوف: {count}")
            
            # معلومات الأعمدة
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            print(f"📋 الأعمدة: {column_names}")
            
            # عرض البيانات إذا كانت موجودة
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name}")
                rows = cursor.fetchall()
                
                print(f"📄 البيانات:")
                for i, row in enumerate(rows):
                    row_dict = dict(zip(column_names, row))
                    print(f"   صف {i+1}: {row_dict}")
                    
                    # إذا كان هناك عمود name أو description، اعرضه بوضوح
                    if 'name' in row_dict:
                        print(f"      🏢 الاسم: {row_dict['name']}")
                    if 'description' in row_dict:
                        print(f"      📝 الوصف: {row_dict['description']}")
                    if 'coordinates' in row_dict:
                        print(f"      📍 الإحداثيات: {row_dict['coordinates']}")
                    print()
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة قاعدة البيانات: {e}")
        return False

def check_init_locations_file():
    """فحص ملف init_locations_data.py"""
    file_path = r'C:\Users\<USER>\Desktop\iMQS1\init_locations_data.py'
    
    if not os.path.exists(file_path):
        print(f"❌ ملف init_locations_data.py غير موجود")
        return False
    
    print(f"\n✅ فحص ملف init_locations_data.py")
    print("=" * 50)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📊 حجم الملف: {len(content)} حرف")
        
        # البحث عن بيانات المواقع
        lines = content.split('\n')
        location_lines = []
        
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in ['location', 'موقع', 'name', 'coordinates']):
                location_lines.append(f"السطر {i+1}: {line.strip()}")
        
        if location_lines:
            print(f"📍 تم العثور على {len(location_lines)} سطر يحتوي على بيانات المواقع:")
            for line in location_lines[:20]:  # عرض أول 20 سطر
                print(f"   {line}")
        else:
            print("❌ لم يتم العثور على بيانات مواقع واضحة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def check_migrate_locations_file():
    """فحص ملف migrate_locations_data.py"""
    file_path = r'C:\Users\<USER>\Desktop\iMQS1\migrate_locations_data.py'
    
    if not os.path.exists(file_path):
        print(f"❌ ملف migrate_locations_data.py غير موجود")
        return False
    
    print(f"\n✅ فحص ملف migrate_locations_data.py")
    print("=" * 50)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📊 حجم الملف: {len(content)} حرف")
        
        # البحث عن بيانات المواقع
        lines = content.split('\n')
        important_lines = []
        
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in ['insert', 'location', 'موقع', 'name', 'coordinates', 'migrate']):
                important_lines.append(f"السطر {i+1}: {line.strip()}")
        
        if important_lines:
            print(f"📍 تم العثور على {len(important_lines)} سطر مهم:")
            for line in important_lines[:20]:  # عرض أول 20 سطر
                print(f"   {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

if __name__ == "__main__":
    print("🔍 البحث عن المواقع المفقودة في ملفات iMQS1")
    print("=" * 60)
    
    # فحص قاعدة البيانات المحلية
    db_success = check_locations_db()
    
    # فحص ملفات البيانات
    init_success = check_init_locations_file()
    migrate_success = check_migrate_locations_file()
    
    print("\n" + "=" * 60)
    print("📋 ملخص النتائج:")
    print(f"   locations.db: {'✅ تم الفحص' if db_success else '❌ فشل الفحص'}")
    print(f"   init_locations_data.py: {'✅ تم الفحص' if init_success else '❌ فشل الفحص'}")
    print(f"   migrate_locations_data.py: {'✅ تم الفحص' if migrate_success else '❌ فشل الفحص'}")
