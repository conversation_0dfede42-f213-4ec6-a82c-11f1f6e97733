/**
 * ملف JavaScript الرئيسي للتطبيق
 */
document.addEventListener("DOMContentLoaded", function () {
  // تبديل الشريط الجانبي
  const toggleSidebar = document.getElementById("toggle-sidebar");
  const sidebar = document.querySelector(".sidebar");
  const mainContent = document.querySelector(".main-content");

  if (toggleSidebar && sidebar && mainContent) {
    toggleSidebar.addEventListener("click", function () {
      sidebar.classList.toggle("collapsed");
      mainContent.classList.toggle("expanded");
    });
  }

  // إصلاح شامل للقوائم المنسدلة مع Bootstrap 5
  var dropdownElementList = [].slice.call(
    document.querySelectorAll('[data-bs-toggle="dropdown"]')
  );
  if (dropdownElementList.length > 0) {
    dropdownElementList.map(function (dropdownToggleEl) {
      return new bootstrap.Dropdown(dropdownToggleEl);
    });
  }

  // تنسيق حقول التاريخ
  const dateInputs = document.querySelectorAll('input[type="date"]');
  dateInputs.forEach(function (input) {
    if (input.type !== "date") {
      // تعيين كمدخل نص عادي لدعم المتصفحات القديمة
      input.setAttribute("placeholder", "YYYY-MM-DD");
    }
  });

  // إنشاء رموز QR تلقائيًا
  const qrContainers = document.querySelectorAll(".qr-code");
  qrContainers.forEach(function (container) {
    const data = container.getAttribute("data-content");
    if (data) {
      new QRCode(container, {
        text: data,
        width: 128,
        height: 128,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
    }
  });

  // الاستعداد لتصدير البيانات
  const exportButtons = document.querySelectorAll(".export-btn");
  exportButtons.forEach(function (button) {
    button.addEventListener("click", function (e) {
      const format = this.getAttribute("data-format");
      const target = this.getAttribute("data-target");

      if (format && target) {
        // إعادة توجيه إلى نقطة نهاية التصدير
        window.location.href = "/export/" + target + "?format=" + format;
      }
    });
  });

  // إضافة تأثيرات لعناصر الواجهة
  const actionButtons = document.querySelectorAll(".btn-action");
  actionButtons.forEach(function (button) {
    button.addEventListener("mouseenter", function () {
      this.classList.add("btn-hover");
    });

    button.addEventListener("mouseleave", function () {
      this.classList.remove("btn-hover");
    });
  });

  // تبديل وضع السمة (فاتح/داكن)
  const themeSwitcher = document.getElementById("theme-switcher");
  if (themeSwitcher) {
    themeSwitcher.addEventListener("click", function () {
      document.body.classList.toggle("light-theme");
      const icon = this.querySelector("i");

      if (document.body.classList.contains("light-theme")) {
        icon.classList.remove("fa-sun");
        icon.classList.add("fa-moon");
        localStorage.setItem("theme", "light");
      } else {
        icon.classList.remove("fa-moon");
        icon.classList.add("fa-sun");
        localStorage.setItem("theme", "dark");
      }
    });

    // التحقق من السمة المخزنة عند تحميل الصفحة
    applyStoredTheme();
  }

  // تطبيق المظهر المخزن (تم إضافة هذه الدالة لتسهيل إعادة الاستخدام)
  function applyStoredTheme() {
    const savedTheme = localStorage.getItem("theme");
    // تطبيق المظهر الافتراضي (الداكن) إذا لم يكن هناك تفضيل مخزن
    if (savedTheme === "light") {
      document.body.classList.add("light-theme");
      // تحديث أيقونات جميع أزرار تغيير المظهر
      document.querySelectorAll("#theme-switcher").forEach((btn) => {
        const icon = btn.querySelector("i");
        if (icon) {
          icon.classList.remove("fa-sun");
          icon.classList.add("fa-moon");
        }
      });
    } else {
      document.body.classList.remove("light-theme");
      // تحديث أيقونات جميع أزرار تغيير المظهر
      document.querySelectorAll("#theme-switcher").forEach((btn) => {
        const icon = btn.querySelector("i");
        if (icon) {
          icon.classList.remove("fa-moon");
          icon.classList.add("fa-sun");
        }
      });
    }
  }

  // تطبيق المظهر المخزن مباشرة عند تحميل الصفحة دون انتظار DOM
  applyStoredTheme();

  // دعم رسوم الإحصائيات
  const chartCanvases = document.querySelectorAll(".chart-canvas");
  chartCanvases.forEach(function (canvas) {
    // تنفيذ الرسوم البيانية في ملف charts.js
  });

  // تقليل البحث المباشر
  const searchInput = document.getElementById("search-input");
  if (searchInput) {
    let timeout = null;
    searchInput.addEventListener("input", function () {
      clearTimeout(timeout);
      timeout = setTimeout(function () {
        // يمكن تنفيذ البحث المباشر هنا
      }, 500);
    });
  }

  // إعداد تلميحات الأدوات
  if (typeof $ === "function" && typeof $.fn.tooltip === "function") {
    $('[data-toggle="tooltip"]').tooltip();
  }

  // معالجة النقر على عنصر القائمة المنسدلة لشاشات العرض فقط
  document.querySelectorAll("#displayScreensDropdown .dropdown-item").forEach(function (item) {
    item.addEventListener("click", function (e) {
      const href = this.getAttribute("href");
      if (href && href !== "#") {
        // منع السلوك الافتراضي للمتصفح
        e.preventDefault();
        e.stopPropagation();

        // منع النقرات المتعددة
        if (this.classList.contains('clicked')) {
          return;
        }
        this.classList.add('clicked');

        // فتح في نافذة جديدة مرة واحدة فقط
        window.open(href, "_blank");

        // إغلاق القائمة
        const dropdown = document.getElementById('displayScreensDropdown');
        if (dropdown) {
          dropdown.classList.remove('show');
        }

        // إزالة الفئة بعد ثانية واحدة
        setTimeout(() => {
          this.classList.remove('clicked');
        }, 1000);
      }
    });
  });
});

// تطبيق المظهر المحفوظ عند تحميل الصفحة خارج حدث DOMContentLoaded
(function () {
  const savedTheme = localStorage.getItem("theme");
  if (savedTheme === "light") {
    document.body.classList.add("light-theme");
  }
})();
