#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإصلاح مشكلة عدم ظهور ملفات التعليمات في المواقع
يتحقق من وجود العمود ويضيفه إذا لم يكن موجود
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import os

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'database': 'military_warehouse',
    'user': 'postgres',
    'password': 'postgres'
}

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def column_exists(cursor, table_name, column_name):
    """فحص وجود عمود في جدول"""
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = %s 
            AND column_name = %s
        );
    """, (table_name, column_name))
    return cursor.fetchone()[0]

def table_exists(cursor, table_name):
    """فحص وجود جدول"""
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = %s
        );
    """, (table_name,))
    return cursor.fetchone()[0]

def check_locations_with_instructions(cursor):
    """فحص المواقع التي تحتوي على ملفات تعليمات"""
    try:
        cursor.execute("""
            SELECT id, name, instructions_file 
            FROM locations 
            WHERE instructions_file IS NOT NULL 
            AND instructions_file != '';
        """)
        return cursor.fetchall()
    except Exception as e:
        print(f"❌ خطأ في فحص المواقع: {e}")
        return []

def fix_location_instructions():
    """إصلاح مشكلة ملفات التعليمات في المواقع"""
    
    print("🔧 بدء إصلاح مشكلة ملفات التعليمات في المواقع...")
    print("=" * 60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # 1. فحص وجود جدول المواقع
        if not table_exists(cursor, 'locations'):
            print("❌ جدول المواقع غير موجود!")
            return False
        
        print("✅ جدول المواقع موجود")
        
        # 2. فحص وجود عمود instructions_file
        if not column_exists(cursor, 'locations', 'instructions_file'):
            print("⚠️  عمود instructions_file غير موجود، سيتم إضافته...")
            
            # إضافة العمود
            cursor.execute("""
                ALTER TABLE locations 
                ADD COLUMN instructions_file VARCHAR(255);
            """)
            
            print("✅ تم إضافة عمود instructions_file بنجاح")
        else:
            print("✅ عمود instructions_file موجود")
        
        # 3. فحص الأعمدة الإضافية المطلوبة
        additional_columns = [
            ('pdf_filename', 'VARCHAR(255)', 'اسم ملف PDF'),
            ('pdf_upload_date', 'TIMESTAMP', 'تاريخ رفع PDF')
        ]
        
        for column_name, column_type, description in additional_columns:
            if not column_exists(cursor, 'locations', column_name):
                print(f"⚠️  عمود {column_name} ({description}) غير موجود، سيتم إضافته...")
                
                cursor.execute(f"""
                    ALTER TABLE locations 
                    ADD COLUMN {column_name} {column_type};
                """)
                
                print(f"✅ تم إضافة عمود {column_name} بنجاح")
            else:
                print(f"✅ عمود {column_name} موجود")
        
        # 4. فحص المواقع التي تحتوي على ملفات تعليمات
        print("\n📋 فحص المواقع التي تحتوي على ملفات تعليمات...")
        locations_with_files = check_locations_with_instructions(cursor)
        
        if locations_with_files:
            print(f"📊 تم العثور على {len(locations_with_files)} موقع يحتوي على ملفات تعليمات:")
            for location in locations_with_files:
                location_id, name, instructions_file = location
                print(f"   📍 {name} (ID: {location_id})")
                print(f"      📄 الملف: {instructions_file}")
                
                # فحص وجود الملف فعلياً
                if instructions_file:
                    file_path = os.path.join('static', 'uploads', 'location_instructions', os.path.basename(instructions_file))
                    if os.path.exists(file_path):
                        print(f"      ✅ الملف موجود على القرص")
                    else:
                        print(f"      ❌ الملف غير موجود على القرص")
        else:
            print("ℹ️  لا توجد مواقع تحتوي على ملفات تعليمات حالياً")
        
        # 5. إنشاء مجلد الرفع إذا لم يكن موجود
        upload_dir = os.path.join('static', 'uploads', 'location_instructions')
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir, exist_ok=True)
            print(f"✅ تم إنشاء مجلد الرفع: {upload_dir}")
        else:
            print(f"✅ مجلد الرفع موجود: {upload_dir}")
        
        # 6. فحص صلاحيات الكتابة
        if os.access(upload_dir, os.W_OK):
            print("✅ صلاحيات الكتابة متوفرة في مجلد الرفع")
        else:
            print("❌ صلاحيات الكتابة غير متوفرة في مجلد الرفع")
        
        # 7. إنشاء فهرس للأداء
        try:
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_locations_instructions_file 
                ON locations (instructions_file) 
                WHERE instructions_file IS NOT NULL;
            """)
            print("✅ تم إنشاء فهرس للأداء")
        except Exception as e:
            print(f"⚠️  تعذر إنشاء الفهرس: {e}")
        
        print("\n" + "=" * 60)
        print("✅ تم إصلاح مشكلة ملفات التعليمات بنجاح!")
        print("\n📋 ملخص الإصلاح:")
        print("   ✅ تم التأكد من وجود جدول المواقع")
        print("   ✅ تم التأكد من وجود عمود instructions_file")
        print("   ✅ تم إنشاء مجلد الرفع")
        print("   ✅ تم فحص الملفات الموجودة")
        
        print("\n💡 الآن يمكنك:")
        print("   📤 رفع ملفات PDF في تعديل المواقع")
        print("   👁️  رؤية الملفات في تفاصيل المواقع")
        print("   📥 تحميل الملفات المرفوعة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ أثناء الإصلاح: {e}")
        return False
    
    finally:
        cursor.close()
        conn.close()

def test_file_upload_functionality():
    """اختبار وظيفة رفع الملفات"""
    print("\n🧪 اختبار وظيفة رفع الملفات...")
    
    # فحص مجلد الرفع
    upload_dir = os.path.join('static', 'uploads', 'location_instructions')
    
    if not os.path.exists(upload_dir):
        print(f"❌ مجلد الرفع غير موجود: {upload_dir}")
        return False
    
    # فحص صلاحيات الكتابة
    if not os.access(upload_dir, os.W_OK):
        print(f"❌ لا توجد صلاحيات كتابة في: {upload_dir}")
        return False
    
    # إنشاء ملف اختبار
    test_file_path = os.path.join(upload_dir, 'test_file.txt')
    try:
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write('ملف اختبار')
        
        # حذف ملف الاختبار
        os.remove(test_file_path)
        
        print("✅ اختبار رفع الملفات نجح")
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار رفع الملفات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إصلاح ملفات التعليمات في المواقع")
    print("📋 هذه الأداة تحل مشكلة عدم ظهور المرفقات في تفاصيل الموقع")
    print("🔒 آمنة تماماً ولا تؤثر على البيانات الموجودة")
    print("=" * 60)
    
    # تأكيد من المستخدم
    response = input("\n❓ هل تريد المتابعة مع الإصلاح؟ (y/n): ").lower().strip()
    if response not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية")
        return
    
    # تشغيل الإصلاح
    success = fix_location_instructions()
    
    if success:
        # اختبار الوظيفة
        test_file_upload_functionality()
        
        print("\n🎉 تم الإصلاح بنجاح!")
        print("💡 الآن يمكنك:")
        print("   1. الذهاب إلى إدارة المواقع")
        print("   2. تعديل أي موقع")
        print("   3. رفع ملف PDF في قسم 'ملف تعليمات الموقع'")
        print("   4. حفظ التغييرات")
        print("   5. عرض تفاصيل الموقع لرؤية الملف المرفوع")
    else:
        print("\n❌ فشل في الإصلاح")
        print("🔧 تحقق من إعدادات قاعدة البيانات وأعد المحاولة")

if __name__ == "__main__":
    main()
