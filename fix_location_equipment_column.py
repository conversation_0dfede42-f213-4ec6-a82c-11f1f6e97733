#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import sys

def fix_location_equipment_column():
    """إصلاح اسم عمود condition_status في جدول location_equipment"""
    try:
        # الاتصال بقاعدة البيانات
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cursor = conn.cursor()
        
        print("🔧 بدء إصلاح عمود condition_status في جدول location_equipment...")
        
        # التحقق من وجود العمود condition
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'location_equipment' 
            AND column_name = 'condition'
        """)
        
        condition_exists = cursor.fetchone()
        
        # التحقق من وجود العمود condition_status
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'location_equipment' 
            AND column_name = 'condition_status'
        """)
        
        condition_status_exists = cursor.fetchone()
        
        if condition_exists and not condition_status_exists:
            print("✅ تم العثور على عمود 'condition' - سيتم إعادة تسميته إلى 'condition_status'")
            
            # إعادة تسمية العمود
            cursor.execute("""
                ALTER TABLE location_equipment 
                RENAME COLUMN condition TO condition_status
            """)
            
            conn.commit()
            print("✅ تم إعادة تسمية العمود بنجاح من 'condition' إلى 'condition_status'")
            
        elif condition_status_exists and not condition_exists:
            print("✅ العمود 'condition_status' موجود بالفعل - لا حاجة لإصلاح")
            
        elif condition_exists and condition_status_exists:
            print("⚠️  كلا العمودين موجودان - سيتم حذف العمود القديم 'condition'")
            
            # نسخ البيانات من condition إلى condition_status إذا كان فارغاً
            cursor.execute("SELECT COUNT(*) FROM location_equipment WHERE condition_status IS NULL")
            null_count = cursor.fetchone()[0]
            
            if null_count > 0:
                cursor.execute("""
                    UPDATE location_equipment 
                    SET condition_status = condition 
                    WHERE condition_status IS NULL AND condition IS NOT NULL
                """)
                print(f"✅ تم نسخ {null_count} قيمة من العمود القديم")
            
            # حذف العمود القديم
            cursor.execute("ALTER TABLE location_equipment DROP COLUMN condition")
            conn.commit()
            print("✅ تم حذف العمود القديم 'condition'")
            
        else:
            print("❌ لم يتم العثور على أي من العمودين - هناك مشكلة في بنية الجدول")
            return False
        
        # التحقق من النتيجة النهائية
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'location_equipment' 
            ORDER BY ordinal_position
        """)
        
        columns = [row[0] for row in cursor.fetchall()]
        print(f"📋 الأعمدة النهائية في جدول location_equipment: {columns}")
        
        # اختبار استعلام بسيط للتأكد من عمل الجدول
        cursor.execute("SELECT COUNT(*) FROM location_equipment")
        count = cursor.fetchone()[0]
        print(f"📊 عدد المعدات في الجدول: {count}")
        
        if count > 0:
            cursor.execute("SELECT equipment_name, condition_status FROM location_equipment LIMIT 3")
            sample_data = cursor.fetchall()
            print("📄 عينة من البيانات:")
            for equipment_name, condition_status in sample_data:
                print(f"   - {equipment_name}: {condition_status}")
        
        conn.close()
        print("✅ تم إصلاح الجدول بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الجدول: {e}")
        return False

if __name__ == "__main__":
    success = fix_location_equipment_column()
    if success:
        print("\n🎉 تم إصلاح مشكلة عمود condition_status بنجاح!")
        print("يمكنك الآن تشغيل التطبيق دون أخطاء.")
    else:
        print("\n❌ فشل في إصلاح المشكلة. يرجى مراجعة الأخطاء أعلاه.")
        sys.exit(1)
