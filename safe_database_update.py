#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت آمن لتحديث قاعدة البيانات بدون فقدان البيانات الموجودة
يقوم بإضافة الجداول والأعمدة الناقصة فقط
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from datetime import datetime

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'database': 'military_warehouse',
    'user': 'postgres',
    'password': 'postgres'
}

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def table_exists(cursor, table_name):
    """فحص وجود جدول"""
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = %s
        );
    """, (table_name,))
    return cursor.fetchone()[0]

def column_exists(cursor, table_name, column_name):
    """فحص وجود عمود في جدول"""
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = %s 
            AND column_name = %s
        );
    """, (table_name, column_name))
    return cursor.fetchone()[0]

def get_table_row_count(cursor, table_name):
    """الحصول على عدد الصفوف في الجدول"""
    try:
        cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
        return cursor.fetchone()[0]
    except:
        return 0

def safe_create_table(cursor, table_name, create_sql):
    """إنشاء جدول بأمان (فقط إذا لم يكن موجوداً)"""
    if not table_exists(cursor, table_name):
        print(f"📋 إنشاء جدول جديد: {table_name}")
        cursor.execute(create_sql)
        print(f"✅ تم إنشاء جدول {table_name} بنجاح")
        return True
    else:
        row_count = get_table_row_count(cursor, table_name)
        print(f"ℹ️  الجدول {table_name} موجود بالفعل ({row_count} صف)")
        return False

def safe_add_column(cursor, table_name, column_name, column_definition):
    """إضافة عمود بأمان (فقط إذا لم يكن موجوداً)"""
    if table_exists(cursor, table_name):
        if not column_exists(cursor, table_name, column_name):
            print(f"📝 إضافة عمود جديد: {table_name}.{column_name}")
            cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition};")
            print(f"✅ تم إضافة العمود {column_name} إلى جدول {table_name}")
            return True
        else:
            print(f"ℹ️  العمود {table_name}.{column_name} موجود بالفعل")
            return False
    else:
        print(f"⚠️  الجدول {table_name} غير موجود، لا يمكن إضافة العمود")
        return False

def update_database_schema():
    """تحديث مخطط قاعدة البيانات بأمان"""
    
    print("🔄 بدء التحديث الآمن لقاعدة البيانات...")
    print("=" * 50)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # إنشاء نسخة احتياطية سريعة قبل التحديث
        print("💾 إنشاء نسخة احتياطية سريعة...")
        backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. جداول كشف الاستلامات (إذا لم تكن موجودة)
        print("\n📋 فحص جداول كشف الاستلامات...")
        
        # جدول بيانات الدوريات في كشف الاستلامات
        safe_create_table(cursor, 'receipts_patrol_data', """
            CREATE TABLE receipts_patrol_data (
                id SERIAL PRIMARY KEY,
                data_json TEXT NOT NULL,
                timestamp VARCHAR(50),
                created_by INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # جدول بيانات المناوبين في كشف الاستلامات
        safe_create_table(cursor, 'receipts_shifts_data', """
            CREATE TABLE receipts_shifts_data (
                id SERIAL PRIMARY KEY,
                data_json TEXT NOT NULL,
                timestamp VARCHAR(50),
                created_by INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # جدول مواقع الدوريات في كشف الاستلامات
        safe_create_table(cursor, 'receipts_patrol_locations', """
            CREATE TABLE receipts_patrol_locations (
                id SERIAL PRIMARY KEY,
                locations_json TEXT NOT NULL,
                timestamp VARCHAR(50),
                created_by INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # جدول مواقع المناوبين في كشف الاستلامات
        safe_create_table(cursor, 'receipts_shifts_locations', """
            CREATE TABLE receipts_shifts_locations (
                id SERIAL PRIMARY KEY,
                locations_json TEXT NOT NULL,
                timestamp VARCHAR(50),
                created_by INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # 2. فحص وإضافة أعمدة ناقصة في الجداول الموجودة
        print("\n📝 فحص الأعمدة الناقصة...")
        
        # إضافة أعمدة ناقصة لجدول المواقع (إذا كانت ناقصة)
        if table_exists(cursor, 'locations'):
            safe_add_column(cursor, 'locations', 'instructions_pdf', 'TEXT')
            safe_add_column(cursor, 'locations', 'pdf_filename', 'VARCHAR(255)')
            safe_add_column(cursor, 'locations', 'pdf_upload_date', 'TIMESTAMP')
        
        # إضافة أعمدة ناقصة لجدول المستخدمين (إذا كانت ناقصة)
        if table_exists(cursor, 'users'):
            safe_add_column(cursor, 'users', 'last_login', 'TIMESTAMP')
            safe_add_column(cursor, 'users', 'is_active', 'BOOLEAN DEFAULT TRUE')
        
        # 3. إنشاء فهارس للأداء (إذا لم تكن موجودة)
        print("\n🔍 إنشاء الفهارس للأداء...")
        
        indexes_to_create = [
            ("idx_receipts_patrol_data_created_by", "receipts_patrol_data", "created_by"),
            ("idx_receipts_shifts_data_created_by", "receipts_shifts_data", "created_by"),
            ("idx_receipts_patrol_locations_created_by", "receipts_patrol_locations", "created_by"),
            ("idx_receipts_shifts_locations_created_by", "receipts_shifts_locations", "created_by"),
        ]
        
        for index_name, table_name, column_name in indexes_to_create:
            if table_exists(cursor, table_name):
                try:
                    cursor.execute(f"""
                        CREATE INDEX IF NOT EXISTS {index_name} 
                        ON {table_name} ({column_name});
                    """)
                    print(f"✅ تم إنشاء فهرس: {index_name}")
                except Exception as e:
                    print(f"ℹ️  الفهرس {index_name} موجود بالفعل أو حدث خطأ: {e}")
        
        print("\n" + "=" * 50)
        print("✅ تم التحديث الآمن لقاعدة البيانات بنجاح!")
        print("📊 ملخص التحديث:")
        
        # عرض ملخص الجداول
        essential_tables = [
            'receipts_patrol_data',
            'receipts_shifts_data', 
            'receipts_patrol_locations',
            'receipts_shifts_locations'
        ]
        
        for table_name in essential_tables:
            if table_exists(cursor, table_name):
                row_count = get_table_row_count(cursor, table_name)
                print(f"   ✅ {table_name}: {row_count} صف")
            else:
                print(f"   ❌ {table_name}: غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ أثناء التحديث: {e}")
        return False
    
    finally:
        cursor.close()
        conn.close()

def check_database_status():
    """فحص حالة قاعدة البيانات قبل التحديث"""
    print("🔍 فحص حالة قاعدة البيانات الحالية...")

    conn = connect_to_database()
    if not conn:
        return False

    cursor = conn.cursor()

    try:
        # فحص الجداول الأساسية
        essential_tables = [
            'users', 'warehouses', 'weapons', 'personnel',
            'locations', 'receipts_patrol_data', 'receipts_shifts_data'
        ]

        existing_tables = []
        missing_tables = []
        tables_with_data = []

        for table_name in essential_tables:
            if table_exists(cursor, table_name):
                existing_tables.append(table_name)
                row_count = get_table_row_count(cursor, table_name)
                if row_count > 0:
                    tables_with_data.append((table_name, row_count))
            else:
                missing_tables.append(table_name)

        print(f"\n📊 تقرير حالة قاعدة البيانات:")
        print(f"   ✅ الجداول الموجودة: {len(existing_tables)}")
        print(f"   ❌ الجداول الناقصة: {len(missing_tables)}")
        print(f"   📈 الجداول التي تحتوي على بيانات: {len(tables_with_data)}")

        if tables_with_data:
            print(f"\n📋 الجداول التي تحتوي على بيانات:")
            for table_name, row_count in tables_with_data:
                print(f"   📊 {table_name}: {row_count} صف")

        if missing_tables:
            print(f"\n⚠️  الجداول الناقصة التي سيتم إنشاؤها:")
            for table_name in missing_tables:
                print(f"   📋 {table_name}")

        return True

    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

    finally:
        cursor.close()
        conn.close()

def main():
    """الدالة الرئيسية"""
    print("🛡️  سكريبت التحديث الآمن لقاعدة البيانات")
    print("📋 هذا السكريبت يضيف الجداول والأعمدة الناقصة فقط")
    print("🔒 لن يتم حذف أو تعديل أي بيانات موجودة")
    print("=" * 60)

    # فحص حالة قاعدة البيانات أولاً
    if not check_database_status():
        print("❌ فشل في فحص قاعدة البيانات")
        return

    # تأكيد من المستخدم
    response = input("\n❓ هل تريد المتابعة مع التحديث؟ (y/n): ").lower().strip()
    if response not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية")
        return

    success = update_database_schema()

    if success:
        print("\n🎉 تم التحديث بنجاح!")
        print("💡 يمكنك الآن استخدام الموقع بشكل طبيعي")
        print("📱 جميع البيانات الموجودة محفوظة ولم تتأثر")

        # فحص نهائي
        print("\n🔍 فحص نهائي للتأكد...")
        check_database_status()
    else:
        print("\n❌ فشل في التحديث")
        print("🔧 تحقق من إعدادات قاعدة البيانات")

if __name__ == "__main__":
    main()
