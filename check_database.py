#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import sys

def check_database():
    try:
        # الاتصال بقاعدة البيانات
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cur = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        print("=" * 50)
        
        # فحص الجداول الموجودة
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        
        tables = cur.fetchall()
        print(f"الجداول الموجودة في قاعدة البيانات ({len(tables)} جدول):")
        print("-" * 30)
        
        for table in tables:
            table_name = table[0]
            print(f"📋 {table_name}")
            
            # فحص عدد الصفوف في كل جدول
            try:
                cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cur.fetchone()[0]
                print(f"   📊 عدد الصفوف: {count}")
            except Exception as e:
                print(f"   ❌ خطأ في قراءة الجدول: {e}")
            
            print()
        
        # فحص الجداول المطلوبة
        required_tables = [
            'users', 'personnel', 'weapons', 'inventory', 'locations', 
            'receipts', 'duties', 'activities', 'statuses', 'warehouse_items'
        ]
        
        existing_table_names = [table[0] for table in tables]
        
        print("=" * 50)
        print("فحص الجداول المطلوبة:")
        print("-" * 30)
        
        missing_tables = []
        for required_table in required_tables:
            if required_table in existing_table_names:
                print(f"✅ {required_table} - موجود")
            else:
                print(f"❌ {required_table} - مفقود")
                missing_tables.append(required_table)
        
        if missing_tables:
            print(f"\n⚠️  الجداول المفقودة: {', '.join(missing_tables)}")
        else:
            print("\n✅ جميع الجداول المطلوبة موجودة")
        
        # فحص بنية بعض الجداول المهمة
        print("\n" + "=" * 50)
        print("فحص بنية الجداول:")
        print("-" * 30)
        
        important_tables = ['users', 'personnel', 'weapons', 'inventory']
        for table_name in important_tables:
            if table_name in existing_table_names:
                print(f"\n📋 بنية جدول {table_name}:")
                cur.execute(f"""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns 
                    WHERE table_name = '{table_name}'
                    ORDER BY ordinal_position;
                """)
                columns = cur.fetchall()
                for col in columns:
                    nullable = "يمكن أن يكون فارغ" if col[2] == 'YES' else "مطلوب"
                    print(f"   - {col[0]} ({col[1]}) - {nullable}")
        
        conn.close()
        
    except psycopg2.OperationalError as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        print("تأكد من أن PostgreSQL يعمل وأن قاعدة البيانات موجودة")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False
    
    return True

if __name__ == "__main__":
    check_database()
