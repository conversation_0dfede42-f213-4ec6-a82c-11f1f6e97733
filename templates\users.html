{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>إدارة المستخدمين</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة مستخدم جديد
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-users-cog"></i> المستخدمون</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المستخدم</th>
                        <th>الاسم الكامل</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>آخر تسجيل دخول</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.full_name }}</td>
                        <td>
                            {% if user.role == 'admin' or user.is_admin %}
                                مدير نظام
                            {% elif user.role == 'warehouse_manager' %}
                                مدير مستودع
                            {% elif user.role == 'inventory_manager' %}
                                مسؤول مخزون
                            {% elif user.role == 'company_duty' or user.user_role == 'مناوب السرية' %}
                                مناوب السرية
                            {% else %}
                                مراقب
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                            <span class="badge badge-success">نشط</span>
                            {% else %}
                            <span class="badge badge-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'لم يسجل دخول بعد' }}
                        </td>
                        <td>
                            <div class="btn-group">
                                {% if user.id != current_user.id %}
                                <a href="{{ url_for('auth.edit_user', user_id=user.id) }}" class="btn btn-sm btn-outline-info" title="تعديل المستخدم">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% else %}
                                <button class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% endif %}
                            </div>
                            <div class="btn-group">
                                {% if user.id != current_user.id %}
                                <a href="{{ url_for('auth.reset_user_password', user_id=user.id) }}" class="btn btn-sm btn-outline-warning" title="إعادة تعيين كلمة المرور">
                                    <i class="fas fa-key"></i>
                                </a>
                                {% else %}
                                <a href="{{ url_for('auth.change_password') }}" class="btn btn-sm btn-outline-warning" title="تغيير كلمة المرور">
                                    <i class="fas fa-key"></i>
                                </a>
                                {% endif %}
                            </div>
                            <div class="btn-group">
                                {% if user.id != current_user.id %}
                                <a href="{{ url_for('auth.toggle_user_status', user_id=user.id) }}"
                                    class="btn btn-sm {{ 'btn-outline-danger' if user.is_active else 'btn-outline-success' }}" title="{{ 'تعطيل' if user.is_active else 'تفعيل' }} المستخدم">
                                    <i class="fas {{ 'fa-user-slash' if user.is_active else 'fa-user-check' }}"></i>
                                </a>
                                {% else %}
                                <button class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="fas fa-user-shield"></i>
                                </button>
                                {% endif %}
                            </div>
                            <div class="btn-group">
                                {% if user.id != current_user.id %}
                                <form method="POST" action="{{ url_for('auth.delete_user', user_id=user.id) }}" onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف المستخدم">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% else %}
                                <button class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center py-3">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> لا يوجد مستخدمين حتى الآن
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}