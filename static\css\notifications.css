/* تنسيق حاوية الإشعارات */
#notifications-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column-reverse;
    gap: 10px;
    pointer-events: none;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 5px; /* مساحة للتمرير */
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

#notifications-container::-webkit-scrollbar {
    width: 5px;
}

#notifications-container::-webkit-scrollbar-track {
    background: transparent;
}

#notifications-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
}

/* تنسيق الإشعارات المحسنة - تصميم احترافي */
.notification {
    position: relative; /* تغيير من fixed إلى relative */
    min-width: 350px;
    max-width: 450px;
    padding: 16px 20px;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    direction: rtl;
    opacity: 0;
    transform: translateX(100px);
    animation: notification-slide-in 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: auto; /* تمكين التفاعل مع الإشعار */
}

.notification.closing {
    animation: notification-slide-out 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}

.notification-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.notification-icon {
    margin-left: 18px;
    font-size: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.notification:hover .notification-icon {
    transform: scale(1.1);
}

.notification-message {
    flex: 1;
    font-size: 15px;
    line-height: 1.5;
}

.notification-title {
    font-weight: 700;
    margin-bottom: 5px;
    font-size: 17px;
    letter-spacing: 0.3px;
}

.notification-close {
    margin-right: 12px;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.3s ease;
    font-size: 16px;
    background: none;
    border: none;
    color: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
}

.notification-close:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0);
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.notification-close:hover {
    opacity: 1;
}

.notification-close:hover:before {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1);
}

.notification-close i {
    position: relative;
    z-index: 1;
}

/* أنواع الإشعارات - تصميم محسن */
.notification-success {
    background-color: rgba(40, 167, 69, 0.95);
    color: white;
    border-right: 5px solid #1e7e34;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.notification-success .notification-icon {
    background-color: rgba(30, 126, 52, 0.5);
    color: #a3ffb8;
}

.notification-danger {
    background-color: rgba(220, 53, 69, 0.95);
    color: white;
    border-right: 5px solid #bd2130;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

.notification-danger .notification-icon {
    background-color: rgba(189, 33, 48, 0.5);
    color: #ffb3b9;
}

.notification-warning {
    background-color: rgba(255, 193, 7, 0.95);
    color: #212529;
    border-right: 5px solid #d39e00;
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

.notification-warning .notification-icon {
    background-color: rgba(211, 158, 0, 0.5);
    color: #806000;
}

.notification-info {
    background-color: rgba(23, 162, 184, 0.95);
    color: white;
    border-right: 5px solid #138496;
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
}

.notification-info .notification-icon {
    background-color: rgba(19, 132, 150, 0.5);
    color: #a8f0ff;
}

/* تأثيرات الظهور والاختفاء المحسنة - من اليمين */
@keyframes notification-slide-in {
    0% {
        opacity: 0;
        transform: translateX(100px) scale(0.9);
    }
    70% {
        transform: translateX(-5px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes notification-slide-out {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    30% {
        opacity: 0.7;
        transform: translateX(10px) scale(0.97);
    }
    100% {
        opacity: 0;
        transform: translateX(100px) scale(0.9);
    }
}

/* تعديلات للشاشات الصغيرة */
@media (max-width: 768px) {
    #notifications-container {
        bottom: 10px;
        right: 10px;
        max-width: calc(100vw - 20px);
        padding: 10px;
        gap: 8px;
    }

    .notification {
        min-width: 280px;
        max-width: 100%;
        width: 100%;
        padding: 14px 16px;
    }

    .notification-icon {
        width: 36px;
        height: 36px;
        font-size: 18px;
        margin-left: 14px;
    }

    .notification-title {
        font-size: 16px;
    }

    .notification-message {
        font-size: 14px;
    }
}

/* تعديلات للشاشات الصغيرة جدًا */
@media (max-width: 480px) {
    #notifications-container {
        bottom: 5px;
        right: 5px;
        max-width: calc(100vw - 10px);
        padding: 5px;
        gap: 6px;
    }

    .notification {
        min-width: auto;
        width: 100%;
        max-width: 100%;
        padding: 12px 14px;
    }

    .notification-icon {
        width: 32px;
        height: 32px;
        font-size: 16px;
        margin-left: 12px;
    }

    .notification-title {
        font-size: 15px;
    }

    .notification-message {
        font-size: 13px;
    }
}
