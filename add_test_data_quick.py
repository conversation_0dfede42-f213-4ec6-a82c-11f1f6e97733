#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة بيانات تجريبية سريعة لاختبار نظام كشف الواجبات
"""

from app import create_app
from models import Personnel, Location
from db import db

def add_quick_test_data():
    """إضافة بيانات تجريبية سريعة"""
    app = create_app()
    
    with app.app_context():
        print("🚀 إضافة بيانات تجريبية سريعة...")
        
        # إضافة مواقع تجريبية
        locations_data = [
            {'name': 'البوابة الرئيسية', 'type': 'أمني', 'serial_number': 'LOC001'},
            {'name': 'المخزن الرئيسي', 'type': 'إداري', 'serial_number': 'LOC002'},
            {'name': 'برج المراقبة', 'type': 'حراسة', 'serial_number': 'LOC003'},
        ]
        
        created_locations = []
        for loc_data in locations_data:
            # التحقق من عدم وجود الموقع
            existing = Location.query.filter_by(serial_number=loc_data['serial_number']).first()
            if not existing:
                location = Location(
                    name=loc_data['name'],
                    type=loc_data['type'],
                    serial_number=loc_data['serial_number'],
                    description=f"موقع {loc_data['name']}",
                    status='نشط'
                )
                db.session.add(location)
                db.session.flush()  # للحصول على ID
                created_locations.append(location)
                print(f"✅ تم إضافة موقع: {location.name} (ID: {location.id})")
            else:
                created_locations.append(existing)
                print(f"📍 موقع موجود: {existing.name} (ID: {existing.id})")
        
        # إضافة أفراد تجريبيين
        personnel_data = [
            {'name': 'أحمد محمد علي', 'rank': 'عريف', 'personnel_id': '1001'},
            {'name': 'محمد أحمد سالم', 'rank': 'جندي أول', 'personnel_id': '1002'},
            {'name': 'علي سالم أحمد', 'rank': 'رقيب', 'personnel_id': '1003'},
            {'name': 'سالم علي محمد', 'rank': 'عريف', 'personnel_id': '1004'},
            {'name': 'خالد عبدالله سعد', 'rank': 'جندي', 'personnel_id': '1005'},
            {'name': 'عبدالله خالد فهد', 'rank': 'رقيب أول', 'personnel_id': '1006'},
            {'name': 'فهد سعد خالد', 'rank': 'عريف', 'personnel_id': '1007'},
            {'name': 'سعد فهد عبدالله', 'rank': 'جندي أول', 'personnel_id': '1008'},
            {'name': 'ناصر عبدالعزيز فيصل', 'rank': 'رقيب', 'personnel_id': '1009'},
        ]
        
        # توزيع الأفراد على المواقع
        for i, person_data in enumerate(personnel_data):
            location = created_locations[i % len(created_locations)]
            
            # التحقق من عدم وجود الفرد
            existing = Personnel.query.filter_by(personnel_id=person_data['personnel_id']).first()
            if not existing:
                person = Personnel(
                    name=person_data['name'],
                    rank=person_data['rank'],
                    personnel_id=person_data['personnel_id'],
                    location_id=location.id,
                    phone=f"05{str(i+1).zfill(8)}",
                    status='نشط'
                )
                db.session.add(person)
                print(f"✅ تم إضافة فرد: {person.rank} {person.name} - موقع: {location.name}")
            else:
                print(f"👤 فرد موجود: {existing.rank} {existing.name}")
        
        db.session.commit()
        
        # عرض ملخص
        print("\n📊 ملخص البيانات:")
        for location in created_locations:
            personnel_count = Personnel.query.filter_by(location_id=location.id, status='نشط').count()
            print(f"📍 {location.name} (ID: {location.id}): {personnel_count} فرد")
        
        print("\n✅ تم الانتهاء من إضافة البيانات!")

if __name__ == '__main__':
    add_quick_test_data()
