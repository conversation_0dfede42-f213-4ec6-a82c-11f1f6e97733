from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_file
from flask_login import login_required, current_user
from sqlalchemy import or_
from datetime import datetime

from db import db
from models import ActivityLog, Warehouse
from utils import format_datetime_12h, get_activities_excel, export_to_excel

# Create the blueprint
activities_bp = Blueprint('activities', __name__, url_prefix='/activities')

@activities_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    per_page = 10  # Number of activities per page
    query = request.args.get('q', '')

    # Get warehouse IDs the user has access to
    if current_user.is_admin_role:
        warehouse_ids = [w.id for w in Warehouse.query.all()]
    else:
        warehouse_ids = [w.id for w in current_user.warehouses]

    # Base query - تعديل الاستعلام للتعامل مع warehouse_id الذي قد يكون None
    activities_query = ActivityLog.query.filter(
        (ActivityLog.warehouse_id.in_(warehouse_ids)) |
        (ActivityLog.warehouse_id.is_(None))
    )

    # Apply search filter if provided
    if query:
        activities_query = activities_query.filter(
            or_(
                ActivityLog.action.ilike(f'%{query}%'),
                ActivityLog.description.ilike(f'%{query}%')
            )
        )

    # Get paginated activities
    activities = activities_query.order_by(ActivityLog.timestamp.desc()).paginate(page=page, per_page=per_page)

    return render_template('activities/index.html',
                          activities=activities,
                          format_datetime_12h=format_datetime_12h,
                          title='سجل النشاطات')

@activities_bp.route('/export')
@login_required
def export():
    """تصدير سجل النشاطات إلى ملف Excel."""
    # الحصول على معامل البحث إذا كان موجودًا
    query = request.args.get('q', '')

    # الحصول على معرفات المستودعات التي يمكن للمستخدم الوصول إليها
    if current_user.is_admin_role:
        warehouse_ids = [w.id for w in Warehouse.query.all()]
    else:
        warehouse_ids = [w.id for w in current_user.warehouses]

    # الحصول على بيانات سجل النشاطات
    df = get_activities_excel(warehouse_ids, query)

    # إنشاء اسم الملف
    filename = f"activity_log_{datetime.now().strftime('%Y%m%d')}"

    # إنشاء ملف Excel
    excel_file = export_to_excel({'سجل النشاطات': df}, filename, "سجل النشاطات")

    # طباعة رسالة تصحيح للتأكد من أن الدالة تعمل بشكل صحيح
    print(f"تم إنشاء ملف Excel بنجاح: {filename}.xlsx")

    # تسجيل عملية التصدير
    default_warehouse_id = warehouse_ids[0] if warehouse_ids else 1

    log = ActivityLog(
        action="تصدير تقرير",
        description="تم تصدير سجل النشاطات",
        ip_address=request.remote_addr,
        user_id=current_user.id,
        warehouse_id=default_warehouse_id
    )
    db.session.add(log)
    db.session.commit()

    return send_file(
        excel_file,
        download_name=f"{filename}.xlsx",
        as_attachment=True,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@activities_bp.route('/delete_all', methods=['POST'])
@login_required
def delete_all():
    # Only admin users can delete all activity logs
    if not current_user.is_admin_role:
        flash('ليس لديك صلاحية لحذف سجلات النشاطات', 'danger')
        return redirect(url_for('activities.index'))

    try:
        # Get warehouse IDs the user has access to
        if current_user.is_admin_role:
            warehouse_ids = [w.id for w in Warehouse.query.all()]
        else:
            warehouse_ids = [w.id for w in current_user.warehouses]

        # Count records before deletion for confirmation
        records_to_delete = ActivityLog.query.filter(
            (ActivityLog.warehouse_id.in_(warehouse_ids)) |
            (ActivityLog.warehouse_id.is_(None))
        ).count()

        # Delete all activity logs for these warehouses AND records with NULL warehouse_id
        # This matches the same filter used in the index view
        ActivityLog.query.filter(
            (ActivityLog.warehouse_id.in_(warehouse_ids)) |
            (ActivityLog.warehouse_id.is_(None))
        ).delete(synchronize_session=False)

        db.session.commit()

        # Add a new log entry about the deletion
        new_log = ActivityLog(
            action='حذف سجلات',
            description=f'تم حذف جميع سجلات النشاطات ({records_to_delete} سجل)',
            user_id=current_user.id,
            warehouse_id=warehouse_ids[0] if warehouse_ids else 1  # Use the first warehouse or default to 1
        )
        db.session.add(new_log)
        db.session.commit()

        flash(f'تم حذف جميع سجلات النشاطات بنجاح ({records_to_delete} سجل)', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف سجلات النشاطات: {str(e)}', 'danger')

    return redirect(url_for('activities.index'))

@activities_bp.route('/count', methods=['GET'])
@login_required
def count():
    """Get the count of activity logs that would be deleted"""
    # Only admin users can access this
    if not current_user.is_admin_role:
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        # Get warehouse IDs the user has access to
        if current_user.is_admin_role:
            warehouse_ids = [w.id for w in Warehouse.query.all()]
        else:
            warehouse_ids = [w.id for w in current_user.warehouses]

        # Count records that would be deleted (same filter as delete_all)
        count = ActivityLog.query.filter(
            (ActivityLog.warehouse_id.in_(warehouse_ids)) |
            (ActivityLog.warehouse_id.is_(None))
        ).count()

        return jsonify({'count': count})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
