<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة الصفوف والأعمدة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(40, 167, 69, 0.8); 
            border-left-color: #28a745;
        }
        .error { 
            background: rgba(220, 53, 69, 0.8); 
            border-left-color: #dc3545;
        }
        .warning { 
            background: rgba(255, 193, 7, 0.8); 
            color: #000; 
            border-left-color: #ffc107;
        }
        .info { 
            background: rgba(23, 162, 184, 0.8); 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        .success-btn {
            background: linear-gradient(45deg, #28a745, #218838);
        }
        select, input {
            padding: 12px;
            border: none;
            border-radius: 8px;
            margin: 8px;
            background: rgba(255,255,255,0.9);
            color: #333;
            font-size: 14px;
            min-width: 200px;
        }
        .log {
            background: rgba(0,0,0,0.4);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .emoji {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        .test-table th, .test-table td {
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.2);
            text-align: center;
        }
        .test-table th {
            background: rgba(255,255,255,0.2);
            font-weight: bold;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>➕ اختبار إضافة الصفوف والأعمدة</h1>
        <p>فحص الحفاظ على البيانات عند إضافة صفوف وأعمدة جديدة</p>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="emoji">💾</div>
                <h3>حفظ البيانات</h3>
                <p>حفظ تلقائي قبل التعديل</p>
            </div>
            <div class="feature-card">
                <div class="emoji">🔄</div>
                <h3>استعادة ذكية</h3>
                <p>استعادة البيانات بعد التعديل</p>
            </div>
            <div class="feature-card">
                <div class="emoji">➕</div>
                <h3>إضافة آمنة</h3>
                <p>إضافة صفوف وأعمدة بأمان</p>
            </div>
            <div class="feature-card">
                <div class="emoji">🛡️</div>
                <h3>حماية البيانات</h3>
                <p>عدم فقدان أي بيانات</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 محاكاة جدول البيانات</h3>
            <table class="test-table" id="testTable">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>الموقع</th>
                        <th>الفرد 1</th>
                        <th>الفرد 2</th>
                        <th>ملاحظات</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>
                            <select class="location-select">
                                <option value="">اختر موقع</option>
                                <option value="البوابة الرئيسية">البوابة الرئيسية</option>
                                <option value="المستودعات">المستودعات</option>
                            </select>
                        </td>
                        <td>
                            <select class="personnel-select">
                                <option value="">اختر فرد</option>
                                <option value="أحمد محمد">أحمد محمد</option>
                                <option value="محمد أحمد">محمد أحمد</option>
                            </select>
                        </td>
                        <td>
                            <select class="personnel-select">
                                <option value="">اختر فرد</option>
                                <option value="عبدالله سالم">عبدالله سالم</option>
                                <option value="سالم عبدالله">سالم عبدالله</option>
                            </select>
                        </td>
                        <td><input type="text" placeholder="ملاحظات"></td>
                        <td>
                            <button onclick="addTestRow()" class="success-btn">➕ صف</button>
                            <button onclick="addTestColumn()" class="success-btn">➕ عمود</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="test-section">
            <h3>🔧 أدوات الاختبار</h3>
            <div>
                <button onclick="fillTestData()">📝 ملء بيانات تجريبية</button>
                <button onclick="saveTestData()">💾 حفظ البيانات</button>
                <button onclick="restoreTestData()">🔄 استعادة البيانات</button>
                <button onclick="clearTestData()" class="danger">🗑️ مسح البيانات</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testStatus">
                <div class="status info">⏳ جاري انتظار الاختبارات...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 سجل العمليات</h3>
            <div id="testLog" class="log">
                جاري تحميل سجل الاختبارات...
            </div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
        </div>
        
        <div class="test-section">
            <h3>🔗 روابط سريعة</h3>
            <button onclick="openDuties()">📋 فتح كشف الواجبات</button>
            <button onclick="runFullTest()">🧪 اختبار شامل</button>
            <button onclick="simulateUserActions()">👤 محاكاة إجراءات المستخدم</button>
        </div>
    </div>

    <script>
        let testData = {};
        let savedData = null;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = `<div style="color: ${colors[type]}; margin: 3px 0; padding: 8px; border-left: 3px solid ${colors[type]}; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += entry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('testStatus');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function saveCurrentTestData() {
            log('💾 حفظ البيانات الحالية...', 'info');
            savedData = {};
            
            const table = document.getElementById('testTable');
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach((row, rowIndex) => {
                savedData[rowIndex] = {};
                
                // حفظ المواقع
                const locationSelect = row.querySelector('.location-select');
                if (locationSelect && locationSelect.value) {
                    savedData[rowIndex].location = locationSelect.value;
                }
                
                // حفظ الأفراد
                const personnelSelects = row.querySelectorAll('.personnel-select');
                savedData[rowIndex].personnel = [];
                personnelSelects.forEach((select, colIndex) => {
                    savedData[rowIndex].personnel[colIndex] = select.value;
                });
                
                // حفظ النصوص
                const textInputs = row.querySelectorAll('input[type="text"]');
                savedData[rowIndex].texts = [];
                textInputs.forEach((input, colIndex) => {
                    savedData[rowIndex].texts[colIndex] = input.value;
                });
            });
            
            log(`✅ تم حفظ بيانات ${rows.length} صف`, 'success');
            return savedData;
        }
        
        function restoreTestData() {
            if (!savedData) {
                log('⚠️ لا توجد بيانات محفوظة للاستعادة', 'warning');
                return;
            }
            
            log('🔄 استعادة البيانات المحفوظة...', 'info');
            
            const table = document.getElementById('testTable');
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach((row, rowIndex) => {
                if (savedData[rowIndex]) {
                    // استعادة المواقع
                    if (savedData[rowIndex].location) {
                        const locationSelect = row.querySelector('.location-select');
                        if (locationSelect) {
                            locationSelect.value = savedData[rowIndex].location;
                        }
                    }
                    
                    // استعادة الأفراد
                    const personnelSelects = row.querySelectorAll('.personnel-select');
                    personnelSelects.forEach((select, colIndex) => {
                        if (savedData[rowIndex].personnel && savedData[rowIndex].personnel[colIndex]) {
                            select.value = savedData[rowIndex].personnel[colIndex];
                        }
                    });
                    
                    // استعادة النصوص
                    const textInputs = row.querySelectorAll('input[type="text"]');
                    textInputs.forEach((input, colIndex) => {
                        if (savedData[rowIndex].texts && savedData[rowIndex].texts[colIndex]) {
                            input.value = savedData[rowIndex].texts[colIndex];
                        }
                    });
                }
            });
            
            log('✅ تم استعادة البيانات بنجاح', 'success');
        }
        
        function addTestRow() {
            log('➕ إضافة صف جديد...', 'info');
            
            // حفظ البيانات الحالية
            saveCurrentTestData();
            
            const table = document.getElementById('testTable');
            const tbody = table.querySelector('tbody');
            const newRowIndex = tbody.children.length + 1;
            
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td>${newRowIndex}</td>
                <td>
                    <select class="location-select">
                        <option value="">اختر موقع</option>
                        <option value="البوابة الرئيسية">البوابة الرئيسية</option>
                        <option value="المستودعات">المستودعات</option>
                    </select>
                </td>
                <td>
                    <select class="personnel-select">
                        <option value="">اختر فرد</option>
                        <option value="أحمد محمد">أحمد محمد</option>
                        <option value="محمد أحمد">محمد أحمد</option>
                    </select>
                </td>
                <td>
                    <select class="personnel-select">
                        <option value="">اختر فرد</option>
                        <option value="عبدالله سالم">عبدالله سالم</option>
                        <option value="سالم عبدالله">سالم عبدالله</option>
                    </select>
                </td>
                <td><input type="text" placeholder="ملاحظات"></td>
                <td>
                    <button onclick="addTestRow()" class="success-btn">➕ صف</button>
                    <button onclick="addTestColumn()" class="success-btn">➕ عمود</button>
                </td>
            `;
            
            tbody.appendChild(newRow);
            
            // استعادة البيانات بعد قليل
            setTimeout(() => {
                restoreTestData();
                log('✅ تم إضافة الصف مع الحفاظ على البيانات', 'success');
                updateStatus('تم إضافة صف جديد بنجاح', 'success');
            }, 100);
        }
        
        function addTestColumn() {
            log('➕ إضافة عمود جديد...', 'info');
            
            // حفظ البيانات الحالية
            saveCurrentTestData();
            
            const table = document.getElementById('testTable');
            
            // إضافة عمود للرأس
            const headerRow = table.querySelector('thead tr');
            const newHeader = document.createElement('th');
            newHeader.textContent = 'عمود جديد';
            headerRow.insertBefore(newHeader, headerRow.lastElementChild);
            
            // إضافة خلايا للصفوف
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const newCell = document.createElement('td');
                newCell.innerHTML = '<input type="text" placeholder="بيانات جديدة">';
                row.insertBefore(newCell, row.lastElementChild);
            });
            
            // استعادة البيانات بعد قليل
            setTimeout(() => {
                restoreTestData();
                log('✅ تم إضافة العمود مع الحفاظ على البيانات', 'success');
                updateStatus('تم إضافة عمود جديد بنجاح', 'success');
            }, 100);
        }
        
        function fillTestData() {
            log('📝 ملء بيانات تجريبية...', 'info');
            
            const table = document.getElementById('testTable');
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach((row, index) => {
                const locationSelect = row.querySelector('.location-select');
                if (locationSelect) {
                    locationSelect.value = index % 2 === 0 ? 'البوابة الرئيسية' : 'المستودعات';
                }
                
                const personnelSelects = row.querySelectorAll('.personnel-select');
                if (personnelSelects[0]) personnelSelects[0].value = 'أحمد محمد';
                if (personnelSelects[1]) personnelSelects[1].value = 'عبدالله سالم';
                
                const textInput = row.querySelector('input[type="text"]');
                if (textInput) {
                    textInput.value = `ملاحظة للصف ${index + 1}`;
                }
            });
            
            log('✅ تم ملء البيانات التجريبية', 'success');
            updateStatus('تم ملء البيانات التجريبية', 'success');
        }
        
        function saveTestData() {
            const data = saveCurrentTestData();
            localStorage.setItem('testTableData', JSON.stringify(data));
            log('💾 تم حفظ البيانات في التخزين المحلي', 'success');
            updateStatus('تم حفظ البيانات', 'success');
        }
        
        function clearTestData() {
            const table = document.getElementById('testTable');
            const inputs = table.querySelectorAll('select, input');
            inputs.forEach(input => {
                input.value = '';
            });
            
            savedData = null;
            localStorage.removeItem('testTableData');
            
            log('🗑️ تم مسح جميع البيانات', 'warning');
            updateStatus('تم مسح البيانات', 'warning');
        }
        
        function runFullTest() {
            log('🧪 بدء الاختبار الشامل...', 'info');
            updateStatus('جاري تشغيل الاختبار الشامل...', 'info');
            
            const steps = [
                { action: 'ملء البيانات', func: fillTestData, delay: 500 },
                { action: 'حفظ البيانات', func: saveTestData, delay: 1000 },
                { action: 'إضافة صف', func: addTestRow, delay: 1500 },
                { action: 'إضافة عمود', func: addTestColumn, delay: 2500 },
                { action: 'التحقق من البيانات', func: () => {
                    log('🔍 التحقق من سلامة البيانات...', 'info');
                    const hasData = checkDataIntegrity();
                    if (hasData) {
                        log('✅ البيانات سليمة ومحفوظة', 'success');
                        updateStatus('✅ نجح الاختبار الشامل - البيانات محفوظة', 'success');
                    } else {
                        log('❌ فقدت بعض البيانات', 'error');
                        updateStatus('❌ فشل الاختبار - فقدت البيانات', 'error');
                    }
                }, delay: 3500 }
            ];
            
            steps.forEach((step, index) => {
                setTimeout(() => {
                    log(`👤 ${step.action}...`, 'info');
                    step.func();
                }, step.delay);
            });
        }
        
        function checkDataIntegrity() {
            const table = document.getElementById('testTable');
            const selects = table.querySelectorAll('select');
            let hasData = false;
            
            selects.forEach(select => {
                if (select.value && select.value !== '') {
                    hasData = true;
                }
            });
            
            return hasData;
        }
        
        function simulateUserActions() {
            log('👤 محاكاة إجراءات المستخدم...', 'info');
            runFullTest();
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('🧹 تم مسح السجل', 'info');
        }
        
        function openDuties() {
            window.open('/duties/', '_blank');
            log('🔗 تم فتح صفحة كشف الواجبات', 'info');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل صفحة اختبار إضافة الصفوف والأعمدة', 'success');
            updateStatus('النظام جاهز للاختبار', 'info');
            
            // تحميل البيانات المحفوظة إن وجدت
            const saved = localStorage.getItem('testTableData');
            if (saved) {
                savedData = JSON.parse(saved);
                log('📥 تم تحميل بيانات محفوظة', 'info');
            }
            
            // اختبار تلقائي بسيط
            setTimeout(() => {
                log('🔄 تشغيل اختبار تلقائي...', 'info');
                fillTestData();
            }, 2000);
        });
    </script>
</body>
</html>
