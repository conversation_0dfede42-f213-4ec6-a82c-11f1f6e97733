<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أسماء المواقع</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(40, 167, 69, 0.8); 
            border-left-color: #28a745;
        }
        .error { 
            background: rgba(220, 53, 69, 0.8); 
            border-left-color: #dc3545;
        }
        .warning { 
            background: rgba(255, 193, 7, 0.8); 
            color: #000; 
            border-left-color: #ffc107;
        }
        .info { 
            background: rgba(23, 162, 184, 0.8); 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        select, input {
            padding: 12px;
            border: none;
            border-radius: 8px;
            margin: 8px;
            background: rgba(255,255,255,0.9);
            color: #333;
            font-size: 14px;
            min-width: 200px;
        }
        .log {
            background: rgba(0,0,0,0.4);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .emoji {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📍 اختبار أسماء المواقع والأفراد</h1>
        <p>فحص عمل النظام مع أسماء المواقع بدلاً من الأرقام</p>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="emoji">📝</div>
                <h3>أسماء المواقع</h3>
                <p>عرض أسماء المواقع في القوائم</p>
            </div>
            <div class="feature-card">
                <div class="emoji">🔄</div>
                <h3>تحويل ذكي</h3>
                <p>تحويل الأسماء إلى أرقام للAPI</p>
            </div>
            <div class="feature-card">
                <div class="emoji">👥</div>
                <h3>تحميل الأفراد</h3>
                <p>تحميل أفراد الموقع المختار</p>
            </div>
            <div class="feature-card">
                <div class="emoji">💾</div>
                <h3>حفظ دائم</h3>
                <p>حفظ الاختيارات بالأسماء</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 اختبار اختيار المواقع</h3>
            <div>
                <label>اختر موقع:</label>
                <select id="locationSelect">
                    <option value="">اختر موقع الواجب</option>
                    <option value="البوابة الرئيسية">البوابة الرئيسية</option>
                    <option value="البوابة الشرقية">البوابة الشرقية</option>
                    <option value="المستودعات">المستودعات</option>
                    <option value="مبنى الإدارة">مبنى الإدارة</option>
                    <option value="موقف السيارات">موقف السيارات</option>
                </select>
                <button onclick="testLocationSelection()">🔍 اختبار الاختيار</button>
            </div>
            <div id="locationResult" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔄 اختبار التحويل</h3>
            <div>
                <label>اسم الموقع:</label>
                <input type="text" id="locationNameInput" placeholder="أدخل اسم موقع">
                <button onclick="testNameToId()">🔄 تحويل إلى رقم</button>
            </div>
            <div id="conversionResult" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testStatus">
                <div class="status info">⏳ جاري انتظار الاختبارات...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 سجل العمليات</h3>
            <div id="testLog" class="log">
                جاري تحميل سجل الاختبارات...
            </div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
        </div>
        
        <div class="test-section">
            <h3>🔗 روابط سريعة</h3>
            <button onclick="openDuties()">📋 فتح كشف الواجبات</button>
            <button onclick="testAllFeatures()">🧪 اختبار شامل</button>
            <button onclick="simulateUserFlow()">👤 محاكاة تدفق المستخدم</button>
        </div>
    </div>

    <script>
        // قاعدة بيانات المواقع المحاكاة
        const mockLocationsDatabase = [
            { id: 1, name: 'البوابة الرئيسية' },
            { id: 2, name: 'البوابة الشرقية' },
            { id: 3, name: 'المستودعات' },
            { id: 4, name: 'مبنى الإدارة' },
            { id: 5, name: 'موقف السيارات' },
            { id: 6, name: 'المنطقة الصناعية' },
            { id: 7, name: 'المنطقة التجارية' }
        ];
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = `<div style="color: ${colors[type]}; margin: 3px 0; padding: 8px; border-left: 3px solid ${colors[type]}; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += entry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('testStatus');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // محاكاة دالة البحث عن الموقع
        function findLocationByName(locationName) {
            return mockLocationsDatabase.find(loc => loc.name === locationName);
        }
        
        function testLocationSelection() {
            const selectedLocation = document.getElementById('locationSelect').value;
            const resultDiv = document.getElementById('locationResult');
            
            if (!selectedLocation) {
                resultDiv.innerHTML = '<div class="status warning">⚠️ لم يتم اختيار موقع</div>';
                resultDiv.style.display = 'block';
                log('⚠️ لم يتم اختيار موقع', 'warning');
                return;
            }
            
            const location = findLocationByName(selectedLocation);
            
            if (location) {
                resultDiv.innerHTML = `
                    <div class="status success">
                        ✅ تم العثور على الموقع<br>
                        <strong>الاسم:</strong> ${location.name}<br>
                        <strong>الرقم:</strong> ${location.id}
                    </div>
                `;
                log(`✅ تم اختيار موقع: "${selectedLocation}" -> ID: ${location.id}`, 'success');
                updateStatus(`تم اختيار الموقع بنجاح: ${selectedLocation}`, 'success');
                
                // محاكاة تحميل الأفراد
                setTimeout(() => {
                    log(`🔄 محاكاة تحميل أفراد الموقع ${location.id}...`, 'info');
                    setTimeout(() => {
                        log(`✅ تم تحميل أفراد الموقع ${location.name} بنجاح`, 'success');
                    }, 1000);
                }, 500);
                
            } else {
                resultDiv.innerHTML = '<div class="status error">❌ لم يتم العثور على الموقع</div>';
                log(`❌ لم يتم العثور على الموقع: "${selectedLocation}"`, 'error');
                updateStatus('خطأ: لم يتم العثور على الموقع', 'error');
            }
            
            resultDiv.style.display = 'block';
        }
        
        function testNameToId() {
            const locationName = document.getElementById('locationNameInput').value.trim();
            const resultDiv = document.getElementById('conversionResult');
            
            if (!locationName) {
                resultDiv.innerHTML = '<div class="status warning">⚠️ أدخل اسم موقع</div>';
                resultDiv.style.display = 'block';
                return;
            }
            
            const location = findLocationByName(locationName);
            
            if (location) {
                resultDiv.innerHTML = `
                    <div class="status success">
                        ✅ تحويل ناجح<br>
                        <strong>"${locationName}"</strong> → <strong>ID: ${location.id}</strong>
                    </div>
                `;
                log(`🔄 تحويل: "${locationName}" -> ${location.id}`, 'success');
            } else {
                resultDiv.innerHTML = `
                    <div class="status error">
                        ❌ فشل التحويل<br>
                        الموقع "${locationName}" غير موجود
                    </div>
                `;
                log(`❌ فشل تحويل: "${locationName}"`, 'error');
            }
            
            resultDiv.style.display = 'block';
        }
        
        function testAllFeatures() {
            log('🧪 بدء الاختبار الشامل...', 'info');
            updateStatus('جاري تشغيل الاختبار الشامل...', 'info');
            
            let testsPassed = 0;
            let totalTests = 0;
            
            // اختبار 1: البحث عن مواقع موجودة
            mockLocationsDatabase.forEach((location, index) => {
                setTimeout(() => {
                    totalTests++;
                    const found = findLocationByName(location.name);
                    if (found && found.id === location.id) {
                        testsPassed++;
                        log(`✅ اختبار ${totalTests}: "${location.name}" -> ${location.id}`, 'success');
                    } else {
                        log(`❌ اختبار ${totalTests}: فشل في "${location.name}"`, 'error');
                    }
                    
                    // النتيجة النهائية
                    if (index === mockLocationsDatabase.length - 1) {
                        setTimeout(() => {
                            const successRate = (testsPassed / totalTests * 100).toFixed(1);
                            if (testsPassed === totalTests) {
                                updateStatus(`✅ نجح الاختبار الشامل: ${testsPassed}/${totalTests} (${successRate}%)`, 'success');
                                log(`🎉 نجح جميع الاختبارات: ${testsPassed}/${totalTests}`, 'success');
                            } else {
                                updateStatus(`⚠️ اختبار جزئي: ${testsPassed}/${totalTests} (${successRate}%)`, 'warning');
                                log(`⚠️ نجح ${testsPassed} من ${totalTests} اختبار`, 'warning');
                            }
                        }, 500);
                    }
                }, index * 200);
            });
        }
        
        function simulateUserFlow() {
            log('👤 محاكاة تدفق المستخدم...', 'info');
            updateStatus('جاري محاكاة تدفق المستخدم...', 'info');
            
            const steps = [
                { action: 'فتح الصفحة', delay: 500 },
                { action: 'تحميل قائمة المواقع', delay: 1000 },
                { action: 'اختيار موقع "البوابة الرئيسية"', delay: 1500 },
                { action: 'تحميل أفراد الموقع', delay: 2000 },
                { action: 'اختيار فرد', delay: 2500 },
                { action: 'حفظ البيانات', delay: 3000 },
                { action: 'تأكيد الحفظ', delay: 3500 }
            ];
            
            steps.forEach((step, index) => {
                setTimeout(() => {
                    log(`👤 ${step.action}...`, 'info');
                    
                    if (index === steps.length - 1) {
                        setTimeout(() => {
                            updateStatus('✅ تم إكمال محاكاة تدفق المستخدم بنجاح', 'success');
                            log('🎉 تم إكمال جميع خطوات المستخدم بنجاح', 'success');
                        }, 500);
                    }
                }, step.delay);
            });
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('🧹 تم مسح السجل', 'info');
        }
        
        function openDuties() {
            window.open('/duties/', '_blank');
            log('🔗 تم فتح صفحة كشف الواجبات', 'info');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل صفحة اختبار أسماء المواقع', 'success');
            updateStatus('النظام جاهز للاختبار', 'info');
            
            // اختبار تلقائي سريع
            setTimeout(() => {
                log('🔄 تشغيل اختبار تلقائي للمواقع...', 'info');
                testAllFeatures();
            }, 2000);
        });
        
        // اختبار عند تغيير الموقع
        document.getElementById('locationSelect').addEventListener('change', function() {
            if (this.value) {
                testLocationSelection();
            }
        });
        
        // اختبار عند الضغط على Enter
        document.getElementById('locationNameInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testNameToId();
            }
        });
    </script>
</body>
</html>
