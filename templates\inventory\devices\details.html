{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            تفاصيل الجهاز: {{ device.name }}
            <small class="text-muted">{{ device.serial_number or "بدون رقم تسلسلي" }}</small>
        </h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('inventory.edit_device', device_id=device.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> تعديل البيانات
        </a>
        <a href="{{ url_for('warehouse.dashboard') }}" class="btn btn-outline-secondary ml-2">
            <i class="fas fa-arrow-right"></i> العودة للصفحة الرئيسية
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> المعلومات الأساسية</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4 font-weight-bold">اسم الجهاز:</div>
                    <div class="col-md-8">{{ device.name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 font-weight-bold">النوع:</div>
                    <div class="col-md-8">
                        {% if device.type == 'computer' %}
                        <i class="fas fa-laptop text-primary"></i> كمبيوتر
                        {% elif device.type == 'printer' %}
                        <i class="fas fa-print text-success"></i> طابعة
                        {% elif device.type == 'scanner' %}
                        <i class="fas fa-scanner text-info"></i> سكانر
                        {% else %}
                        <i class="fas fa-hdd text-secondary"></i> {{ device.type }}
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 font-weight-bold">الموديل:</div>
                    <div class="col-md-8">{{ device.model or '-' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 font-weight-bold">الرقم التسلسلي:</div>
                    <div class="col-md-8">{{ device.serial_number or '-' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 font-weight-bold">الحالة:</div>
                    <div class="col-md-8">
                        {% if device.status == 'سليم' %}
                        <span class="badge badge-success">{{ device.status }}</span>
                        {% elif device.status == 'عطل' %}
                        <span class="badge badge-danger">{{ device.status }}</span>
                        {% elif device.status == 'تحت الصيانة' %}
                        <span class="badge badge-warning">{{ device.status }}</span>
                        {% else %}
                        <span class="badge badge-secondary">{{ device.status }}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 font-weight-bold">المستودع:</div>
                    <div class="col-md-8">{{ device.warehouse.name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 font-weight-bold">ملاحظات:</div>
                    <div class="col-md-8">{{ device.notes or '-' }}</div>
                </div>
                <div class="row">
                    <div class="col-md-4 font-weight-bold">تاريخ الإضافة:</div>
                    <div class="col-md-8">{{ device.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-tools"></i> سجل الصيانة</h5>
                    <a href="{{ url_for('inventory.add_device_maintenance', device_id=device.id) }}"
                        class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus"></i> إضافة صيانة
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>نوع الصيانة</th>
                                <th>الوصف</th>
                                <th>تاريخ البدء</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th>التكلفة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in device.maintenance_records %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    {% if record.maintenance_type == 'routine' %}
                                    <span>صيانة دورية</span>
                                    {% elif record.maintenance_type == 'repair' %}
                                    <span>إصلاح</span>
                                    {% elif record.maintenance_type == 'upgrade' %}
                                    <span>تحديث</span>
                                    {% elif record.maintenance_type == 'inspection' %}
                                    <span>فحص</span>
                                    {% else %}
                                    <span>{{ record.maintenance_type }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ record.description }}</td>
                                <td>{{ record.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ record.end_date.strftime('%Y-%m-%d') if record.end_date else '-' }}</td>
                                <td>
                                    {% if record.status == 'ongoing' %}
                                    <span class="badge badge-warning">جارية</span>
                                    {% elif record.status == 'completed' %}
                                    <span class="badge badge-success">مكتملة</span>
                                    {% elif record.status == 'cancelled' %}
                                    <span class="badge badge-danger">ملغاة</span>
                                    {% endif %}
                                </td>
                                <td>{{ record.cost if record.cost else '-' }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center py-3">
                                    <div class="alert alert-info mb-0">لا يوجد سجل صيانة</div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-cog"></i> الإجراءات السريعة</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="{{ url_for('inventory.add_device_maintenance', device_id=device.id) }}"
                        class="list-group-item list-group-item-action">
                        <i class="fas fa-tools text-primary mr-2"></i> إضافة صيانة جديدة
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" id="update-status-operational">
                        <i class="fas fa-check-circle text-success mr-2"></i> تعيين كسليم
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" id="update-status-maintenance">
                        <i class="fas fa-wrench text-warning mr-2"></i> تعيين تحت الصيانة
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" id="update-status-damaged">
                        <i class="fas fa-times-circle text-danger mr-2"></i> تعيين كعاطل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Status update handlers
        document.getElementById('update-status-operational').addEventListener('click', function (e) {
            e.preventDefault();
            if (confirm('هل أنت متأكد من تغيير حالة الجهاز إلى "سليم"؟')) {
                fetch('{{ url_for("inventory.update_device_status", device_id=device.id) }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        status: 'سليم'
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ في تحديث الحالة');
                    });
            }
        });

        document.getElementById('update-status-maintenance').addEventListener('click', function (e) {
            e.preventDefault();
            if (confirm('هل أنت متأكد من تغيير حالة الجهاز إلى "تحت الصيانة"؟')) {
                fetch('{{ url_for("inventory.update_device_status", device_id=device.id) }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        status: 'تحت الصيانة'
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ في تحديث الحالة');
                    });
            }
        });

        document.getElementById('update-status-damaged').addEventListener('click', function (e) {
            e.preventDefault();
            if (confirm('هل أنت متأكد من تغيير حالة الجهاز إلى "عطل"؟')) {
                fetch('{{ url_for("inventory.update_device_status", device_id=device.id) }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        status: 'عطل'
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ في تحديث الحالة');
                    });
            }
        });
    });
</script>
{% endblock %}