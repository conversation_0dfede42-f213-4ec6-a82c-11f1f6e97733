{% extends "base.html" %}

{% block title %}تعديل الصنف - {{ item.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-edit text-warning"></i>
                    تعديل الصنف: {{ item.name }}
                </h2>
                <div>
                    <a href="{{ url_for('inventory_management.item_details', item_id=item.id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للتفاصيل
                    </a>
                    <a href="{{ url_for('inventory_management.items') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list"></i> قائمة الأصناف
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        تعديل بيانات الصنف
                        <span class="badge 
                            {% if item.category == 'ملبوسات' %}bg-success
                            {% elif item.category == 'معدات' %}bg-info
                            {% elif item.category == 'ذخيرة' %}bg-danger
                            {% else %}bg-secondary{% endif %}">
                            {{ item.category }}
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">المعلومات الأساسية</h6>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.item_code.label(class="form-label required") }}
                                    {{ form.item_code(class="form-control") }}
                                    {% if form.item_code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.item_code.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-8 mb-3">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label required") }}
                                    {{ form.name(class="form-control") }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.category.label(class="form-label required") }}
                                    {{ form.category(class="form-control", id="category") }}
                                    {% if form.category.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.category.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.subcategory.label(class="form-label") }}
                                    {{ form.subcategory(class="form-control", id="subcategory") }}
                                    {% if form.subcategory.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.subcategory.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.warehouse_id.label(class="form-label required") }}
                                    {{ form.warehouse_id(class="form-control") }}
                                    {% if form.warehouse_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.warehouse_id.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Product Details -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">تفاصيل المنتج</h6>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.brand.label(class="form-label") }}
                                    {{ form.brand(class="form-control") }}
                                    {% if form.brand.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.brand.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.model.label(class="form-label") }}
                                    {{ form.model(class="form-control") }}
                                    {% if form.model.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.model.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.size.label(class="form-label") }}
                                    {{ form.size(class="form-control") }}
                                    {% if form.size.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.size.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-group">
                                    {{ form.color.label(class="form-label") }}
                                    {{ form.color(class="form-control") }}
                                    {% if form.color.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.color.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.material.label(class="form-label") }}
                                    {{ form.material(class="form-control") }}
                                    {% if form.material.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.material.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.supplier.label(class="form-label") }}
                                    {{ form.supplier(class="form-control") }}
                                    {% if form.supplier.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.supplier.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Quantity Information -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">معلومات الكمية</h6>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.quantity_in_stock.label(class="form-label required") }}
                                    {{ form.quantity_in_stock(class="form-control") }}
                                    {% if form.quantity_in_stock.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.quantity_in_stock.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.minimum_stock.label(class="form-label") }}
                                    {{ form.minimum_stock(class="form-control") }}
                                    {% if form.minimum_stock.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.minimum_stock.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.maximum_stock.label(class="form-label") }}
                                    {{ form.maximum_stock(class="form-control") }}
                                    {% if form.maximum_stock.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.maximum_stock.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.unit_cost.label(class="form-label") }}
                                    {{ form.unit_cost(class="form-control") }}
                                    {% if form.unit_cost.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.unit_cost.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.status.label(class="form-label required") }}
                                    {{ form.status(class="form-control") }}
                                    {% if form.status.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.status.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Dates Information -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">معلومات التواريخ</h6>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.manufacture_date.label(class="form-label") }}
                                    {{ form.manufacture_date(class="form-control", type="date") }}
                                    {% if form.manufacture_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.manufacture_date.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.expiry_date.label(class="form-label") }}
                                    {{ form.expiry_date(class="form-control", type="date") }}
                                    {% if form.expiry_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.expiry_date.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    {{ form.purchase_date.label(class="form-label") }}
                                    {{ form.purchase_date(class="form-control", type="date") }}
                                    {% if form.purchase_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.purchase_date.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">معلومات الموقع</h6>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.location.label(class="form-label") }}
                                    {{ form.location(class="form-control") }}
                                    {% if form.location.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.location.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.shelf_number.label(class="form-label") }}
                                    {{ form.shelf_number(class="form-control") }}
                                    {% if form.shelf_number.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.shelf_number.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">معلومات إضافية</h6>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.batch_number.label(class="form-label") }}
                                    {{ form.batch_number(class="form-control") }}
                                    {% if form.batch_number.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.batch_number.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.serial_numbers.label(class="form-label") }}
                                    {{ form.serial_numbers(class="form-control", rows="3") }}
                                    {% if form.serial_numbers.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.serial_numbers.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">أدخل كل رقم تسلسلي في سطر منفصل</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.specifications.label(class="form-label") }}
                                    {{ form.specifications(class="form-control", rows="4") }}
                                    {% if form.specifications.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.specifications.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    {{ form.notes.label(class="form-label") }}
                                    {{ form.notes(class="form-control", rows="4") }}
                                    {% if form.notes.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.notes.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{{ url_for('inventory_management.item_details', item_id=item.id) }}" class="btn btn-secondary me-2">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    {{ form.submit(class="btn btn-warning") }}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Info Alert -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> معلومات التعديل:</h6>
                <ul class="mb-0">
                    <li>يمكنك تعديل جميع بيانات الصنف</li>
                    <li>تأكد من صحة كود الصنف قبل الحفظ</li>
                    <li>سيتم حفظ تاريخ آخر تعديل تلقائياً</li>
                    <li>سيتم حساب القيمة الإجمالية تلقائياً عند وجود تكلفة الوحدة والكمية</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تفعيل التركيز على حقل الاسم
    $('#name').focus();

    // تأكيد قبل الحفظ
    $('form').submit(function(e) {
        if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
            e.preventDefault();
        }
    });

    // تحديث الفئات الفرعية عند تغيير الفئة الرئيسية
    $('#category').change(function() {
        var category = $(this).val();
        var subcategoryField = $('#subcategory');

        if (category) {
            $.get('/inventory_management/api/subcategories/' + category, function(data) {
                subcategoryField.empty();
                subcategoryField.append('<option value="">اختر الفئة الفرعية</option>');
                $.each(data, function(index, subcategory) {
                    subcategoryField.append('<option value="' + subcategory + '">' + subcategory + '</option>');
                });
            });
        } else {
            subcategoryField.empty();
            subcategoryField.append('<option value="">اختر الفئة الفرعية</option>');
        }
    });

    // تحديد الفئات الفرعية المتاحة عند تحميل الصفحة
    var currentCategory = $('#category').val();
    if (currentCategory) {
        var currentSubcategory = $('#subcategory').val();
        $.get('/inventory_management/api/subcategories/' + currentCategory, function(data) {
            var subcategoryField = $('#subcategory');
            subcategoryField.empty();
            subcategoryField.append('<option value="">اختر الفئة الفرعية</option>');
            $.each(data, function(index, subcategory) {
                var selected = (subcategory === currentSubcategory) ? 'selected' : '';
                subcategoryField.append('<option value="' + subcategory + '" ' + selected + '>' + subcategory + '</option>');
            });
        });
    }
});
</script>
{% endblock %}
