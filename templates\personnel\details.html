{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            تفاصيل الفرد: {{ personnel.name }}
            <small class="text-muted">{{ personnel.personnel_id }}</small>
        </h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('personnel.edit', personnel_id=personnel.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> تعديل البيانات
        </a>
        <a href="{{ url_for('personnel.index') }}" class="btn btn-outline-secondary ml-2">
            <i class="fas fa-arrow-right"></i> العودة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> المعلومات الأساسية</h5>
            </div>
            <div class="card-body">
                <table class="table table-details">
                    <tbody>
                        <tr>
                            <th class="w-25">الرقم العسكري:</th>
                            <td>{{ personnel.personnel_id }}</td>
                        </tr>
                        <tr>
                            <th>الاسم:</th>
                            <td>{{ personnel.name }}</td>
                        </tr>
                        <tr>
                            <th>الرتبة:</th>
                            <td>{{ personnel.rank }}</td>
                        </tr>
                        <tr>
                            <th>الحالة:</th>
                            <td>
                                {% if personnel.status == 'نشط' %}
                                <span class="badge badge-success">{{ personnel.status }}</span>
                                {% elif personnel.status == 'إجازة' %}
                                <span class="badge badge-warning">{{ personnel.status }}</span>
                                {% elif personnel.status == 'مهمة' %}
                                <span class="badge badge-mission">{{ personnel.status }}</span>
                                {% elif personnel.status == 'دورة' %}
                                <span class="badge badge-danger">{{ personnel.status }}</span>
                                {% elif personnel.status == 'مستلم' %}
                                <span class="badge badge-recipient">{{ personnel.status }}</span>
                                {% elif personnel.status == 'رماية' %}
                                <span class="badge badge-shooting">{{ personnel.status }}</span>
                                {% else %}
                                <span class="badge badge-secondary">{{ personnel.status }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>رقم الهوية الوطنية:</th>
                            <td>{{ personnel.phone or '-' }}</td>
                        </tr>
                        <tr>
                            <th>المستودع:</th>
                            <td>{{ personnel.warehouse.name }}</td>
                        </tr>
                        <tr>
                            <th>ملاحظات:</th>
                            <td>{{ personnel.notes or '-' }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء:</th>
                            <td>{{ personnel.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-exchange-alt"></i> معاملات الأسلحة</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>السلاح</th>
                                <th>نوع المعاملة</th>
                                <th>التاريخ</th>
                                <th>المستودع</th>
                                <th>المستخدم</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in personnel.transactions %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ transaction.weapon.name }} ({{ transaction.weapon.serial_number }})</td>
                                <td>
                                    {% if transaction.transaction_type == 'checkout' %}
                                    <span class="badge badge-recipient">تسليم</span>
                                    {% elif transaction.transaction_type == 'return' %}
                                    <span class="badge badge-success">استلام</span>
                                    {% elif transaction.transaction_type == 'transfer' %}
                                    <span class="badge badge-recipient">نقل</span>
                                    {% endif %}
                                </td>
                                <td>{{ transaction.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    {% if transaction.transaction_type == 'transfer' %}
                                    {{ transaction.source_warehouse.name }} → {{ transaction.target_warehouse.name }}
                                    {% else %}
                                    {{ transaction.source_warehouse.name }}
                                    {% endif %}
                                </td>
                                <td>{{ transaction.user.username }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-3">
                                    <div class="alert alert-info mb-0">لا توجد معاملات</div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-crosshairs"></i> الأسلحة المخصصة</h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    {% for weapon in personnel.weapons %}
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="font-weight-bold">{{ weapon.name }}</div>
                                <small class="text-muted">{{ weapon.serial_number }}</small>
                            </div>
                            <div>
                                {% if weapon.status == 'نشط' %}
                                <span class="badge badge-success">{{ weapon.status }}</span>
                                {% elif weapon.status == 'إجازة' %}
                                <span class="badge badge-warning">{{ weapon.status }}</span>
                                {% elif weapon.status == 'مهمة' %}
                                <span class="badge badge-mission">{{ weapon.status }}</span>
                                {% elif weapon.status == 'صيانة' %}
                                <span class="badge badge-maintenance">{{ weapon.status }}</span>
                                {% elif weapon.status == 'تالف' %}
                                <span class="badge badge-danger">{{ weapon.status }}</span>
                                {% elif weapon.status == 'دورة' %}
                                <span class="badge badge-danger">{{ weapon.status }}</span>
                                {% elif weapon.status == 'شاغر' %}
                                <span class="badge badge-dark">{{ weapon.status }}</span>
                                {% elif weapon.status == 'رماية' %}
                                <span class="badge badge-shooting">{{ weapon.status }}</span>
                                {% elif weapon.status == 'مستلم' %}
                                <span class="badge badge-recipient">{{ weapon.status }}</span>
                                {% else %}
                                <span class="badge badge-secondary">{{ weapon.status }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="list-group-item">
                        <div class="alert alert-info mb-0">لا توجد أسلحة مخصصة</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cog"></i> الإجراءات السريعة</h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='نشط') }}"
                        class="list-group-item list-group-item-action">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle text-success ml-2"></i>
                            <span>تعيين كنشط</span>
                        </div>
                    </a>
                    <a href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='إجازة') }}"
                        class="list-group-item list-group-item-action">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-calendar text-warning ml-2"></i>
                            <span>تعيين في إجازة</span>
                        </div>
                    </a>
                    <a href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='مهمة') }}"
                        class="list-group-item list-group-item-action">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-tasks ml-2" style="color: #fd7e14;"></i>
                            <span>تعيين في مهمة</span>
                        </div>
                    </a>
                    <a href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='دورة') }}"
                        class="list-group-item list-group-item-action">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-graduation-cap text-danger ml-2"></i>
                            <span>تعيين في دورة</span>
                        </div>
                    </a>
                    <a href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='رماية') }}"
                        class="list-group-item list-group-item-action">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-bullseye ml-2" style="color: #C8BBBE;"></i>
                            <span>تعيين في رماية</span>
                        </div>
                    </a>
                    <a href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='مستلم') }}"
                        class="list-group-item list-group-item-action">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user-clock text-primary ml-2"></i>
                            <span>تعيين كمستلم</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}