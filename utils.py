import os
import uuid
import json
import qrcode
import shutil
import zipfile
import pandas as pd
from io import BytesIO, StringIO
import base64
from datetime import datetime, timedelta
try:
    from hijri_converter import <PERSON><PERSON><PERSON>, Gregorian
    HIJRI_AVAILABLE = True
except ImportError:
    HIJRI_AVAILABLE = False
    # Create dummy classes to prevent errors
    class Hijri:
        pass
    class Gregorian:
        def __init__(self, year, month, day):
            pass
        def to_hijri(self):
            return type('obj', (object,), {'day': 9, 'month': 12, 'year': 1446})
from datetime_utils import get_saudi_now
import pytz
from flask import current_app, send_file, jsonify, Flask
from werkzeug.utils import secure_filename
from sqlalchemy import func, text, or_
from sqlalchemy.orm import joinedload
from markupsafe import Markup
import calendar

from db import db
from models import (
    Weapon, Personnel, WeaponTransaction, Warehouse,
    ActivityLog, MaintenanceRecord, Audit, AuditItem,
    BackupRecord, BackupSchedule, Device, DeviceMaintenanceRecord,
    User, InventoryItem, InventoryTransaction, WeeklyReport,
    user_warehouse, personnel_weapon,
    ReceiptData, ReceiptLocations, PatrolData, PatrolLocations,
    ShiftsData, ShiftsLocations
)
import logging

# QR Code generation
def generate_qr_code(data):
    """Generate a QR code as a base64 encoded string."""
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        buffered = BytesIO()
        img.save(buffered)
        img_str = base64.b64encode(buffered.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"

    except (ImportError, Exception) as e:
        # Fallback: return data for JavaScript generation
        return f"data:text/plain;base64,{base64.b64encode(str(data).encode()).decode()}"

# Barcode generation
def generate_barcode(data):
    """Generate a barcode as a base64 encoded string using the serial number."""
    try:
        from barcode import Code128
        from barcode.writer import ImageWriter

        # Create barcode with the serial number
        code = Code128(str(data), writer=ImageWriter())

        # Generate barcode image
        buffered = BytesIO()
        code.write(buffered)
        buffered.seek(0)

        # Convert to base64
        img_str = base64.b64encode(buffered.getvalue()).decode()
        return f"data:image/png;base64,{img_str}"

    except (ImportError, Exception) as e:
        # Fallback: return a simple text-based representation
        # This avoids the PIL dependency issue
        return f"data:text/plain;base64,{base64.b64encode(str(data).encode()).decode()}"

# Timezone conversion utilities
def format_saudi_time(dt):
    """Format datetime to Saudi time string in 12-hour format"""
    if dt is None:
        return 'غير محدد'

    try:
        # If the datetime is naive, assume it's already in Saudi time
        if dt.tzinfo is None:
            # Format in 12-hour format with Arabic AM/PM
            hour = dt.hour
            minute = dt.minute

            if hour == 0:
                hour_12 = 12
                period = 'ص'  # صباحاً
            elif hour < 12:
                hour_12 = hour
                period = 'ص'  # صباحاً
            elif hour == 12:
                hour_12 = 12
                period = 'م'  # مساءً
            else:
                hour_12 = hour - 12
                period = 'م'  # مساءً

            return f"{dt.strftime('%Y-%m-%d')} {hour_12}:{minute:02d} {period}"

        # If it has timezone info, convert to Saudi time first
        saudi_tz = pytz.timezone('Asia/Riyadh')
        if dt.tzinfo != saudi_tz:
            dt = dt.astimezone(saudi_tz)

        # Then format in 12-hour format
        hour = dt.hour
        minute = dt.minute

        if hour == 0:
            hour_12 = 12
            period = 'ص'
        elif hour < 12:
            hour_12 = hour
            period = 'ص'
        elif hour == 12:
            hour_12 = 12
            period = 'م'
        else:
            hour_12 = hour - 12
            period = 'م'

        return f"{dt.strftime('%Y-%m-%d')} {hour_12}:{minute:02d} {period}"

    except Exception:
        # Fallback to simple formatting
        return dt.strftime('%Y-%m-%d %H:%M') if dt else 'غير محدد'

# Helper functions for backup
def get_user_warehouse_relations():
    """استخراج علاقات المستخدمين بالمستودعات"""
    try:
        result = db.session.execute(text("SELECT user_id, warehouse_id FROM user_warehouse"))
        return [{'user_id': row[0], 'warehouse_id': row[1]} for row in result]
    except Exception as e:
        current_app.logger.error(f'Error getting user_warehouse relations: {str(e)}')
        return []

def get_personnel_weapon_relations():
    """استخراج علاقات الأفراد بالأسلحة"""
    try:
        result = db.session.execute(text("SELECT personnel_id, weapon_id, is_primary FROM personnel_weapon"))
        return [{'personnel_id': row[0], 'weapon_id': row[1], 'is_primary': row[2]} for row in result]
    except Exception as e:
        current_app.logger.error(f'Error getting personnel_weapon relations: {str(e)}')
        return []

def get_external_sqlite_data():
    """استخراج البيانات من قواعد البيانات الخارجية SQLite"""
    external_data = {}

    # بيانات المواقع من PostgreSQL
    try:
        from models import Location, LocationEquipment

        # جدول المواقع
        locations = Location.query.all()
        external_data['locations'] = [
            {
                'id': loc.id,
                'name': loc.name,
                'serial_number': loc.serial_number,
                'type': loc.type,
                'status': loc.status,
                'coordinates': loc.coordinates,
                'description': loc.description,
                'created_at': loc.created_at.isoformat() if loc.created_at else None,
                'updated_at': loc.updated_at.isoformat() if loc.updated_at else None,
                'created_by': loc.created_by
            } for loc in locations
        ]

        # جدول عهد المواقع
        location_equipment = LocationEquipment.query.all()
        external_data['location_equipment'] = [
            {
                'id': eq.id,
                'location_id': eq.location_id,
                'equipment_name': eq.equipment_name,
                'quantity': eq.quantity,
                'condition_status': eq.condition_status,
                'notes': eq.notes,
                'created_at': eq.created_at.isoformat() if eq.created_at else None
            } for eq in location_equipment
        ]
    except Exception as e:
        current_app.logger.error(f'Error getting locations data: {str(e)}')
        external_data['locations'] = []
        external_data['location_equipment'] = []

    # بيانات الكشوفات والدوريات من PostgreSQL
    try:
        # جدول بيانات الكشوفات
        try:
            receipt_data = ReceiptData.query.all()
            external_data['receipt_data'] = [
                {
                    'id': rd.id,
                    'user_id': rd.user_id,
                    'receipt_data': rd.receipt_data,
                    'created_at': rd.created_at.isoformat() if rd.created_at else None,
                    'updated_at': rd.updated_at.isoformat() if rd.updated_at else None
                } for rd in receipt_data
            ]
        except:
            external_data['receipt_data'] = []

        # جدول مواقع الدوريات
        try:
            patrol_locations = PatrolLocations.query.all()
            external_data['patrol_locations'] = [
                {
                    'id': pl.id,
                    'row_index': pl.row_index,
                    'location_id': pl.location_id,
                    'timestamp': pl.timestamp,
                    'created_by': pl.created_by,
                    'created_at': pl.created_at.isoformat() if pl.created_at else None
                } for pl in patrol_locations
            ]
        except:
            external_data['patrol_locations'] = []

        # جدول مواقع المناوبين
        try:
            shifts_locations = ShiftsLocations.query.all()
            external_data['shifts_locations'] = [
                {
                    'id': sl.id,
                    'row_index': sl.row_index,
                    'location_id': sl.location_id,
                    'timestamp': sl.timestamp,
                    'created_by': sl.created_by,
                    'created_at': sl.created_at.isoformat() if sl.created_at else None
                } for sl in shifts_locations
            ]
        except:
            external_data['shifts_locations'] = []

        # جدول مواقع الكشوفات
        try:
            receipt_locations = ReceiptLocations.query.all()
            external_data['receipt_locations'] = [
                {
                    'id': rl.id,
                    'row_index': rl.row_index,
                    'location_id': rl.location_id,
                    'timestamp': rl.timestamp,
                    'created_by': rl.created_by,
                    'created_at': rl.created_at.isoformat() if rl.created_at else None
                } for rl in receipt_locations
            ]
        except:
            external_data['receipt_locations'] = []

    except Exception as e:
        current_app.logger.error(f'Error getting receipts data: {str(e)}')
        external_data['receipt_data'] = []
        external_data['patrol_locations'] = []
        external_data['shifts_locations'] = []
        external_data['receipt_locations'] = []

    return external_data

def restore_external_sqlite_data(data):
    """استعادة البيانات الخارجية إلى قاعدة البيانات PostgreSQL"""

    # استعادة بيانات المواقع إلى PostgreSQL
    try:
        from models import Location, LocationEquipment

        # حذف البيانات الحالية
        LocationEquipment.query.delete()
        Location.query.delete()
        db.session.commit()

        # استخدام المستخدم الحالي الذي قام بعملية الاستعادة
        current_user_id = data.get('current_user_id')
        
        # التحقق من وجود مستخدم نشط في النظام كاحتياط
        active_user_id = None
        if not current_user_id:
            try:
                from flask import current_app
                with current_app.app_context():
                    from models import User
                    active_user = User.query.first()
                    if active_user:
                        active_user_id = active_user.id
            except:
                # في حالة فشل الحصول على مستخدم، استخدم قيمة افتراضية
                active_user_id = 1
        else:
            active_user_id = current_user_id

        # استعادة المواقع
        for location_data in data.get('locations', []):
            # التعامل مع معرف المستخدم (created_by)
            created_by = location_data.get('created_by')

            # إذا كان created_by غير موجود أو None، استخدم المستخدم النشط
            if created_by is None or created_by == "":
                created_by = active_user_id

            # تحويل التواريخ من string إلى datetime إذا لزم الأمر
            created_at = location_data.get('created_at')
            updated_at = location_data.get('updated_at')

            if isinstance(created_at, str):
                try:
                    from datetime import datetime
                    created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                except:
                    created_at = None

            if isinstance(updated_at, str):
                try:
                    from datetime import datetime
                    updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                except:
                    updated_at = None

            location = Location(
                name=location_data.get('name'),
                serial_number=location_data.get('serial_number'),
                type=location_data.get('type', 'أمني'),
                status=location_data.get('status', 'نشط'),
                coordinates=location_data.get('coordinates'),
                description=location_data.get('description'),
                created_by=created_by,
                created_at=created_at,
                updated_at=updated_at
            )
            db.session.add(location)

        # حفظ المواقع أولاً
        db.session.commit()

        # استعادة عهد المواقع
        for equipment_data in data.get('location_equipment', []):
            # تحويل التواريخ من string إلى datetime إذا لزم الأمر
            created_at = equipment_data.get('created_at')

            if isinstance(created_at, str):
                try:
                    from datetime import datetime
                    created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                except:
                    created_at = None

            equipment = LocationEquipment(
                location_id=equipment_data.get('location_id'),
                equipment_name=equipment_data.get('equipment_name'),
                quantity=equipment_data.get('quantity', 1),
                condition_status=equipment_data.get('condition_status', 'جيد'),
                notes=equipment_data.get('notes'),
                created_at=created_at
            )
            db.session.add(equipment)

        db.session.commit()

    except Exception as e:
        try:
            from flask import current_app
            current_app.logger.error(f'Error restoring locations data: {str(e)}')
        except:
            print(f'Error restoring locations data: {str(e)}')
            
    # استعادة بيانات الكشوفات
    try:
        import sqlite3
        main_db_path = 'military_warehouse.db'

        conn = sqlite3.connect(main_db_path)
        cursor = conn.cursor()

        # استعادة بيانات الكشوفات
        try:
            cursor.execute("DELETE FROM receipt_data")
            for receipt in data.get('receipt_data', []):
                cursor.execute('''
                    INSERT INTO receipt_data (id, user_id, receipt_data, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    receipt.get('id'),
                    receipt.get('user_id'),
                    receipt.get('receipt_data'),
                    receipt.get('created_at'),
                    receipt.get('updated_at')
                ))
        except Exception as e:
            current_app.logger.error(f'Error restoring receipt_data: {str(e)}')

        # استعادة مواقع الدوريات
        try:
            cursor.execute("DELETE FROM patrol_locations")
            for patrol in data.get('patrol_locations', []):
                cursor.execute('''
                    INSERT INTO patrol_locations (id, row_index, location_id, timestamp, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    patrol.get('id'),
                    patrol.get('row_index'),
                    patrol.get('location_id'),
                    patrol.get('timestamp'),
                    patrol.get('created_by', 1),
                    patrol.get('created_at')
                ))
        except Exception as e:
            current_app.logger.error(f'Error restoring patrol_locations: {str(e)}')

        # استعادة مواقع المناوبين
        try:
            cursor.execute("DELETE FROM shifts_locations")
            for shift in data.get('shifts_locations', []):
                cursor.execute('''
                    INSERT INTO shifts_locations (id, row_index, location_id, timestamp, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    shift.get('id'),
                    shift.get('row_index'),
                    shift.get('location_id'),
                    shift.get('timestamp'),
                    shift.get('created_by', 1),
                    shift.get('created_at')
                ))
        except Exception as e:
            current_app.logger.error(f'Error restoring shifts_locations: {str(e)}')

        conn.commit()
        conn.close()

    except Exception as e:
        current_app.logger.error(f'Error restoring receipts data: {str(e)}')

# Backup and restore functions
def backup_database(warehouse_id=None, user_id=None):
    """
    Create a backup of the database, optionally filtered by warehouse.

    Args:
        warehouse_id: Optional warehouse ID to backup only data for that warehouse
        user_id: User ID who initiated the backup

    Returns:
        tuple: (filename, file_path) of the created backup
    """
    timestamp = get_saudi_now().strftime('%Y%m%d_%H%M%S')

    if warehouse_id:
        warehouse = Warehouse.query.get(warehouse_id)
        if not warehouse:
            return None, "Warehouse not found"

        backup_type = f"warehouse_{warehouse_id}"
        filename = f"backup_{backup_type}_{timestamp}.json"
    else:
        backup_type = "full"
        filename = f"backup_full_{timestamp}.json"

    backup_dir = current_app.config['BACKUP_DIR']
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)

    file_path = os.path.join(backup_dir, filename)

    # Create data dictionary to store all entities
    data = {
        'metadata': {
            'timestamp': get_saudi_now().isoformat(),
            'backup_type': backup_type,
            'version': '1.0'
        }
    }

    # Export warehouses
    if warehouse_id:
        warehouses = [Warehouse.query.get(warehouse_id)]

        # Filter all queries by the specific warehouse
        weapons = Weapon.query.filter_by(warehouse_id=warehouse_id).all()
        personnel = Personnel.query.filter_by(warehouse_id=warehouse_id).all()
        devices = Device.query.filter_by(warehouse_id=warehouse_id).all()
        audits = Audit.query.filter_by(warehouse_id=warehouse_id).all()
        activity_logs = ActivityLog.query.filter_by(warehouse_id=warehouse_id).all()

        # Get related transactions
        weapon_ids = [w.id for w in weapons]
        transactions = WeaponTransaction.query.filter(
            (WeaponTransaction.source_warehouse_id == warehouse_id) |
            (WeaponTransaction.target_warehouse_id == warehouse_id)
        ).all()

        # Get related maintenance records
        maintenance_records = MaintenanceRecord.query.filter(
            MaintenanceRecord.weapon_id.in_(weapon_ids)
        ).all()

        # Get device maintenance records
        device_ids = [d.id for d in devices]
        device_maintenance_records = DeviceMaintenanceRecord.query.filter(
            DeviceMaintenanceRecord.device_id.in_(device_ids)
        ).all()

        # Get audit items
        audit_ids = [a.id for a in audits]
        audit_items = AuditItem.query.filter(
            AuditItem.audit_id.in_(audit_ids)
        ).all()
    else:
        # Export all data
        warehouses = Warehouse.query.all()
        weapons = Weapon.query.all()
        personnel = Personnel.query.all()
        devices = Device.query.all()
        transactions = WeaponTransaction.query.all()
        maintenance_records = MaintenanceRecord.query.all()
        device_maintenance_records = DeviceMaintenanceRecord.query.all()
        audits = Audit.query.all()
        audit_items = AuditItem.query.all()
        activity_logs = ActivityLog.query.all()

    # إضافة البيانات المفقودة (فقط في النسخة الكاملة)
    if not warehouse_id:
        # استيراد النماذج المطلوبة
        from models import User, InventoryItem, InventoryTransaction, BackupRecord, BackupSchedule, WeeklyReport

        users = User.query.all()
        inventory_items = InventoryItem.query.all()
        inventory_transactions = InventoryTransaction.query.all()
        backup_records = BackupRecord.query.all()
        backup_schedules = BackupSchedule.query.all()
        weekly_reports = WeeklyReport.query.all()
    else:
        # تعيين قيم فارغة للنسخة الجزئية
        users = []
        inventory_items = []
        inventory_transactions = []
        backup_records = []
        backup_schedules = []
        weekly_reports = []

    # Convert all entities to dictionaries
    data['warehouses'] = [serialize_model(w) for w in warehouses]
    data['weapons'] = [serialize_model(w) for w in weapons]
    data['personnel'] = [serialize_model(p) for p in personnel]
    data['devices'] = [serialize_model(d) for d in devices]
    data['transactions'] = [serialize_model(t) for t in transactions]
    data['maintenance_records'] = [serialize_model(m) for m in maintenance_records]
    data['device_maintenance_records'] = [serialize_model(d) for d in device_maintenance_records]
    data['audits'] = [serialize_model(a) for a in audits]
    data['audit_items'] = [serialize_model(a) for a in audit_items]
    data['activity_logs'] = [serialize_model(a) for a in activity_logs]

    # إضافة البيانات الجديدة للنسخة الاحتياطية
    if not warehouse_id:  # فقط في النسخة الكاملة
        data['users'] = [serialize_model(u) for u in users]
        data['inventory_items'] = [serialize_model(i) for i in inventory_items]
        data['inventory_transactions'] = [serialize_model(t) for t in inventory_transactions]
        data['backup_records'] = [serialize_model(b) for b in backup_records]
        data['backup_schedules'] = [serialize_model(s) for s in backup_schedules]
        data['weekly_reports'] = [serialize_model(w) for w in weekly_reports]

        # إضافة جداول العلاقات
        data['user_warehouse_relations'] = get_user_warehouse_relations()
        data['personnel_weapon_relations'] = get_personnel_weapon_relations()

        # إضافة البيانات الخارجية
        external_data = get_external_sqlite_data()
        data.update(external_data)

    # Save to file
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        return None, str(e)

    # Record the backup
    try:
        file_size = os.path.getsize(file_path)

        # التحقق من وجود المستخدم
        from models import User
        if user_id is None:
            # استخدام أول مستخدم موجود في قاعدة البيانات
            user = User.query.first()
            if user:
                user_id = user.id
            else:
                return None, "لا يوجد مستخدمين في قاعدة البيانات"
        else:
            # التحقق من وجود المستخدم المحدد
            user = User.query.get(user_id)
            if not user:
                return None, f"المستخدم برقم {user_id} غير موجود"

        backup_record = BackupRecord(
            filename=filename,
            backup_type=backup_type,
            file_size=file_size,
            user_id=user_id,
            warehouse_id=warehouse_id if warehouse_id else None
        )
        db.session.add(backup_record)
        db.session.commit()

        return filename, file_path
    except Exception as e:
        db.session.rollback()
        return None, str(e)

def update_backup_records():
    """
    Update backup records in the database by scanning the backup directory.
    This function will:
    1. Scan the backup directory for backup files
    2. Add new backup files to the database
    3. Remove records for missing backup files
    """
    try:
        # Get the backup directory from config
        backup_dir = current_app.config['BACKUP_DIR']
        if not os.path.exists(backup_dir):
            return

        # Get all backup files in the directory
        existing_files = set()
        for filename in os.listdir(backup_dir):
            if filename.startswith('backup_') and filename.endswith('.json'):
                file_path = os.path.join(backup_dir, filename)
                existing_files.add(filename)

                # Check if this backup is already in the database
                backup_record = BackupRecord.query.filter_by(filename=filename).first()
                if not backup_record:
                    # Parse backup type and warehouse_id from filename
                    if 'warehouse_' in filename:
                        try:
                            warehouse_id = int(filename.split('warehouse_')[1].split('_')[0])
                            backup_type = f'warehouse_{warehouse_id}'
                        except:
                            warehouse_id = None
                            backup_type = 'full'
                    else:
                        warehouse_id = None
                        backup_type = 'full'

                    # Get file size
                    file_size = os.path.getsize(file_path)

                    # Get first admin user for the record
                    from models import User
                    user = User.query.filter_by(is_admin=True).first()
                    if not user:
                        user = User.query.first()

                    if user:
                        # Create new backup record
                        backup_record = BackupRecord(
                            filename=filename,
                            backup_type=backup_type,
                            file_size=file_size,
                            user_id=user.id,
                            warehouse_id=warehouse_id
                        )
                        db.session.add(backup_record)

        # Remove records for missing files
        for record in BackupRecord.query.all():
            if record.filename not in existing_files:
                db.session.delete(record)

        db.session.commit()
        return True

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Error updating backup records: {str(e)}')
        return False

def restore_database(filepath, user_id):
    """
    Restore database from a backup file.

    Args:
        filepath: Path to the backup file
        user_id: User ID who initiated the restore

    Returns:
        bool: True if successful, False otherwise
    """
    if not os.path.exists(filepath):
        return False, "ملف النسخة الاحتياطية غير موجود"

    try:
        # التأكد من وجود المستخدم
        from models import User
        user = User.query.get(user_id)
        if not user:
            return False, f"المستخدم برقم {user_id} غير موجود"

        # قراءة ملف النسخة الاحتياطية
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # إضافة user_id إلى البيانات للاستخدام في استعادة البيانات الخارجية
        data['current_user_id'] = user_id

        # التحقق من نوع النسخة الاحتياطية
        backup_type = data['metadata'].get('backup_type', 'unknown')

        # حفظ نسخة احتياطية قبل الاستعادة للأمان
        safety_backup_filename, _ = backup_database(user_id=user_id)

        # حذف البيانات الحالية حسب نوع النسخة الاحتياطية
        if backup_type.startswith('warehouse_'):
            # استعادة نسخة احتياطية لمستودع محدد
            warehouse_id = int(backup_type.split('_')[1])

            # حذف البيانات المرتبطة بالمستودع المحدد
            try:
                # حذف العناصر المرتبطة أولاً (العلاقات)
                AuditItem.query.filter(
                    AuditItem.audit_id.in_(
                        db.session.query(Audit.id).filter_by(warehouse_id=warehouse_id)
                    )
                ).delete(synchronize_session=False)

                MaintenanceRecord.query.filter(
                    MaintenanceRecord.weapon_id.in_(
                        db.session.query(Weapon.id).filter_by(warehouse_id=warehouse_id)
                    )
                ).delete(synchronize_session=False)

                DeviceMaintenanceRecord.query.filter(
                    DeviceMaintenanceRecord.device_id.in_(
                        db.session.query(Device.id).filter_by(warehouse_id=warehouse_id)
                    )
                ).delete(synchronize_session=False)

                # حذف المعاملات المرتبطة بالمستودع
                WeaponTransaction.query.filter(
                    (WeaponTransaction.source_warehouse_id == warehouse_id) |
                    (WeaponTransaction.target_warehouse_id == warehouse_id)
                ).delete(synchronize_session=False)

                # حذف علاقات الأفراد بالأسلحة
                db.session.execute(text("""
                    DELETE FROM personnel_weapon
                    WHERE personnel_id IN (
                        SELECT id FROM personnel WHERE warehouse_id = :warehouse_id
                    )
                """), {"warehouse_id": warehouse_id})

                # حذف الكيانات الرئيسية
                ActivityLog.query.filter_by(warehouse_id=warehouse_id).delete(synchronize_session=False)
                Audit.query.filter_by(warehouse_id=warehouse_id).delete(synchronize_session=False)
                Device.query.filter_by(warehouse_id=warehouse_id).delete(synchronize_session=False)
                Personnel.query.filter_by(warehouse_id=warehouse_id).delete(synchronize_session=False)
                Weapon.query.filter_by(warehouse_id=warehouse_id).delete(synchronize_session=False)

                db.session.commit()
            except Exception as e:
                db.session.rollback()
                return False, f"فشل في حذف البيانات الحالية: {str(e)}"

        elif backup_type == 'full':
            # استعادة نسخة احتياطية كاملة
            try:
                # حذف البيانات من الجداول بالترتيب الصحيح لتجنب مشاكل المفاتيح الأجنبية
                tables_to_clear = [
                    'audit_items',
                    'maintenance_records',
                    'device_maintenance_records',
                    'weapon_transactions',
                    'personnel_weapon',
                    'activity_logs',
                    'audits',
                    'devices',
                    'weapons',
                    'personnel'
                ]

                for table in tables_to_clear:
                    db.session.execute(text(f"DELETE FROM {table}"))
                    db.session.commit()
            except Exception as e:
                db.session.rollback()
                return False, f"فشل في حذف البيانات الحالية: {str(e)}"

        # استعادة البيانات من ملف النسخة الاحتياطية
        try:
            # استعادة المستودعات (إذا كانت غير موجودة)
            for warehouse_data in data.get('warehouses', []):
                warehouse = Warehouse.query.filter_by(id=warehouse_data['id']).first()
                if not warehouse:
                    warehouse = Warehouse(
                        id=warehouse_data['id'],
                        name=warehouse_data['name'],
                        description=warehouse_data.get('description'),
                        location=warehouse_data.get('location'),
                        created_at=datetime.fromisoformat(warehouse_data.get('created_at')),
                        updated_at=datetime.fromisoformat(warehouse_data.get('updated_at'))
                    )
                    db.session.add(warehouse)
                # تحديث المستودع الموجود بالبيانات من النسخة الاحتياطية
                else:
                    warehouse.name = warehouse_data['name']
                    warehouse.description = warehouse_data.get('description')
                    warehouse.location = warehouse_data.get('location')
            db.session.commit()

            # استعادة الأسلحة
            for weapon_data in data.get('weapons', []):
                # التحقق من عدم وجود السلاح بنفس المعرف
                existing_weapon = Weapon.query.get(weapon_data['id'])
                if existing_weapon:
                    continue

                weapon = Weapon(
                    id=weapon_data['id'],
                    serial_number=weapon_data['serial_number'],
                    name=weapon_data['name'],
                    type=weapon_data['type'],
                    status=weapon_data['status'],
                    condition=weapon_data.get('condition'),
                    notes=weapon_data.get('notes'),
                    barcode=weapon_data.get('barcode'),
                    qr_code=weapon_data.get('qr_code'),
                    created_at=datetime.fromisoformat(weapon_data.get('created_at')),
                    updated_at=datetime.fromisoformat(weapon_data.get('updated_at')),
                    warehouse_id=weapon_data['warehouse_id']
                )
                db.session.add(weapon)
            db.session.commit()

            # استعادة الأفراد
            for personnel_data in data.get('personnel', []):
                # التحقق من عدم وجود الفرد بنفس المعرف
                existing_personnel = Personnel.query.get(personnel_data['id'])
                if existing_personnel:
                    continue

                personnel = Personnel(
                    id=personnel_data['id'],
                    personnel_id=personnel_data['personnel_id'],
                    name=personnel_data['name'],
                    rank=personnel_data.get('rank'),
                    status=personnel_data['status'],
                    phone=personnel_data.get('phone'),
                    notes=personnel_data.get('notes'),
                    created_at=datetime.fromisoformat(personnel_data.get('created_at')),
                    updated_at=datetime.fromisoformat(personnel_data.get('updated_at')),
                    warehouse_id=personnel_data['warehouse_id']
                )
                db.session.add(personnel)
            db.session.commit()

            # استعادة علاقات الأفراد بالأسلحة
            for personnel_data in data.get('personnel', []):
                if 'weapon_ids' in personnel_data and personnel_data['weapon_ids']:
                    personnel = Personnel.query.get(personnel_data['id'])
                    for weapon_id in personnel_data['weapon_ids']:
                        weapon = Weapon.query.get(weapon_id)
                        if personnel and weapon:
                            personnel.weapons.append(weapon)
            db.session.commit()

            # استعادة الأجهزة
            for device_data in data.get('devices', []):
                # التحقق من عدم وجود الجهاز بنفس المعرف
                existing_device = Device.query.get(device_data['id'])
                if existing_device:
                    continue

                device = Device(
                    id=device_data['id'],
                    name=device_data['name'],
                    type=device_data['type'],
                    model=device_data.get('model'),
                    serial_number=device_data.get('serial_number'),
                    status=device_data['status'],
                    notes=device_data.get('notes'),
                    created_at=datetime.fromisoformat(device_data.get('created_at')),
                    updated_at=datetime.fromisoformat(device_data.get('updated_at')),
                    warehouse_id=device_data['warehouse_id']
                )
                db.session.add(device)
            db.session.commit()

            # استعادة المعاملات
            for transaction_data in data.get('transactions', []):
                # التحقق من عدم وجود المعاملة بنفس المعرف
                existing_transaction = WeaponTransaction.query.get(transaction_data['id'])
                if existing_transaction:
                    continue

                transaction = WeaponTransaction(
                    id=transaction_data['id'],
                    transaction_type=transaction_data['transaction_type'],
                    timestamp=datetime.fromisoformat(transaction_data.get('timestamp')),
                    notes=transaction_data.get('notes'),
                    weapon_id=transaction_data['weapon_id'],
                    personnel_id=transaction_data['personnel_id'],
                    source_warehouse_id=transaction_data['source_warehouse_id'],
                    target_warehouse_id=transaction_data.get('target_warehouse_id'),
                    user_id=transaction_data['user_id']
                )
                db.session.add(transaction)
            db.session.commit()

            # استعادة سجلات الصيانة
            for record_data in data.get('maintenance_records', []):
                # التحقق من عدم وجود سجل الصيانة بنفس المعرف
                existing_record = MaintenanceRecord.query.get(record_data['id'])
                if existing_record:
                    continue

                record = MaintenanceRecord(
                    id=record_data['id'],
                    maintenance_type=record_data['maintenance_type'],
                    description=record_data['description'],
                    start_date=datetime.fromisoformat(record_data.get('start_date')),
                    end_date=datetime.fromisoformat(record_data.get('end_date')) if record_data.get('end_date') else None,
                    status=record_data['status'],
                    cost=record_data.get('cost'),
                    notes=record_data.get('notes'),
                    created_at=datetime.fromisoformat(record_data.get('created_at')),
                    updated_at=datetime.fromisoformat(record_data.get('updated_at')),
                    weapon_id=record_data['weapon_id'],
                    user_id=record_data['user_id']
                )
                db.session.add(record)
            db.session.commit()

            # استعادة سجلات صيانة الأجهزة
            for record_data in data.get('device_maintenance_records', []):
                # التحقق من عدم وجود سجل صيانة الجهاز بنفس المعرف
                existing_record = DeviceMaintenanceRecord.query.get(record_data['id'])
                if existing_record:
                    continue

                record = DeviceMaintenanceRecord(
                    id=record_data['id'],
                    maintenance_type=record_data['maintenance_type'],
                    description=record_data['description'],
                    start_date=datetime.fromisoformat(record_data.get('start_date')),
                    end_date=datetime.fromisoformat(record_data.get('end_date')) if record_data.get('end_date') else None,
                    status=record_data['status'],
                    cost=record_data.get('cost'),
                    notes=record_data.get('notes'),
                    created_at=datetime.fromisoformat(record_data.get('created_at')),
                    updated_at=datetime.fromisoformat(record_data.get('updated_at')),
                    device_id=record_data['device_id'],
                    user_id=record_data['user_id']
                )
                db.session.add(record)
            db.session.commit()

            # استعادة عمليات التدقيق
            for audit_data in data.get('audits', []):
                # التحقق من عدم وجود عملية التدقيق بنفس المعرف
                existing_audit = Audit.query.get(audit_data['id'])
                if existing_audit:
                    continue

                audit = Audit(
                    id=audit_data['id'],
                    audit_date=datetime.fromisoformat(audit_data.get('audit_date')),
                    status=audit_data['status'],
                    description=audit_data.get('description'),
                    notes=audit_data.get('notes'),
                    created_at=datetime.fromisoformat(audit_data.get('created_at')),
                    warehouse_id=audit_data['warehouse_id'],
                    user_id=audit_data['user_id']
                )
                db.session.add(audit)
            db.session.commit()

            # استعادة عناصر التدقيق
            for item_data in data.get('audit_items', []):
                # التحقق من عدم وجود عنصر التدقيق بنفس المعرف
                existing_item = AuditItem.query.get(item_data['id'])
                if existing_item:
                    continue

                item = AuditItem(
                    id=item_data['id'],
                    status=item_data['status'],
                    condition=item_data.get('condition'),
                    notes=item_data.get('notes'),
                    audit_id=item_data['audit_id'],
                    weapon_id=item_data['weapon_id']
                )
                db.session.add(item)
            db.session.commit()

            # استعادة سجلات النشاط
            for log_data in data.get('activity_logs', []):
                # التحقق من عدم وجود سجل النشاط بنفس المعرف
                existing_log = ActivityLog.query.get(log_data['id'])
                if existing_log:
                    continue

                log = ActivityLog(
                    id=log_data['id'],
                    action=log_data['action'],
                    description=log_data['description'],
                    timestamp=datetime.fromisoformat(log_data.get('timestamp')),
                    ip_address=log_data.get('ip_address'),
                    user_id=log_data['user_id'],
                    warehouse_id=log_data['warehouse_id']
                )
                db.session.add(log)
            db.session.commit()

            # استعادة البيانات الإضافية (فقط في النسخة الكاملة)
            if backup_type == 'full':
                # استعادة المستخدمين
                for user_data in data.get('users', []):
                    existing_user = User.query.get(user_data['id'])
                    if existing_user:
                        continue

                    user = User(
                        id=user_data['id'],
                        username=user_data['username'],
                        password_hash=user_data['password_hash'],
                        full_name=user_data.get('full_name'),
                        email=user_data.get('email'),
                        is_admin=user_data.get('is_admin', False),
                        user_role=user_data.get('user_role'),
                        role_en=user_data.get('role_en'),
                        is_active=user_data.get('is_active', True),
                        created_at=datetime.fromisoformat(user_data.get('created_at')),
                        last_login=datetime.fromisoformat(user_data.get('last_login')) if user_data.get('last_login') else None,
                        updated_at=datetime.fromisoformat(user_data.get('updated_at'))
                    )
                    db.session.add(user)
                db.session.commit()

                # استعادة عناصر المخزون
                for item_data in data.get('inventory_items', []):
                    existing_item = InventoryItem.query.get(item_data['id'])
                    if existing_item:
                        continue

                    item = InventoryItem(
                        id=item_data['id'],
                        item_code=item_data['item_code'],
                        name=item_data['name'],
                        category=item_data['category'],
                        subcategory=item_data.get('subcategory'),
                        brand=item_data.get('brand'),
                        model=item_data.get('model'),
                        size=item_data.get('size'),
                        color=item_data.get('color'),
                        material=item_data.get('material'),
                        quantity_in_stock=item_data.get('quantity_in_stock', 0),
                        quantity_issued=item_data.get('quantity_issued', 0),
                        minimum_stock=item_data.get('minimum_stock', 0),
                        maximum_stock=item_data.get('maximum_stock', 0),
                        unit_cost=item_data.get('unit_cost'),
                        total_value=item_data.get('total_value'),
                        manufacture_date=datetime.fromisoformat(item_data.get('manufacture_date')) if item_data.get('manufacture_date') else None,
                        expiry_date=datetime.fromisoformat(item_data.get('expiry_date')) if item_data.get('expiry_date') else None,
                        purchase_date=datetime.fromisoformat(item_data.get('purchase_date')) if item_data.get('purchase_date') else None,
                        status=item_data.get('status', 'متوفر'),
                        location=item_data.get('location'),
                        shelf_number=item_data.get('shelf_number'),
                        supplier=item_data.get('supplier'),
                        batch_number=item_data.get('batch_number'),
                        serial_numbers=item_data.get('serial_numbers'),
                        specifications=item_data.get('specifications'),
                        notes=item_data.get('notes'),
                        created_at=datetime.fromisoformat(item_data.get('created_at')),
                        updated_at=datetime.fromisoformat(item_data.get('updated_at')),
                        warehouse_id=item_data['warehouse_id']
                    )
                    db.session.add(item)
                db.session.commit()

                # استعادة معاملات المخزون
                for trans_data in data.get('inventory_transactions', []):
                    existing_trans = InventoryTransaction.query.get(trans_data['id'])
                    if existing_trans:
                        continue

                    trans = InventoryTransaction(
                        id=trans_data['id'],
                        transaction_type=trans_data['transaction_type'],
                        quantity=trans_data['quantity'],
                        unit_cost=trans_data.get('unit_cost'),
                        total_cost=trans_data.get('total_cost'),
                        reference_number=trans_data.get('reference_number'),
                        recipient_name=trans_data.get('recipient_name'),
                        recipient_id=trans_data.get('recipient_id'),
                        recipient_rank=trans_data.get('recipient_rank'),
                        recipient_unit=trans_data.get('recipient_unit'),
                        from_warehouse_id=trans_data.get('from_warehouse_id'),
                        to_warehouse_id=trans_data.get('to_warehouse_id'),
                        reason=trans_data.get('reason'),
                        notes=trans_data.get('notes'),
                        approved_by=trans_data.get('approved_by'),
                        transaction_date=datetime.fromisoformat(trans_data.get('transaction_date')),
                        created_at=datetime.fromisoformat(trans_data.get('created_at')),
                        updated_at=datetime.fromisoformat(trans_data.get('updated_at')),
                        item_id=trans_data['item_id'],
                        user_id=trans_data['user_id']
                    )
                    db.session.add(trans)
                db.session.commit()

                # استعادة التقارير الأسبوعية
                for report_data in data.get('weekly_reports', []):
                    existing_report = WeeklyReport.query.get(report_data['id'])
                    if existing_report:
                        continue

                    report = WeeklyReport(
                        id=report_data['id'],
                        week_start=datetime.fromisoformat(report_data.get('week_start')).date(),
                        week_end=datetime.fromisoformat(report_data.get('week_end')).date(),
                        personnel_name=report_data['personnel_name'],
                        personnel_id=report_data['personnel_id'],
                        national_id=report_data['national_id'],
                        rank=report_data['rank'],
                        old_status=report_data['old_status'],
                        new_status=report_data['new_status'],
                        current_status=report_data['current_status'],
                        change_date=datetime.fromisoformat(report_data.get('change_date')),
                        change_time=report_data['change_time'],
                        changed_by=report_data['changed_by'],
                        change_method=report_data['change_method'],
                        weapons=report_data.get('weapons'),
                        warehouse_name=report_data['warehouse_name'],
                        created_at=datetime.fromisoformat(report_data.get('created_at')),
                        warehouse_id=report_data['warehouse_id'],
                        user_id=report_data.get('user_id')
                    )
                    db.session.add(report)
                db.session.commit()

                # استعادة علاقات المستخدمين بالمستودعات
                for relation in data.get('user_warehouse_relations', []):
                    try:
                        db.session.execute(text(
                            "INSERT OR IGNORE INTO user_warehouse (user_id, warehouse_id) VALUES (:user_id, :warehouse_id)"
                        ), relation)
                    except:
                        pass
                db.session.commit()

                # استعادة علاقات الأفراد بالأسلحة
                for relation in data.get('personnel_weapon_relations', []):
                    try:
                        db.session.execute(text(
                            "INSERT OR IGNORE INTO personnel_weapon (personnel_id, weapon_id, is_primary) VALUES (:personnel_id, :weapon_id, :is_primary)"
                        ), relation)
                    except:
                        pass
                db.session.commit()

                # استعادة البيانات الخارجية
                restore_external_sqlite_data(data)

            # تسجيل عملية الاستعادة
            log = ActivityLog(
                action="استعادة النسخة الاحتياطية",
                description=f"تم استعادة النسخة الاحتياطية {os.path.basename(filepath)}",
                ip_address="127.0.0.1",
                user_id=user_id,
                warehouse_id=1  # المستودع الافتراضي
            )
            db.session.add(log)
            db.session.commit()

            return True, "تمت استعادة النسخة الاحتياطية بنجاح"

        except Exception as e:
            db.session.rollback()
            return False, f"فشل في استعادة البيانات: {str(e)}"

    except Exception as e:
        db.session.rollback()
        return False, f"فشل استعادة النسخة الاحتياطية: {str(e)}"

def serialize_model(instance):
    """
    Convert SQLAlchemy model instance to a dictionary for JSON serialization.

    Args:
        instance: SQLAlchemy model instance

    Returns:
        dict: Dictionary representation of the model
    """
    from datetime import date, datetime
    from decimal import Decimal

    data = {}
    for column in instance.__table__.columns:
        value = getattr(instance, column.name)

        # Handle datetime objects
        if isinstance(value, datetime):
            value = value.isoformat()
        # Handle date objects
        elif isinstance(value, date):
            value = value.isoformat()
        # Handle decimal objects
        elif isinstance(value, Decimal):
            value = float(value)

        data[column.name] = value

    # Handle specific relationships based on model type
    if hasattr(instance, '__class__'):
        class_name = instance.__class__.__name__
        if class_name == 'Personnel':
            # Include weapon IDs for personnel
            data['weapon_ids'] = [weapon.id for weapon in instance.weapons]

    return data

# Statistics and reporting functions
def get_warehouse_summary(warehouse_id):
    """Get summary statistics for a warehouse."""
    warehouse = Warehouse.query.get(warehouse_id)
    if not warehouse:
        return None

    # Basic counts
    weapons_count = Weapon.query.filter_by(warehouse_id=warehouse_id).count()
    personnel_count = Personnel.query.filter_by(warehouse_id=warehouse_id).count()

    # Obtener el recuento total de dispositivos (independiente de los almacenes)
    devices_count = Device.query.count()

    # Weapon status breakdown
    weapon_status = {
        'نشط': Weapon.query.filter_by(warehouse_id=warehouse_id, status='نشط').count(),
        'إجازة': Weapon.query.filter_by(warehouse_id=warehouse_id, status='إجازة').count(),
        'مهمة': Weapon.query.filter_by(warehouse_id=warehouse_id, status='مهمة').count(),
        'صيانة': Weapon.query.filter_by(warehouse_id=warehouse_id, status='صيانة').count(),
        'دورة': Weapon.query.filter_by(warehouse_id=warehouse_id, status='دورة').count(),
        'شاغر': Weapon.query.filter_by(warehouse_id=warehouse_id, status='شاغر').count(),
        'مستلم': Weapon.query.filter_by(warehouse_id=warehouse_id, status='مستلم').count(),
        'رماية': Weapon.query.filter_by(warehouse_id=warehouse_id, status='رماية').count(),
        'أخرى': Weapon.query.filter_by(warehouse_id=warehouse_id, status='أخرى').count()
    }

    # Personnel status breakdown
    personnel_status = {
        'نشط': Personnel.query.filter_by(warehouse_id=warehouse_id, status='نشط').count(),
        'إجازة': Personnel.query.filter_by(warehouse_id=warehouse_id, status='إجازة').count(),
        'مهمة': Personnel.query.filter_by(warehouse_id=warehouse_id, status='مهمة').count(),
        'مستلم': Personnel.query.filter_by(warehouse_id=warehouse_id, status='مستلم').count(),
        'رماية': Personnel.query.filter_by(warehouse_id=warehouse_id, status='رماية').count(),
        'دورة': Personnel.query.filter_by(warehouse_id=warehouse_id, status='دورة').count()
    }

    # Weapon types breakdown
    weapon_types = db.session.query(Weapon.type, func.count(Weapon.id))\
        .filter(Weapon.warehouse_id == warehouse_id)\
        .group_by(Weapon.type)\
        .all()
    weapon_types_dict = {t[0]: t[1] for t in weapon_types}

    # Recent activity
    recent_activity = ActivityLog.query.filter_by(warehouse_id=warehouse_id)\
        .order_by(ActivityLog.timestamp.desc())\
        .limit(10).all()

    # Recent transactions
    recent_transactions = WeaponTransaction.query.filter_by(source_warehouse_id=warehouse_id)\
        .order_by(WeaponTransaction.timestamp.desc())\
        .limit(10).all()

    # Device status breakdown (independiente de los almacenes)
    device_status = {
        'سليم': Device.query.filter_by(status='سليم').count(),
        'عطل بسيط': Device.query.filter_by(status='عطل بسيط').count(),
        'عطل جسيم': Device.query.filter_by(status='عطل جسيم').count(),
        'تحت الصيانة': Device.query.filter_by(status='تحت الصيانة').count(),
        'خارج الخدمة': Device.query.filter_by(status='خارج الخدمة').count(),
        'مفقود': Device.query.filter_by(status='مفقود').count()
    }

    return {
        'warehouse': warehouse,
        'weapons_count': weapons_count,
        'personnel_count': personnel_count,
        'devices_count': devices_count,
        'weapon_status': weapon_status,
        'personnel_status': personnel_status,
        'device_status': device_status,
        'weapon_types': weapon_types_dict,
        'recent_activity': recent_activity,
        'recent_transactions': recent_transactions
    }

def get_transaction_stats(warehouse_id, start_date=None, end_date=None):
    """Get transaction statistics for a warehouse within a date range."""
    query = db.session.query(WeaponTransaction).filter(
        (WeaponTransaction.source_warehouse_id == warehouse_id) |
        (WeaponTransaction.target_warehouse_id == warehouse_id)
    )

    if start_date:
        query = query.filter(WeaponTransaction.timestamp >= start_date)

    if end_date:
        query = query.filter(WeaponTransaction.timestamp <= end_date)

    transactions = query.order_by(WeaponTransaction.timestamp).all()

    checkout_count = sum(1 for t in transactions if t.transaction_type == 'checkout')
    return_count = sum(1 for t in transactions if t.transaction_type == 'return')
    transfer_count = sum(1 for t in transactions if t.transaction_type == 'transfer')

    # Calculate daily transaction counts
    if start_date and end_date:
        date_range = (end_date - start_date).days + 1
        daily_counts = {}

        for i in range(date_range):
            day = start_date + timedelta(days=i)
            daily_counts[day.strftime('%Y-%m-%d')] = {
                'checkout': 0,
                'return': 0,
                'transfer': 0,
                'total': 0
            }

        for t in transactions:
            day = t.timestamp.strftime('%Y-%m-%d')
            if day in daily_counts:
                daily_counts[day][t.transaction_type] += 1
                daily_counts[day]['total'] += 1
    else:
        daily_counts = {}

    return {
        'total': len(transactions),
        'checkout': checkout_count,
        'return': return_count,
        'transfer': transfer_count,
        'daily': daily_counts
    }

def get_maintenance_stats(warehouse_id, start_date=None, end_date=None):
    """Get maintenance statistics for a warehouse within a date range."""
    # Get weapon IDs in this warehouse
    weapon_ids = [w.id for w in Weapon.query.filter_by(warehouse_id=warehouse_id).all()]

    query = db.session.query(MaintenanceRecord).filter(
        MaintenanceRecord.weapon_id.in_(weapon_ids)
    )

    if start_date:
        query = query.filter(MaintenanceRecord.start_date >= start_date)

    if end_date:
        query = query.filter(MaintenanceRecord.start_date <= end_date)

    maintenance_records = query.order_by(MaintenanceRecord.start_date).all()

    ongoing_count = sum(1 for r in maintenance_records if r.status == 'ongoing')
    completed_count = sum(1 for r in maintenance_records if r.status == 'completed')
    cancelled_count = sum(1 for r in maintenance_records if r.status == 'cancelled')

    # Calculate total maintenance cost
    total_cost = sum(r.cost or 0 for r in maintenance_records)

    # Get maintenance types breakdown
    maintenance_types = {}
    for record in maintenance_records:
        mtype = record.maintenance_type
        maintenance_types[mtype] = maintenance_types.get(mtype, 0) + 1

    return {
        'total': len(maintenance_records),
        'ongoing': ongoing_count,
        'completed': completed_count,
        'cancelled': cancelled_count,
        'total_cost': total_cost,
        'types': maintenance_types
    }

def get_audit_stats(warehouse_id, start_date=None, end_date=None):
    """Get audit statistics for a warehouse within a date range."""
    query = db.session.query(Audit).filter(Audit.warehouse_id == warehouse_id)

    if start_date:
        query = query.filter(Audit.audit_date >= start_date)

    if end_date:
        query = query.filter(Audit.audit_date <= end_date)

    audits = query.order_by(Audit.audit_date).all()

    in_progress_count = sum(1 for a in audits if a.status == 'in-progress')
    completed_count = sum(1 for a in audits if a.status == 'completed')
    cancelled_count = sum(1 for a in audits if a.status == 'cancelled')

    # Get summary of audit findings
    audit_findings = {
        'found': 0,
        'missing': 0,
        'damaged': 0
    }

    for audit in audits:
        if audit.status == 'completed':
            items = AuditItem.query.filter_by(audit_id=audit.id).all()
            for item in items:
                audit_findings[item.status] = audit_findings.get(item.status, 0) + 1

    return {
        'total': len(audits),
        'in_progress': in_progress_count,
        'completed': completed_count,
        'cancelled': cancelled_count,
        'findings': audit_findings
    }

def export_to_excel(data, filename, report_title=None):
    """
    Export data to Excel file with professional formatting.

    Args:
        data: Dictionary of dataframes to export as sheets
        filename: Output filename
        report_title: Optional title for the report

    Returns:
        BytesIO: Excel file as bytes
    """
    try:
        output = BytesIO()
        writer = pd.ExcelWriter(output, engine='xlsxwriter')
        workbook = writer.book

        # Define enhanced styles with RTL support
        header_format = workbook.add_format({
        'bold': True,
        'font_size': 14,
        'font_color': 'white',
        'bg_color': '#1f4e79',  # Dark blue
        'border': 2,
        'border_color': '#ffffff',
        'align': 'center',
        'valign': 'vcenter',
        'text_wrap': True,
        'reading_order': 2  # RTL reading order
        })

        title_format = workbook.add_format({
        'bold': True,
        'font_size': 20,
        'font_color': '#1f4e79',
        'align': 'center',
        'valign': 'vcenter',
        'bg_color': '#f8f9fa',
        'border': 2,
        'border_color': '#1f4e79',
        'reading_order': 2  # RTL reading order
        })

        subtitle_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'font_color': '#495057',
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#e9ecef',
            'border': 1,
            'border_color': '#6c757d',
            'reading_order': 2  # RTL reading order
        })

        date_format = workbook.add_format({
            'num_format': 'yyyy-mm-dd',
            'align': 'center',
            'border': 1,
            'bg_color': '#f8f9fa',
            'reading_order': 2  # RTL reading order
        })

        cell_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'border_color': '#dee2e6',
            'bg_color': '#ffffff',
            'reading_order': 2  # RTL reading order
        })

        # Alternating row format
        alt_row_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'border_color': '#dee2e6',
            'bg_color': '#f8f9fa',
            'reading_order': 2  # RTL reading order
        })

        # Enhanced status formats with better colors and RTL styling
        status_formats = {
            'نشط': workbook.add_format({
                'bg_color': '#d1e7dd', 'font_color': '#0f5132', 'align': 'center',
                'border': 1, 'bold': True, 'border_color': '#0f5132', 'reading_order': 2
            }),
            'إجازة': workbook.add_format({
                'bg_color': '#fff3cd', 'font_color': '#664d03', 'align': 'center',
                'border': 1, 'bold': True, 'border_color': '#664d03', 'reading_order': 2
            }),
            'مهمة': workbook.add_format({
                'bg_color': '#cfe2ff', 'font_color': '#084298', 'align': 'center',
                'border': 1, 'bold': True, 'border_color': '#084298', 'reading_order': 2
            }),
            'صيانة': workbook.add_format({
                'bg_color': '#f8d7da', 'font_color': '#721c24', 'align': 'center',
                'border': 1, 'bold': True, 'border_color': '#721c24', 'reading_order': 2
            }),
            'دورة': workbook.add_format({
                'bg_color': '#e2e3e5', 'font_color': '#41464b', 'align': 'center',
                'border': 1, 'bold': True, 'border_color': '#41464b', 'reading_order': 2
            }),
            'شاغر': workbook.add_format({
                'bg_color': '#d3d3d4', 'font_color': '#495057', 'align': 'center',
                'border': 1, 'bold': True, 'border_color': '#495057', 'reading_order': 2
            }),
            'مستلم': workbook.add_format({
                'bg_color': '#d1ecf1', 'font_color': '#055160', 'align': 'center',
                'border': 1, 'bold': True, 'border_color': '#055160', 'reading_order': 2
            }),
            'رماية': workbook.add_format({
                'bg_color': '#fff3cd', 'font_color': '#664d03', 'align': 'center',
                'border': 1, 'bold': True, 'border_color': '#664d03', 'reading_order': 2
            }),
            'أخرى': workbook.add_format({
                'bg_color': '#e2e3e5', 'font_color': '#41464b', 'align': 'center',
                'border': 1, 'bold': True, 'border_color': '#41464b', 'reading_order': 2
            })
        }

        # Process each sheet
        for sheet_name, df in data.items():
            # Create worksheet first
            worksheet = workbook.add_worksheet(sheet_name)

            # Set worksheet direction to Right-to-Left (RTL) for Arabic
            worksheet.right_to_left()

            # Get current date and time info with Umm Al-Qura timezone
            try:
                current_date_info = get_hijri_date()
                saudi_time = get_saudi_now()
            except Exception as e:
                # Fallback to basic datetime if hijri conversion fails
                from datetime import datetime
                saudi_time = datetime.now()
                # Calculate approximate hijri date for fallback
                days_since_ref = (saudi_time.date() - datetime(2025, 6, 14).date()).days
                hijri_day = 18 + days_since_ref
                hijri_month = 12
                hijri_year = 1446

                # Adjust for month/year overflow (simple approximation)
                while hijri_day > 30:
                    hijri_day -= 30
                    hijri_month += 1
                    if hijri_month > 12:
                        hijri_month = 1
                        hijri_year += 1
                while hijri_day < 1:
                    hijri_day += 30
                    hijri_month -= 1
                    if hijri_month < 1:
                        hijri_month = 12
                        hijri_year -= 1

                hijri_months = [
                    'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                    'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
                ]

                current_date_info = {
                    'hijri_formatted': f'{hijri_day} {hijri_months[hijri_month - 1]} {hijri_year}هـ',
                    'gregorian_formatted': saudi_time.strftime('%Y-%m-%d'),
                    'day_name': saudi_time.strftime('%A'),
                    'time_24h': saudi_time.strftime('%H:%M')
                }

            # Format times in Umm Al-Qura timezone
            umm_alqura_time = saudi_time.strftime('%I:%M %p')  # 12-hour format
            umm_alqura_date = saudi_time.strftime('%Y-%m-%d')

            # Add enhanced header with title, dates, and time
            title = report_title if report_title else f"تقرير {sheet_name}"

            # Determine number of columns for merging
            num_cols = max(len(df.columns), 5) if not df.empty else 5
            last_col = chr(ord('A') + num_cols - 1)

            # Merge cells for title (full width)
            worksheet.merge_range(f'A1:{last_col}1', title, title_format)

            # Add date and time information
            worksheet.merge_range(f'A2:{last_col}2',
                f"التاريخ الهجري: {current_date_info['hijri_formatted']} | التاريخ الميلادي: {umm_alqura_date}",
                subtitle_format)

            worksheet.merge_range(f'A3:{last_col}3',
                f"وقت الإنشاء (توقيت أم القرى): {umm_alqura_time} | اليوم: {current_date_info['day_name']}",
                subtitle_format)

            # Add empty row for spacing
            worksheet.set_row(4, 5)  # Small spacing row

            # Check if DataFrame is empty
            if df.empty:
                # Add "No data" message
                no_data_format = workbook.add_format({
                    'bold': True,
                    'font_size': 14,
                    'align': 'center',
                    'valign': 'vcenter',
                    'bg_color': '#FFF2CC',
                    'border': 1,
                    'reading_order': 2
                })

                worksheet.merge_range(f'A5:{last_col}5', "لا توجد بيانات للعرض", no_data_format)

                # Set default column widths
                for col_num in range(num_cols):
                    worksheet.set_column(col_num, col_num, 20)

            else:
                # Write the dataframe to the sheet
                df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=5)

                # Set enhanced column widths with better spacing
                for i, col in enumerate(df.columns):
                    column_width = max(df[col].astype(str).map(len).max(), len(col) + 2)
                    worksheet.set_column(i, i, min(column_width + 8, 50))  # Add padding but limit max width

                # Format headers with enhanced styling
                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(5, col_num, value, header_format)

                # Apply alternating row colors and conditional formatting
                for row_num in range(len(df)):
                    row_format = alt_row_format if row_num % 2 == 1 else cell_format

                    for col_num in range(len(df.columns)):
                        cell_value = df.iloc[row_num, col_num]

                        # Special formatting for status column
                        if 'الحالة' in df.columns and col_num == df.columns.get_loc('الحالة'):
                            if cell_value in status_formats:
                                worksheet.write(row_num + 6, col_num, cell_value, status_formats[cell_value])
                            else:
                                worksheet.write(row_num + 6, col_num, cell_value, row_format)

                        # Special formatting for date columns
                        elif df.columns[col_num] in ['تاريخ الإضافة', 'تاريخ البدء', 'تاريخ الإنتهاء', 'التاريخ']:
                            if pd.notna(cell_value):
                                worksheet.write(row_num + 6, col_num, cell_value, date_format)
                            else:
                                worksheet.write(row_num + 6, col_num, cell_value, row_format)

                        # Regular cell formatting
                        else:
                            worksheet.write(row_num + 6, col_num, cell_value, row_format)

                # Add auto-filter and freeze panes only if DataFrame is not empty
                if len(df.columns) > 0:
                    # Auto-filter starting from header row
                    worksheet.autofilter(5, 0, 5 + len(df), len(df.columns) - 1)

                    # Freeze panes at header row
                    worksheet.freeze_panes(6, 0)

                    # Set print area
                    if len(df) > 0:
                        worksheet.print_area(f'A1:{last_col}{6 + len(df)}')

            # Add print settings for better printing (for both empty and non-empty sheets)
            worksheet.set_landscape()
            worksheet.set_paper(9)  # A4 paper
            worksheet.fit_to_pages(1, 0)  # Fit to 1 page wide, unlimited pages tall
            worksheet.set_margins(0.5, 0.5, 0.75, 0.75)  # left, right, top, bottom

    except Exception as e:
        # Try to create a simple Excel file as fallback
        try:
            fallback_output = BytesIO()
            fallback_writer = pd.ExcelWriter(fallback_output, engine='xlsxwriter')

            # Create a simple error sheet
            error_df = pd.DataFrame({
                'خطأ': ['فشل في إنشاء التقرير'],
                'التفاصيل': [str(e)],
                'الوقت': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
            })

            error_df.to_excel(fallback_writer, sheet_name='خطأ', index=False)
            fallback_writer.close()
            fallback_output.seek(0)

            return fallback_output

        except Exception as fallback_error:
            return None

    writer.close()
    output.seek(0)
    return output

def export_to_pdf(html_content, filename):
    """
    Export HTML content to PDF.

    Args:
        html_content: HTML content to export
        filename: Output filename

    Returns:
        BytesIO: PDF file as bytes
    """
    try:
        from weasyprint import HTML

        pdf_bytes = HTML(string=html_content).write_pdf()
        pdf_file = BytesIO(pdf_bytes)
        pdf_file.seek(0)

        return pdf_file
    except ImportError:
        # Fallback if weasyprint is not available
        return None

def get_weapons_excel(warehouse_id=None, status=None):
    """
    Generate Excel export of weapons.

    Args:
        warehouse_id: Optional warehouse ID to filter by
        status: Optional status to filter by (e.g., 'نشط', 'إجازة', 'مستلم', etc.)

    Returns:
        DataFrame: Pandas DataFrame with weapon data
    """
    query = Weapon.query

    if warehouse_id:
        query = query.filter_by(warehouse_id=warehouse_id)

    if status:
        query = query.filter_by(status=status)

    weapons = query.all()

    data = []
    for weapon in weapons:
        # Get personnel assigned to this weapon
        personnel_names = []
        for person in weapon.personnel:
            personnel_names.append(f"{person.name} ({person.rank})")

        personnel_str = ", ".join(personnel_names) if personnel_names else "لا يوجد"

        # البحث عن آخر تغيير حالة للسلاح من سجل الأنشطة
        last_status_change = ActivityLog.query.filter(
            ActivityLog.action == "تغيير حالة سلاح",
            ActivityLog.description.like(f"%{weapon.serial_number}%")
        ).order_by(ActivityLog.timestamp.desc()).first()

        # تحديد وقت تغيير الحالة بتوقيت أم القرى
        if last_status_change:
            # التأكد من أن الوقت بتوقيت أم القرى
            if last_status_change.timestamp.tzinfo is None:
                # إذا لم يكن هناك معلومات توقيت، نفترض أنه بتوقيت أم القرى
                import pytz
                saudi_tz = pytz.timezone('Asia/Riyadh')
                localized_time = saudi_tz.localize(last_status_change.timestamp)
            else:
                localized_time = last_status_change.timestamp

            status_change_time = localized_time.strftime('%Y-%m-%d %I:%M %p') + ' (أم القرى)'
        else:
            # إذا لم يتم العثور على تغيير حالة، استخدم تاريخ الإنشاء
            if weapon.created_at.tzinfo is None:
                import pytz
                saudi_tz = pytz.timezone('Asia/Riyadh')
                localized_time = saudi_tz.localize(weapon.created_at)
            else:
                localized_time = weapon.created_at

            status_change_time = localized_time.strftime('%Y-%m-%d %I:%M %p') + ' (أم القرى)'

        data.append({
            'الأفراد المرتبطين': personnel_str,
            'الرقم التسلسلي': weapon.serial_number,
            'نوع الأسلحة': weapon.name,
            'أرقام الأسلحة': weapon.type,
            'الحالة': weapon.status,
            'حالة السلاح': weapon.condition,
            'وقت تغيير الحالة': status_change_time,
            'المستودع': weapon.warehouse.name,
            'تاريخ الإضافة': weapon.created_at.strftime('%Y-%m-%d'),
            'الملاحظات': weapon.notes
        })

    # Create DataFrame - if no data, create empty DataFrame with columns
    if data:
        df = pd.DataFrame(data)
    else:
        df = pd.DataFrame(columns=[
            'الأفراد المرتبطين', 'الرقم التسلسلي', 'نوع الأسلحة', 'أرقام الأسلحة', 'الحالة', 'حالة السلاح',
            'وقت تغيير الحالة', 'المستودع', 'تاريخ الإضافة', 'الملاحظات'
        ])

    return df

def translate_device_status(status):
    """ترجمة حالة الجهاز من الإنجليزية إلى العربية"""
    translations = {
        'operational': 'سليم',
        'minor_damage': 'عطل بسيط',
        'major_damage': 'عطل جسيم',
        'maintenance': 'تحت الصيانة',
        'out_of_service': 'خارج الخدمة',
        'missing': 'مفقود'
    }
    return translations.get(status, status)

def translate_device_type(device_type):
    """ترجمة نوع الجهاز من الإنجليزية إلى العربية"""
    translations = {
        'computer': 'كمبيوتر',
        'laptop': 'لابتوب',
        'printer': 'طابعة',
        'scanner': 'سكانر',
        'monitor': 'شاشة',
        'projector': 'جهاز عرض',
        'server': 'سيرفر',
        'network': 'معدات شبكة',
        'camera': 'كاميرا',
        'storage': 'وحدة تخزين',
        'ups': 'مزود طاقة احتياطي',
        'other': 'أخرى'
    }
    return translations.get(device_type, device_type)

def get_devices_excel(warehouse_id=None):
    """Generate Excel export of devices."""
    # نستخدم warehouse_id إذا كان متوفراً
    if warehouse_id:
        devices = Device.query.filter_by(warehouse_id=warehouse_id).all()
    else:
        devices = Device.query.all()

    data = []
    for device in devices:
        # ترجمة حالة الجهاز ونوعه إلى العربية
        status_ar = translate_device_status(device.status)
        type_ar = translate_device_type(device.type)

        data.append({
            'الرقم التسلسلي': device.serial_number,
            'الاسم': device.name,
            'النوع': type_ar,
            'الموديل': device.model,
            'الشركة المصنعة': device.manufacturer if device.manufacturer else '',
            'الحالة': status_ar,
            'موقع الجهاز': device.location if device.location else '',
            'تاريخ الإضافة': device.created_at.strftime('%Y-%m-%d') if device.created_at else ''
        })

    # Create DataFrame - if no data, create empty DataFrame with columns
    if data:
        df = pd.DataFrame(data)
    else:
        df = pd.DataFrame(columns=[
            'الرقم التسلسلي', 'الاسم', 'النوع', 'الموديل', 'الشركة المصنعة', 'الحالة',
            'تاريخ الشراء', 'تاريخ انتهاء الضمان', 'موقع الجهاز', 'تاريخ الإضافة'
        ])

    return df

def get_maintenance_excel(warehouse_id=None):
    """Generate Excel export of maintenance."""
    if warehouse_id:
        maintenances = MaintenanceRecord.query.filter_by(warehouse_id=warehouse_id).all()
    else:
        maintenances = MaintenanceRecord.query.all()

    data = []
    for record in maintenances:
        # ترجمة نوع الصيانة وحالتها إلى العربية
        maintenance_type_ar = translate_maintenance_type(record.maintenance_type)
        status_ar = translate_maintenance_status(record.status)

        data.append({
            'المستخدم': record.user.username if record.user else '',
            'الوصف': record.description,
            'النوع': maintenance_type_ar,
            'التكلفة': record.cost,
            'الحالة': status_ar,
            'الملاحظات': record.notes if record.notes else '',
            'تاريخ البدء': record.start_date.strftime('%Y-%m-%d') if record.start_date else '',
            'تاريخ الإنتهاء': record.end_date.strftime('%Y-%m-%d') if record.end_date else '',
            'تاريخ الإضافة': record.created_at.strftime('%Y-%m-%d') if record.created_at else ''
        })

    # Create DataFrame - if no data, create empty DataFrame with columns
    if data:
        df = pd.DataFrame(data)
    else:
        df = pd.DataFrame(columns=[
            'المستخدم', 'الوصف', 'النوع', 'التكلفة', 'الحالة', 'الملاحظات',
            'تاريخ البدء', 'تاريخ الإنتهاء', 'تاريخ الإضافة'
        ])

    return df


def get_device_maintenance_excel():
    """Generate Excel export of device maintenance."""
    records = DeviceMaintenanceRecord.query.all()

    data = []
    for record in records:
        device = Device.query.get(record.device_id)

        # ترجمة نوع الصيانة وحالتها إلى العربية
        maintenance_type_ar = translate_maintenance_type(record.maintenance_type)
        status_ar = translate_maintenance_status(record.status)

        data.append({
            'الجهاز': device.name if device else '',
            'الرقم التسلسلي': device.serial_number if device else '',
            'الوصف': record.description,
            'النوع': maintenance_type_ar,
            'التكلفة': record.cost,
            'الحالة': status_ar,
            'الملاحظات': record.notes if record.notes else '',
            'تاريخ البدء': record.start_date.strftime('%Y-%m-%d') if record.start_date else '',
            'تاريخ الإنتهاء': record.end_date.strftime('%Y-%m-%d') if record.end_date else '',
            'تاريخ الإضافة': record.created_at.strftime('%Y-%m-%d') if record.created_at else ''
        })

    # Create DataFrame - if no data, create empty DataFrame with columns
    if data:
        df = pd.DataFrame(data)
    else:
        df = pd.DataFrame(columns=[
            'الجهاز', 'الرقم التسلسلي', 'الوصف', 'النوع', 'التكلفة', 'الحالة',
            'الملاحظات', 'تاريخ البدء', 'تاريخ الإنتهاء', 'تاريخ الإضافة'
        ])

    return df


def get_personnel_excel(warehouse_id=None, status=None, weekly=False, start_date=None, end_date=None):
    """
    Generate Excel export of personnel.

    Args:
        warehouse_id: Optional warehouse ID to filter by
        status: Optional status to filter by (e.g., 'نشط', 'إجازة', 'مستلم', etc.)
        weekly: Boolean to generate weekly report with Hijri dates
        start_date: Optional start date for custom period (for weekly reports)
        end_date: Optional end date for custom period (for weekly reports)

    Returns:
        DataFrame: Pandas DataFrame with personnel data
    """
    if weekly:
        # للتقرير الأسبوعي، استخدم الدالة الجديدة التي تعتمد على سجل الأنشطة
        return get_weekly_personnel_activities_excel(warehouse_id, status, start_date, end_date)

    query = Personnel.query

    if warehouse_id:
        query = query.filter_by(warehouse_id=warehouse_id)

    if status:
        query = query.filter_by(status=status)

    personnel_list = query.all()

    # الحصول على معلومات التاريخ الحالي
    current_date_info = get_hijri_date()
    week_info = get_week_range()

    data = []
    for personnel in personnel_list:
        weapons_str = ', '.join([f"{w.name} ({w.serial_number})" for w in personnel.weapons])

        # البحث عن آخر تغيير حالة للفرد من سجل الأنشطة
        last_status_change = ActivityLog.query.filter(
            ActivityLog.action == "تغيير حالة فرد",
            ActivityLog.description.like(f"%{personnel.name}%")
        ).order_by(ActivityLog.timestamp.desc()).first()

        # تحديد وقت تغيير الحالة بتوقيت أم القرى ومعلومات المستخدم
        if last_status_change:
            # التأكد من أن الوقت بتوقيت أم القرى
            if last_status_change.timestamp.tzinfo is None:
                # إذا لم يكن هناك معلومات توقيت، نفترض أنه بتوقيت أم القرى
                import pytz
                saudi_tz = pytz.timezone('Asia/Riyadh')
                localized_time = saudi_tz.localize(last_status_change.timestamp)
            else:
                localized_time = last_status_change.timestamp

            status_change_time = localized_time.strftime('%Y-%m-%d %I:%M %p') + ' (أم القرى)'
            changed_by_user = last_status_change.user.username if last_status_change.user else 'غير محدد'
        else:
            # إذا لم يتم العثور على تغيير حالة، استخدم تاريخ الإنشاء
            if personnel.created_at.tzinfo is None:
                import pytz
                saudi_tz = pytz.timezone('Asia/Riyadh')
                localized_time = saudi_tz.localize(personnel.created_at)
            else:
                localized_time = personnel.created_at

            status_change_time = localized_time.strftime('%Y-%m-%d %I:%M %p') + ' (أم القرى)'
            changed_by_user = 'غير محدد'

        # البيانات الأساسية بالترتيب المطلوب: الرتبة، الرقم العسكري، الاسم، الهوية الوطنية
        row_data = {
            'الرتبة': personnel.rank,
            'الرقم العسكري': personnel.personnel_id,
            'الاسم': personnel.name,
            'الهوية الوطنية': personnel.phone,  # حقل phone يحتوي على رقم الهوية الوطنية
            'الحالة': personnel.status,
            'الأسلحة': weapons_str,
            'المستودع': personnel.warehouse.name,
            'وقت تغيير الحالة': status_change_time,
            'تم التغيير بواسطة': changed_by_user,
            'تاريخ الإضافة': personnel.created_at.strftime('%Y-%m-%d')
        }

        data.append(row_data)

    # Create DataFrame - if no data, create empty DataFrame with columns
    if data:
        df = pd.DataFrame(data)
    else:
        columns = [
            'الرتبة', 'الرقم العسكري', 'الاسم', 'الهوية الوطنية', 'الحالة',
            'الأسلحة', 'المستودع', 'وقت تغيير الحالة', 'تم التغيير بواسطة', 'تاريخ الإضافة'
        ]
        df = pd.DataFrame(columns=columns)

    return df


def get_weekly_personnel_activities_excel(warehouse_id=None, status=None, start_date=None, end_date=None):
    """
    Generate Excel export of personnel activities for weekly report.
    This function first tries to get data from the saved weekly reports table,
    and falls back to ActivityLog if no saved data is found.

    Args:
        warehouse_id: Optional warehouse ID to filter by
        status: Optional status to filter by
        start_date: Optional start date for custom period
        end_date: Optional end date for custom period

    Returns:
        DataFrame: Pandas DataFrame with personnel activity data
    """
    from datetime import datetime, timedelta

    # أولاً، جرب الحصول على البيانات من جدول التقارير الأسبوعية المحفوظة
    df_saved = get_weekly_reports_from_database(warehouse_id, status, start_date, end_date)

    if df_saved is not None and not df_saved.empty:
        return df_saved

    # إذا لم توجد بيانات محفوظة، استخدم الطريقة القديمة من ActivityLog
    # تحديد الفترة الزمنية
    if start_date and end_date:
        # استخدام الفترة المخصصة
        period_start = start_date
        period_end = end_date
        period_type = "مخصصة"
    else:
        # استخدام الأسبوع الحالي
        week_info = get_week_range()
        period_start = week_info['week_start']
        period_end = week_info['week_end']
        period_type = "أسبوعية"

    current_date_info = get_hijri_date()
    saudi_time = get_saudi_now()

    # البحث عن جميع تغييرات حالات الأفراد خلال الفترة المحددة
    activities_query = ActivityLog.query.filter(
        ActivityLog.action == "تغيير حالة فرد",
        ActivityLog.timestamp >= period_start,
        ActivityLog.timestamp <= period_end
    )

    # تطبيق فلتر المستودع إذا تم تحديده
    if warehouse_id:
        activities_query = activities_query.filter(ActivityLog.warehouse_id == warehouse_id)

    activities = activities_query.order_by(ActivityLog.timestamp.desc()).all()

    # إذا لم توجد أنشطة في الفترة المحددة، ابحث في فترات أوسع
    if not activities and not (start_date and end_date):
        seven_days_ago = datetime.now() - timedelta(days=7)

        recent_activities_query = ActivityLog.query.filter(
            ActivityLog.action == "تغيير حالة فرد",
            ActivityLog.timestamp >= seven_days_ago
        )

        if warehouse_id:
            recent_activities_query = recent_activities_query.filter(ActivityLog.warehouse_id == warehouse_id)

        activities = recent_activities_query.order_by(ActivityLog.timestamp.desc()).all()
        period_type = "آخر 7 أيام"

    # إذا لم توجد أنشطة في آخر 7 أيام، ابحث في آخر 30 يوم
    if not activities and not (start_date and end_date):
        thirty_days_ago = datetime.now() - timedelta(days=30)

        recent_activities_query = ActivityLog.query.filter(
            ActivityLog.action == "تغيير حالة فرد",
            ActivityLog.timestamp >= thirty_days_ago
        )

        if warehouse_id:
            recent_activities_query = recent_activities_query.filter(ActivityLog.warehouse_id == warehouse_id)

        activities = recent_activities_query.order_by(ActivityLog.timestamp.desc()).limit(50).all()
        print(f"Debug: Found {len(activities)} activities in last 30 days")
        period_type = "آخر 30 يوم"

    # إذا لم توجد أنشطة حتى في آخر 30 يوم، ابحث في جميع الأنشطة
    if not activities and not (start_date and end_date):
        print("Debug: No activities in last 30 days, searching all activities...")
        all_activities_query = ActivityLog.query.filter(
            ActivityLog.action == "تغيير حالة فرد"
        )

        if warehouse_id:
            all_activities_query = all_activities_query.filter(ActivityLog.warehouse_id == warehouse_id)

        activities = all_activities_query.order_by(ActivityLog.timestamp.desc()).limit(20).all()
        print(f"Debug: Found {len(activities)} total activities")
        period_type = "جميع الأنشطة"



    data = []
    # إزالة processed_personnel لأننا نريد تسجيل جميع التغييرات، حتى لو كانت لنفس الفرد

    for activity in activities:
        # print(f"Debug: Processing activity: {activity.description}")  # يمكن إزالتها لاحق<|im_start|>
        # استخراج اسم الفرد من وصف النشاط
        description = activity.description

        # البحث عن اسم الفرد في الوصف
        # أمثلة مختلفة للأوصاف:
        # "تم تغيير حالة الفرد: أحمد محمد من نشط إلى إجازة"
        # "تم تغيير حالة الفرد: أحمد محمد من نشط إلى مهمة عبر صفحة الحالات"
        # "تم تغيير حالة الفرد: أحمد محمد من نشط إلى مهمة عبر صفحة الحالات (باستخدام رقم الهوية الوطنية)"
        if "تم تغيير حالة الفرد:" in description:
            try:
                print(f"Debug: Parsing description: {description}")

                # استخراج اسم الفرد والحالات
                parts = description.split("تم تغيير حالة الفرد:")[1].strip()

                # إزالة النصوص الإضافية مثل "عبر صفحة الحالات" و "(باستخدام رقم الهوية الوطنية)"
                if " عبر " in parts:
                    parts = parts.split(" عبر ")[0].strip()
                if " (" in parts:
                    parts = parts.split(" (")[0].strip()

                # تحليل الاسم والحالات
                if " من " in parts:
                    name_and_status = parts.split(" من ")
                    personnel_name = name_and_status[0].strip()
                    print(f"Debug: Extracted personnel name: '{personnel_name}'")

                    if len(name_and_status) > 1:
                        status_change = name_and_status[1]
                        if " إلى " in status_change:
                            old_status = status_change.split(" إلى ")[0].strip()
                            new_status_part = status_change.split(" إلى ")[1].strip()
                            # إزالة أي نص إضافي بعد الحالة الجديدة
                            new_status = new_status_part.split(" ")[0].strip()
                            print(f"Debug: Status change: '{old_status}' -> '{new_status}'")
                        else:
                            old_status = "غير محدد"
                            new_status = "غير محدد"
                            print("Debug: Could not parse status change - no 'إلى' found")
                    else:
                        old_status = "غير محدد"
                        new_status = "غير محدد"
                        print("Debug: No status change found in description")
                else:
                    # إذا لم يوجد " من " في النص، قد يكون النص مختلف
                    print(f"Debug: Unexpected description format: {parts}")
                    continue

                # البحث عن الفرد في قاعدة البيانات بطرق متعددة
                personnel = None

                # البحث بالاسم الكامل أولاً
                personnel = Personnel.query.filter(Personnel.name == personnel_name).first()

                # إذا لم يوجد، ابحث بالاسم الجزئي
                if not personnel:
                    personnel = Personnel.query.filter(Personnel.name.like(f"%{personnel_name}%")).first()

                # إذا لم يوجد، ابحث بكلمات الاسم منفصلة
                if not personnel and len(personnel_name.split()) > 1:
                    name_parts = personnel_name.split()
                    for part in name_parts:
                        if len(part) > 2:  # تجنب البحث بأجزاء قصيرة جداً
                            personnel = Personnel.query.filter(Personnel.name.like(f"%{part}%")).first()
                            if personnel:
                                break

                print(f"Debug: Found personnel: {personnel.name if personnel else 'None'}")

                if personnel:
                    # تطبيق فلتر الحالة إذا تم تحديده
                    # يجب أن يشمل التغييرات التي تتضمن الحالة المطلوبة (سواء كانت قديمة أو جديدة أو حالية)
                    if status and not (old_status == status or new_status == status or personnel.status == status):
                        print(f"Debug: Skipping due to status filter. Required: {status}, Old: {old_status}, New: {new_status}, Current: {personnel.status}")
                        continue

                    # تطبيق فلتر المستودع إذا تم تحديده
                    if warehouse_id and personnel.warehouse_id != warehouse_id:
                        print(f"Debug: Skipping due to warehouse filter. Required: {warehouse_id}, Personnel warehouse: {personnel.warehouse_id}")
                        continue

                    # إزالة منطق تجنب التكرار لأننا نريد تسجيل جميع التغييرات

                    weapons_str = ', '.join([f"{w.name} ({w.serial_number})" for w in personnel.weapons])

                    # تحديد وقت تغيير الحالة بتوقيت أم القرى
                    if activity.timestamp.tzinfo is None:
                        import pytz
                        saudi_tz = pytz.timezone('Asia/Riyadh')
                        localized_time = saudi_tz.localize(activity.timestamp)
                    else:
                        localized_time = activity.timestamp

                    # تنسيق الوقت بشكل أكثر تفصيل<|im_start|>
                    status_change_time = localized_time.strftime('%Y-%m-%d %I:%M:%S %p')
                    status_change_date = localized_time.strftime('%Y-%m-%d')
                    status_change_time_only = localized_time.strftime('%I:%M:%S %p')

                    # تحويل التاريخ إلى هجري
                    change_hijri = get_hijri_date(localized_time)

                    # تحديد فترة التقرير بناءً على النوع
                    if start_date and end_date:
                        period_info = f"من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}"
                    else:
                        week_info = get_week_range()
                        period_info = f"من {week_info['week_start_hijri']['hijri_formatted']} إلى {week_info['week_end_hijri']['hijri_formatted']}"

                    row_data = {
                        'رقم التسلسل': len(data) + 1,
                        'الرتبة': personnel.rank or '',
                        'الرقم العسكري': personnel.personnel_id,
                        'الاسم': personnel.name,
                        'الهوية الوطنية': personnel.phone or '',
                        'الحالة السابقة': old_status,
                        'الحالة الجديدة': new_status,
                        'الحالة الحالية': personnel.status,
                        'الأسلحة': weapons_str,
                        'المستودع': personnel.warehouse.name if personnel.warehouse else '',
                        'تاريخ التغيير (هجري)': change_hijri['hijri_formatted'],
                        'تاريخ التغيير (ميلادي)': status_change_date,
                        'وقت التغيير': status_change_time_only,
                        'التاريخ والوقت الكامل': status_change_time,
                        'يوم التغيير': change_hijri['day_name'],
                        'تاريخ التقرير (هجري)': current_date_info['hijri_formatted'],
                        'تاريخ التقرير (ميلادي)': current_date_info['gregorian_formatted'],
                        'اليوم': current_date_info['day_name'],
                        'الوقت (أم القرى)': saudi_time.strftime('%I:%M %p'),
                        'نوع التقرير': period_type,
                        'فترة التقرير': period_info,
                        'تاريخ الإضافة': personnel.created_at.strftime('%Y-%m-%d'),
                        'تفاصيل التغيير': description,
                        'تم التغيير بواسطة': activity.user.username if activity.user else 'غير محدد'
                    }

                    data.append(row_data)

            except Exception as e:
                # في حالة حدوث خطأ في تحليل الوصف، تجاهل هذا السجل
                continue

    # إذا لم توجد بيانات، أضف ملاحظة في التقرير
    if not data:
        print("Debug: No personnel status changes found in the specified period")

    # Create DataFrame - if no data, create empty DataFrame with columns
    if data:
        df = pd.DataFrame(data)
        # ترتيب البيانات حسب التاريخ والوقت الكامل (الأقدم أولاً لإظهار التسلسل الزمني)
        df = df.sort_values('التاريخ والوقت الكامل', ascending=True)

        # إعادة ترقيم التسلسل بعد الترتيب
        df.reset_index(drop=True, inplace=True)
        df['رقم التسلسل'] = range(1, len(df) + 1)

        # إعادة ترتيب الأعمدة لجعل رقم التسلسل أولاً
        cols = df.columns.tolist()
        cols = ['رقم التسلسل'] + [col for col in cols if col != 'رقم التسلسل']
        df = df[cols]

        print(f"Debug: Created DataFrame with {len(df)} rows and {len(df.columns)} columns")
    else:
        columns = [
            'رقم التسلسل', 'الرتبة', 'الرقم العسكري', 'الاسم', 'الهوية الوطنية', 'الحالة السابقة', 'الحالة الجديدة',
            'الحالة الحالية', 'الأسلحة', 'المستودع', 'تاريخ التغيير (هجري)', 'تاريخ التغيير (ميلادي)',
            'وقت التغيير', 'التاريخ والوقت الكامل', 'يوم التغيير', 'تاريخ التقرير (هجري)',
            'تاريخ التقرير (ميلادي)', 'اليوم', 'الوقت (أم القرى)', 'نوع التقرير', 'فترة التقرير', 'تاريخ الإضافة',
            'تفاصيل التغيير', 'تم التغيير بواسطة'
        ]
        df = pd.DataFrame(columns=columns)
        print("Debug: Created empty DataFrame with predefined columns")

    return df


def get_personnel_status_summary(warehouse_id=None, start_date=None, end_date=None):
    """
    Generate a summary of personnel status changes for the specified period.

    Args:
        warehouse_id: Optional warehouse ID to filter by
        start_date: Optional start date for custom period
        end_date: Optional end date for custom period

    Returns:
        dict: Summary statistics of status changes
    """
    from datetime import datetime, timedelta

    # تحديد الفترة الزمنية
    if start_date and end_date:
        period_start = start_date
        period_end = end_date
    else:
        week_info = get_week_range()
        period_start = week_info['week_start']
        period_end = week_info['week_end']

    # البحث عن جميع تغييرات حالات الأفراد خلال الفترة المحددة
    activities_query = ActivityLog.query.filter(
        ActivityLog.action == "تغيير حالة فرد",
        ActivityLog.timestamp >= period_start,
        ActivityLog.timestamp <= period_end
    )

    if warehouse_id:
        activities_query = activities_query.filter(ActivityLog.warehouse_id == warehouse_id)

    activities = activities_query.all()

    # إحصائيات التغييرات
    status_changes = {}
    personnel_affected = set()
    daily_changes = {}

    for activity in activities:
        # استخراج معلومات التغيير
        description = activity.description
        if "تم تغيير حالة الفرد:" in description:
            try:
                parts = description.split("تم تغيير حالة الفرد:")[1].strip()
                if " عبر " in parts:
                    parts = parts.split(" عبر ")[0].strip()
                if " (" in parts:
                    parts = parts.split(" (")[0].strip()

                if " من " in parts:
                    name_and_status = parts.split(" من ")
                    personnel_name = name_and_status[0].strip()
                    personnel_affected.add(personnel_name)

                    if len(name_and_status) > 1:
                        status_change = name_and_status[1]
                        if " إلى " in status_change:
                            old_status = status_change.split(" إلى ")[0].strip()
                            new_status = status_change.split(" إلى ")[1].strip().split(" ")[0]

                            # إحصائيات التغييرات
                            change_key = f"{old_status} → {new_status}"
                            status_changes[change_key] = status_changes.get(change_key, 0) + 1

                            # إحصائيات يومية
                            day_key = activity.timestamp.strftime('%Y-%m-%d')
                            if day_key not in daily_changes:
                                daily_changes[day_key] = 0
                            daily_changes[day_key] += 1
            except:
                continue

    return {
        'total_changes': len(activities),
        'personnel_affected': len(personnel_affected),
        'status_changes': status_changes,
        'daily_changes': daily_changes,
        'period_start': period_start,
        'period_end': period_end
    }


def translate_maintenance_type(maintenance_type):
    """ترجمة نوع الصيانة من الإنجليزية إلى العربية"""
    translations = {
        'routine': 'صيانة دورية',
        'repair': 'إصلاح',
        'replacement': 'استبدال',
        'upgrade': 'ترقية',
        'inspection': 'فحص',
        'other': 'أخرى'
    }
    return translations.get(maintenance_type, maintenance_type)

def translate_maintenance_status(status):
    """ترجمة حالة الصيانة من الإنجليزية إلى العربية"""
    translations = {
        'pending': 'قيد الانتظار',
        'in_progress': 'قيد التنفيذ',
        'completed': 'مكتملة',
        'cancelled': 'ملغية',
        'ongoing': 'جارية'
    }
    return translations.get(status, status)

def translate_audit_status(status):
    """ترجمة حالة الجرد من الإنجليزية إلى العربية"""
    translations = {
        'present': 'موجود',
        'missing': 'مفقود',
        'damaged': 'تالف',
        'needs_maintenance': 'يحتاج صيانة'
    }
    return translations.get(status, status)

def translate_transaction_type(transaction_type):
    """ترجمة نوع المعاملة من الإنجليزية إلى العربية"""
    translations = {
        'checkout': 'تسليم',
        'return': 'استلام',
        'transfer': 'نقل'
    }
    return translations.get(transaction_type, transaction_type)

def get_transactions_excel(warehouse_id=None, start_date=None, end_date=None):
    """Generate Excel export of transactions."""
    query = db.session.query(WeaponTransaction)

    if warehouse_id:
        query = query.filter(
            (WeaponTransaction.source_warehouse_id == warehouse_id) |
            (WeaponTransaction.target_warehouse_id == warehouse_id)
        )

    if start_date:
        query = query.filter(WeaponTransaction.timestamp >= start_date)

    if end_date:
        query = query.filter(WeaponTransaction.timestamp <= end_date)

    transactions = query.order_by(WeaponTransaction.timestamp.desc()).all()

    data = []
    for transaction in transactions:
        weapon = Weapon.query.get(transaction.weapon_id)
        personnel = Personnel.query.get(transaction.personnel_id)
        source_warehouse = Warehouse.query.get(transaction.source_warehouse_id)
        target_warehouse = Warehouse.query.get(transaction.target_warehouse_id) if transaction.target_warehouse_id else None

        # ترجمة نوع المعاملة إلى العربية
        transaction_type_ar = translate_transaction_type(transaction.transaction_type)

        data.append({
            'نوع العملية': transaction_type_ar,
            'التاريخ': transaction.timestamp.strftime('%Y-%m-%d %H:%M'),
            'اسم السلاح': weapon.name if weapon else '',
            'الرقم التسلسلي': weapon.serial_number if weapon else '',
            'اسم الفرد': personnel.name if personnel else '',
            'الرقم العسكري': personnel.personnel_id if personnel else '',
            'المستودع المصدر': source_warehouse.name if source_warehouse else '',
            'المستودع الهدف': target_warehouse.name if target_warehouse else '',
            'الملاحظات': transaction.notes
        })

    # Create DataFrame - if no data, create empty DataFrame with columns
    if data:
        df = pd.DataFrame(data)
    else:
        df = pd.DataFrame(columns=[
            'نوع العملية', 'التاريخ', 'اسم السلاح', 'الرقم التسلسلي', 'اسم الفرد',
            'الرقم العسكري', 'المستودع المصدر', 'المستودع الهدف', 'الملاحظات'
        ])

    return df

def get_audit_excel(audit_id):
    """Generate Excel export of an audit."""
    audit = Audit.query.get(audit_id)
    if not audit:
        return None

    # Get all items in this audit
    audit_items = AuditItem.query.filter_by(audit_id=audit.id).all()

    data = []
    for item in audit_items:
        weapon = Weapon.query.get(item.weapon_id)

        # ترجمة حالة الجرد إلى العربية
        status_ar = translate_audit_status(item.status)

        data.append({
            'اسم السلاح': weapon.name if weapon else '',
            'الرقم التسلسلي': weapon.serial_number if weapon else '',
            'النوع': weapon.type if weapon else '',
            'الحالة': status_ar,
            'حالة السلاح': item.condition,
            'الملاحظات': item.notes
        })

    # Create DataFrame - if no data, create empty DataFrame with columns
    if data:
        df = pd.DataFrame(data)
    else:
        df = pd.DataFrame(columns=[
            'اسم السلاح', 'الرقم التسلسلي', 'النوع', 'الحالة', 'حالة السلاح', 'الملاحظات'
        ])

    return df

def get_activities_excel(warehouse_ids=None, query=None):
    """
    Generate Excel export of activity logs.

    Args:
        warehouse_ids: Optional list of warehouse IDs to filter by
        query: Optional search query to filter activities

    Returns:
        DataFrame: Pandas DataFrame with activity log data
    """
    # Base query
    activities_query = ActivityLog.query

    # Filter by warehouse IDs if provided
    if warehouse_ids:
        activities_query = activities_query.filter(
            (ActivityLog.warehouse_id.in_(warehouse_ids)) |
            (ActivityLog.warehouse_id.is_(None))
        )

    # Apply search filter if provided
    if query:
        activities_query = activities_query.filter(
            or_(
                ActivityLog.action.ilike(f'%{query}%'),
                ActivityLog.description.ilike(f'%{query}%')
            )
        )

    # Get all activities ordered by timestamp
    activities = activities_query.order_by(ActivityLog.timestamp.desc()).all()

    data = []
    for activity in activities:
        # Get user and warehouse information
        user = activity.user
        warehouse = Warehouse.query.get(activity.warehouse_id) if activity.warehouse_id else None

        data.append({
            'النشاط': activity.action,
            'الوصف': activity.description,
            'المستودع': warehouse.name if warehouse else '',
            'بواسطة': user.username if user else '',
            'التاريخ': format_datetime_12h(activity.timestamp)
        })

    # Create DataFrame - if no data, create empty DataFrame with columns
    if data:
        df = pd.DataFrame(data)
    else:
        df = pd.DataFrame(columns=[
            'النشاط', 'الوصف', 'المستودع', 'بواسطة', 'التاريخ'
        ])

    return df


def status_color(status):
    """Return appropriate Bootstrap color class based on status."""
    # ترجمة الحالة إلى العربية أولاً إذا كانت بالإنجليزية
    if status == 'found':
        status = 'سليم'
    elif status == 'missing':
        status = 'مفقود'
    elif status == 'damaged':
        status = 'صيانة'
    elif status == 'ongoing':
        status = 'جارية'
    elif status == 'completed':
        status = 'مكتملة'
    elif status == 'cancelled':
        status = 'ملغاة'
    elif status == 'in-progress':
        status = 'قيد التنفيذ'

    status_colors = {
        'نشط': 'success',
        'إجازة': 'warning',
        'مهمة': 'mission',
        'صيانة': 'maintenance',
        'دورة': 'danger',
        'شاغر': 'dark',
        'مستلم': 'recipient',
        'رماية': 'shooting',
        'مفقود': 'danger',
        'سليم': 'success',
        'عطل بسيط': 'warning',
        'عطل جسيم': 'danger',
        'تحت الصيانة': 'maintenance',
        'خارج الخدمة': 'danger',
        'جارية': 'warning',
        'مكتملة': 'success',
        'ملغاة': 'danger',
        'قيد التنفيذ': 'warning'
    }

    return status_colors.get(status, 'secondary')


def translate_maintenance_type(maintenance_type):
    """ترجمة نوع الصيانة من الإنجليزية إلى العربية"""
    translations = {
        'routine': 'صيانة دورية',
        'repair': 'إصلاح',
        'upgrade': 'تحديث',
        'inspection': 'فحص',
        'replacement': 'طلب تعويض',
        'other': 'أخرى'
    }
    return translations.get(maintenance_type, maintenance_type)


def translate_maintenance_status(status):
    """ترجمة حالة الصيانة من الإنجليزية إلى العربية"""
    translations = {
        'ongoing': 'جارية',
        'completed': 'مكتملة',
        'cancelled': 'ملغاة',
        'pending': 'قيد الانتظار',
        'in_progress': 'قيد التنفيذ'
    }
    return translations.get(status, status)


def translate_audit_status(status):
    """ترجمة حالة الجرد من الإنجليزية إلى العربية"""
    translations = {
        'found': 'سليم',
        'missing': 'مفقود',
        'damaged': 'صيانة',
        'needs_maintenance': 'يحتاج صيانة',
        'present': 'موجود'
    }
    return translations.get(status, status)


def format_datetime_12h(dt):
    """
    تنسيق التاريخ والوقت بنظام 12 ساعة بالتنسيق السعودي

    Args:
        dt: كائن datetime للتنسيق

    Returns:
        str: التاريخ والوقت بتنسيق "YYYY-MM-DD hh:mm AM/PM"
    """
    if not dt:
        return ""

    # تحويل الوقت إلى نظام 12 ساعة مع إضافة صباحًا/مساءً
    hour = dt.hour
    am_pm = "صباحًا"
    if hour >= 12:
        am_pm = "مساءً"
        if hour > 12:
            hour -= 12

    # تنسيق الوقت بنظام 12 ساعة
    time_str = f"{hour:02d}:{dt.minute:02d} {am_pm}"

    # إرجاع التاريخ والوقت معًا
    return f"{dt.strftime('%Y-%m-%d')} {time_str}"


def format_time_12h(dt_or_hour, minute=None):
    """
    تنسيق الوقت فقط بنظام 12 ساعة بالتنسيق السعودي

    Args:
        dt_or_hour: كائن datetime للتنسيق أو ساعة كرقم
        minute: الدقيقة (اختياري، يستخدم عندما يكون المعامل الأول ساعة)

    Returns:
        str: الوقت بتنسيق "hh:mm AM/PM"
    """
    if not dt_or_hour and dt_or_hour != 0:
        return ""

    # إذا كان dt_or_hour رقمًا (ساعة) ومعامل minute موجود
    if isinstance(dt_or_hour, int) and minute is not None:
        hour = dt_or_hour
        minute = minute
    elif isinstance(dt_or_hour, int):
        # إذا كان dt_or_hour رقمًا (ساعة) فقط
        hour = dt_or_hour
        minute = 0
    else:
        # إذا كان dt_or_hour كائن datetime
        hour = dt_or_hour.hour
        minute = dt_or_hour.minute

    # تحويل الوقت إلى نظام 12 ساعة مع إضافة صباحًا/مساءً
    am_pm = "ص"  # صباحًا
    display_hour = hour

    if hour == 0:
        display_hour = 12
        am_pm = "ص"
    elif hour < 12:
        display_hour = hour
        am_pm = "ص"
    elif hour == 12:
        display_hour = 12
        am_pm = "م"
    else:
        display_hour = hour - 12
        am_pm = "م"

    # تنسيق الوقت بنظام 12 ساعة
    return f"{display_hour}:{minute:02d} {am_pm}"


def calculate_next_run(schedule_type, hour, minute, day_of_week=None, day_of_month=None):
    """Calculate the next run time for a backup schedule."""
    now = get_saudi_now()
    saudi_tz = now.tzinfo  # الحصول على المنطقة الزمنية من التاريخ الحالي

    if schedule_type == 'daily':
        # Next run is today at the specified time, or tomorrow if that time has passed
        next_run = datetime(now.year, now.month, now.day, hour, minute, tzinfo=saudi_tz)
        if next_run <= now:
            next_run += timedelta(days=1)

    elif schedule_type == 'weekly':
        # Calculate days until the next occurrence of day_of_week
        if day_of_week is None:
            day_of_week = 0  # Default to Monday

        days_ahead = day_of_week - now.weekday()
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7

        next_run = datetime(now.year, now.month, now.day, hour, minute, tzinfo=saudi_tz) + timedelta(days=days_ahead)

    elif schedule_type == 'monthly':
        # Calculate the next occurrence of day_of_month
        if day_of_month is None:
            day_of_month = 1  # Default to first day of month

        if day_of_month < now.day or (day_of_month == now.day and
                                      (hour < now.hour or
                                       (hour == now.hour and minute <= now.minute))):
            # Move to next month
            if now.month == 12:
                next_month = 1
                next_year = now.year + 1
            else:
                next_month = now.month + 1
                next_year = now.year
        else:
            next_month = now.month
            next_year = now.year

        # Adjust for months with fewer days
        import calendar
        last_day = calendar.monthrange(next_year, next_month)[1]
        day = min(day_of_month, last_day)

        next_run = datetime(next_year, next_month, day, hour, minute, tzinfo=saudi_tz)

    else:
        return None

    return next_run


def create_backup_schedule(schedule_type, backup_type, user_id, warehouse_id=None,
                          day_of_week=None, day_of_month=None, hour=0, minute=0):
    """Create a new backup schedule."""
    schedule = BackupSchedule(
        schedule_type=schedule_type,
        backup_type=backup_type,
        user_id=user_id,
        warehouse_id=warehouse_id,
        day_of_week=day_of_week,
        day_of_month=day_of_month,
        hour=hour,
        minute=minute,
        is_active=True
    )

    # Calculate the next run time
    schedule.next_run = calculate_next_run(
        schedule_type=schedule_type,
        hour=hour,
        minute=minute,
        day_of_week=day_of_week,
        day_of_month=day_of_month
    )

    # Save the schedule
    db.session.add(schedule)
    db.session.commit()

    return schedule


def update_backup_schedule(schedule_id, **kwargs):
    """Update an existing backup schedule."""
    schedule = BackupSchedule.query.get(schedule_id)
    if not schedule:
        return None

    # Update the schedule attributes
    for key, value in kwargs.items():
        if hasattr(schedule, key):
            setattr(schedule, key, value)

    # Recalculate the next run time
    schedule.next_run = calculate_next_run(
        schedule_type=schedule.schedule_type,
        hour=schedule.hour,
        minute=schedule.minute,
        day_of_week=schedule.day_of_week,
        day_of_month=schedule.day_of_month
    )

    # Save the changes
    db.session.commit()

    return schedule


def delete_backup_schedule(schedule_id):
    """Delete a backup schedule."""
    schedule = BackupSchedule.query.get(schedule_id)
    if not schedule:
        return False

    db.session.delete(schedule)
    db.session.commit()

    return True


def get_due_backup_schedules():
    """Get all backup schedules that are due to run."""
    now = get_saudi_now()
    return BackupSchedule.query.filter(
        BackupSchedule.is_active == True,
        BackupSchedule.next_run <= now
    ).all()


def run_scheduled_backups():
    """Run all due backup schedules."""
    schedules = get_due_backup_schedules()
    results = []

    for schedule in schedules:
        # Create the backup
        filename, file_path = backup_database(
            backup_type=schedule.backup_type,
            warehouse_id=schedule.warehouse_id,
            user_id=schedule.user_id
        )

        if filename:
            # Update the schedule's last_run and next_run
            schedule.last_run = get_saudi_now()
            schedule.next_run = calculate_next_run(
                schedule_type=schedule.schedule_type,
                hour=schedule.hour,
                minute=schedule.minute,
                day_of_week=schedule.day_of_week,
                day_of_month=schedule.day_of_month
            )
            db.session.commit()

            results.append({
                'schedule_id': schedule.id,
                'backup_filename': filename,
                'success': True
            })
        else:
            results.append({
                'schedule_id': schedule.id,
                'success': False,
                'error': 'Failed to create backup'
            })

    return results


# دوال التاريخ الهجري
def get_hijri_date(gregorian_date=None):
    """
    تحويل التاريخ الميلادي إلى هجري

    Args:
        gregorian_date: التاريخ الميلادي (datetime object)، إذا لم يتم تمريره سيتم استخدام التاريخ الحالي

    Returns:
        dict: قاموس يحتوي على التاريخ الهجري والميلادي مع اليوم والوقت
    """
    if gregorian_date is None:
        gregorian_date = get_saudi_now()

    # تحويل إلى التاريخ الهجري
    if HIJRI_AVAILABLE:
        try:
            hijri_date = Gregorian(
                gregorian_date.year,
                gregorian_date.month,
                gregorian_date.day
            ).to_hijri()
        except Exception as e:
            print(f"Error converting to Hijri date: {e}")
            # Use current correct values as fallback (18 ذو الحجة 1446هـ for 14 June 2025)
            # Calculate approximate hijri date based on current gregorian date
            days_since_ref = (gregorian_date.date() - datetime(2025, 6, 14).date()).days
            hijri_day = 18 + days_since_ref
            hijri_month = 12
            hijri_year = 1446

            # Adjust for month/year overflow (simple approximation)
            while hijri_day > 30:
                hijri_day -= 30
                hijri_month += 1
                if hijri_month > 12:
                    hijri_month = 1
                    hijri_year += 1
            while hijri_day < 1:
                hijri_day += 30
                hijri_month -= 1
                if hijri_month < 1:
                    hijri_month = 12
                    hijri_year -= 1

            hijri_date = type('obj', (object,), {'day': hijri_day, 'month': hijri_month, 'year': hijri_year})
    else:
        # Use current correct values when hijri_converter is not available
        # Calculate approximate hijri date based on current gregorian date
        days_since_ref = (gregorian_date.date() - datetime(2025, 6, 14).date()).days
        hijri_day = 18 + days_since_ref
        hijri_month = 12
        hijri_year = 1446

        # Adjust for month/year overflow (simple approximation)
        while hijri_day > 30:
            hijri_day -= 30
            hijri_month += 1
            if hijri_month > 12:
                hijri_month = 1
                hijri_year += 1
        while hijri_day < 1:
            hijri_day += 30
            hijri_month -= 1
            if hijri_month < 1:
                hijri_month = 12
                hijri_year -= 1

        hijri_date = type('obj', (object,), {'day': hijri_day, 'month': hijri_month, 'year': hijri_year})

    # أسماء الأشهر الهجرية
    hijri_months = [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
        'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ]

    # أسماء الأيام بالعربية
    arabic_days = [
        'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ]

    # الحصول على اسم اليوم
    day_name = arabic_days[gregorian_date.weekday()]

    return {
        'hijri_day': hijri_date.day,
        'hijri_month': hijri_date.month,
        'hijri_month_name': hijri_months[hijri_date.month - 1],
        'hijri_year': hijri_date.year,
        'hijri_formatted': f"{hijri_date.day} {hijri_months[hijri_date.month - 1]} {hijri_date.year}هـ",
        'gregorian_formatted': gregorian_date.strftime('%Y-%m-%d'),
        'day_name': day_name,
        'time_12h': gregorian_date.strftime('%I:%M %p'),
        'time_24h': gregorian_date.strftime('%H:%M'),
        'full_datetime': gregorian_date
    }


def get_week_range(date=None):
    """
    الحصول على نطاق الأسبوع (من الأحد إلى السبت)

    Args:
        date: التاريخ المرجعي (datetime object)، إذا لم يتم تمريره سيتم استخدام التاريخ الحالي

    Returns:
        dict: قاموس يحتوي على بداية ونهاية الأسبوع
    """
    if date is None:
        date = get_saudi_now()

    # الأسبوع يبدأ من الأحد (6) وينتهي بالسبت (5)
    # weekday() يعطي: الاثنين=0, الثلاثاء=1, ..., الأحد=6

    # تحويل إلى نظام الأحد=0, الاثنين=1, ..., السبت=6
    current_weekday = (date.weekday() + 1) % 7

    # حساب بداية الأسبوع (الأحد)
    days_since_sunday = current_weekday
    week_start = date - timedelta(days=days_since_sunday)
    week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)

    # حساب نهاية الأسبوع (السبت)
    week_end = week_start + timedelta(days=6)
    week_end = week_end.replace(hour=23, minute=59, second=59, microsecond=999999)

    return {
        'week_start': week_start,
        'week_end': week_end,
        'week_start_hijri': get_hijri_date(week_start),
        'week_end_hijri': get_hijri_date(week_end)
    }


def format_weekly_report_title(date=None):
    """
    تنسيق عنوان التقرير الأسبوعي

    Args:
        date: التاريخ المرجعي (datetime object)

    Returns:
        str: عنوان التقرير الأسبوعي
    """
    week_info = get_week_range(date)
    start_hijri = week_info['week_start_hijri']
    end_hijri = week_info['week_end_hijri']

    return f"تقرير الأفراد الأسبوعي من {start_hijri['hijri_formatted']} إلى {end_hijri['hijri_formatted']}"


def save_weekly_report_entry(personnel, old_status, new_status, user, change_method="غير محدد"):
    """
    حفظ إدخال في التقرير الأسبوعي عند تغيير حالة فرد

    Args:
        personnel: كائن الفرد
        old_status: الحالة السابقة
        new_status: الحالة الجديدة
        user: المستخدم الذي قام بالتغيير
        change_method: طريقة التغيير
    """
    from models import WeeklyReport
    from db import db

    try:
        # الحصول على معلومات الأسبوع الحالي
        week_info = get_week_range()
        current_date_info = get_hijri_date()

        # الحصول على الأسلحة المرتبطة بالفرد
        weapons_list = []
        if hasattr(personnel, 'weapons') and personnel.weapons:
            weapons_list = [f"{weapon.name} - {weapon.serial_number}" for weapon in personnel.weapons]
        weapons_text = ", ".join(weapons_list) if weapons_list else "لا توجد أسلحة"

        # إنشاء إدخال جديد في التقرير الأسبوعي
        weekly_entry = WeeklyReport(
            week_start=week_info['week_start'].date(),
            week_end=week_info['week_end'].date(),
            personnel_name=personnel.name,
            personnel_id=personnel.personnel_id,
            national_id=personnel.phone or "غير محدد",  # استخدام phone كهوية وطنية
            rank=personnel.rank or "غير محدد",
            old_status=old_status,
            new_status=new_status,
            current_status=personnel.status,  # الحالة الحالية
            change_date=get_saudi_now(),
            change_time=current_date_info['time_12h'],
            changed_by=user.username if user else "غير محدد",
            change_method=change_method,
            weapons=weapons_text,
            warehouse_name=personnel.warehouse.name if personnel.warehouse else "غير محدد",
            warehouse_id=personnel.warehouse_id,
            user_id=user.id if user else None
        )

        db.session.add(weekly_entry)
        db.session.commit()

        print(f"✅ تم حفظ إدخال التقرير الأسبوعي: {personnel.name} من {old_status} إلى {new_status}")

    except Exception as e:
        print(f"❌ خطأ في حفظ إدخال التقرير الأسبوعي: {str(e)}")
        db.session.rollback()


def get_weekly_reports_from_database(warehouse_id=None, status=None, start_date=None, end_date=None):
    """
    الحصول على التقارير الأسبوعية من قاعدة البيانات المحفوظة

    Args:
        warehouse_id: معرف المستودع (اختياري)
        status: فلتر الحالة (اختياري)
        start_date: تاريخ البداية (اختياري)
        end_date: تاريخ النهاية (اختياري)

    Returns:
        DataFrame: بيانات التقرير الأسبوعي
    """
    from models import WeeklyReport
    import pandas as pd

    try:
        # تحديد الفترة الزمنية
        if start_date and end_date:
            period_start = start_date.date() if hasattr(start_date, 'date') else start_date
            period_end = end_date.date() if hasattr(end_date, 'date') else end_date
        else:
            # استخدام الأسبوع الحالي
            week_info = get_week_range()
            period_start = week_info['week_start'].date()
            period_end = week_info['week_end'].date()

        # بناء الاستعلام
        query = WeeklyReport.query.filter(
            WeeklyReport.week_start >= period_start,
            WeeklyReport.week_end <= period_end
        )

        # فلترة حسب المستودع
        if warehouse_id:
            query = query.filter(WeeklyReport.warehouse_id == warehouse_id)

        # فلترة حسب الحالة
        if status:
            query = query.filter(
                (WeeklyReport.old_status == status) |
                (WeeklyReport.new_status == status) |
                (WeeklyReport.current_status == status)
            )

        # الحصول على النتائج
        reports = query.order_by(WeeklyReport.change_date.desc()).all()

        print(f"Debug: Found {len(reports)} weekly reports in database")

        if not reports:
            # إنشاء DataFrame فارغ مع الأعمدة المطلوبة
            return pd.DataFrame(columns=[
                'الرتبة', 'الرقم العسكري', 'الاسم', 'الهوية الوطنية',
                'الحالة السابقة', 'الحالة الجديدة', 'الحالة الحالية',
                'تاريخ التغيير', 'وقت التغيير', 'تم التغيير بواسطة',
                'طريقة التغيير', 'الأسلحة', 'المستودع',
                'فترة التقرير', 'تاريخ التقرير (هجري)', 'تاريخ التقرير (ميلادي)'
            ])

        # تحويل البيانات إلى DataFrame
        data = []
        current_date_info = get_hijri_date()
        week_info = get_week_range()

        for report in reports:
            # تحويل تاريخ التغيير إلى هجري
            change_hijri = get_hijri_date(report.change_date)

            data.append({
                'الرتبة': report.rank,
                'الرقم العسكري': report.personnel_id,
                'الاسم': report.personnel_name,
                'الهوية الوطنية': report.national_id,
                'الحالة السابقة': report.old_status,
                'الحالة الجديدة': report.new_status,
                'الحالة الحالية': report.current_status,
                'تاريخ التغيير': change_hijri['hijri_formatted'],
                'وقت التغيير': report.change_time,
                'تم التغيير بواسطة': report.changed_by,
                'طريقة التغيير': report.change_method,
                'الأسلحة': report.weapons or "لا توجد أسلحة",
                'المستودع': report.warehouse_name,
                'فترة التقرير': f"من {week_info['week_start_hijri']['hijri_formatted']} إلى {week_info['week_end_hijri']['hijri_formatted']}",
                'تاريخ التقرير (هجري)': current_date_info['hijri_formatted'],
                'تاريخ التقرير (ميلادي)': current_date_info['gregorian_formatted']
            })

        df = pd.DataFrame(data)
        print(f"Debug: Created DataFrame with {len(df)} rows and {len(df.columns)} columns")

        return df

    except Exception as e:
        print(f"❌ خطأ في الحصول على التقارير الأسبوعية: {str(e)}")
        import pandas as pd
        return pd.DataFrame()


# دوال مساعدة للتحقق من صلاحيات مناوب السرية
def company_duty_required(f):
    """
    Decorator للتأكد من أن المستخدم هو مناوب السرية أو لديه صلاحيات أعلى
    """
    from functools import wraps
    from flask import abort, flash, redirect, url_for
    from flask_login import current_user

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('يجب تسجيل الدخول أولاً', 'error')
            return redirect(url_for('auth.login'))

        # السماح لمدير النظام ومدير المستودعات ومناوب السرية
        if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_company_duty):
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
            return redirect(url_for('receipts.index'))

        return f(*args, **kwargs)
    return decorated_function


def block_company_duty_access(f):
    """
    Decorator لمنع مناوب السرية من الوصول لصفحات معينة
    """
    from functools import wraps
    from flask import abort, flash, redirect, url_for
    from flask_login import current_user

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if current_user.is_authenticated and current_user.is_company_duty:
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
            return redirect(url_for('receipts.index'))

        return f(*args, **kwargs)
    return decorated_function