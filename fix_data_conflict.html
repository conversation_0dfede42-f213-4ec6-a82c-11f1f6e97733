<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح سريع - تضارب البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        .fix-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn-fix {
            background: #dc3545;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-weight: bold;
            width: 100%;
            margin: 10px 0;
        }
        .btn-fix:hover {
            background: #c82333;
            color: white;
        }
        .status {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="text-center mb-4">
            <h2>🚨 إصلاح سريع - تضارب البيانات</h2>
            <p class="text-muted">حل سريع لمشكلة تداخل البيانات بين الصفحتين</p>
        </div>
        
        <div class="alert alert-danger">
            <strong>المشكلة:</strong> البيانات تتشارك بين كشف الواجبات وكشف الاستلامات
        </div>
        
        <button class="btn btn-fix" onclick="quickFix()">
            🔧 إصلاح سريع - حذف البيانات المتضاربة
        </button>
        
        <button class="btn btn-outline-info w-100" onclick="showCurrentStatus()">
            📊 عرض الحالة الحالية
        </button>
        
        <div id="status" class="status">
            اضغط على "عرض الحالة الحالية" لفحص البيانات...
        </div>
        
        <div class="alert alert-info mt-3">
            <strong>بعد الإصلاح:</strong>
            <ul class="mb-0">
                <li>كشف الواجبات سيستخدم: <code>duties_*</code></li>
                <li>كشف الاستلامات سيستخدم: <code>receipts_*</code></li>
                <li>لن تتداخل البيانات مرة أخرى</li>
            </ul>
        </div>
        
        <div class="text-center mt-4">
            <a href="/receipts" class="btn btn-primary me-2">كشف الاستلامات</a>
            <a href="/duties/test" class="btn btn-success">كشف الواجبات</a>
        </div>
    </div>

    <script>
        function showCurrentStatus() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '🔍 جاري فحص البيانات...\n';
            
            setTimeout(() => {
                let output = '📊 حالة localStorage:\n\n';
                
                const allKeys = Object.keys(localStorage);
                const conflictingKeys = ['patrolData', 'shiftsData', 'assignmentData', 'selectedLocations'];
                const dutiesKeys = allKeys.filter(key => key.startsWith('duties_'));
                const receiptsKeys = allKeys.filter(key => key.startsWith('receipts_'));
                const conflicts = allKeys.filter(key => conflictingKeys.includes(key));
                
                output += `🔧 مفاتيح كشف الواجبات: ${dutiesKeys.length}\n`;
                dutiesKeys.forEach(key => output += `  ✅ ${key}\n`);
                
                output += `\n📋 مفاتيح كشف الاستلامات: ${receiptsKeys.length}\n`;
                receiptsKeys.forEach(key => output += `  ✅ ${key}\n`);
                
                output += `\n⚠️ مفاتيح متضاربة: ${conflicts.length}\n`;
                if (conflicts.length > 0) {
                    conflicts.forEach(key => output += `  ❌ ${key} (يسبب المشكلة!)\n`);
                    output += '\n🚨 يوجد تضارب! يجب تشغيل الإصلاح.\n';
                } else {
                    output += '  🎉 لا توجد مفاتيح متضاربة!\n';
                }
                
                statusDiv.innerHTML = output;
            }, 500);
        }
        
        function quickFix() {
            if (!confirm('هل تريد حذف البيانات المتضاربة؟\n\nهذا سيحل المشكلة نهائياً.')) {
                return;
            }
            
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '🔧 جاري الإصلاح...\n';
            
            setTimeout(() => {
                // حذف المفاتيح المتضاربة
                const conflictingKeys = ['patrolData', 'shiftsData', 'assignmentData', 'selectedLocations'];
                let removed = 0;
                
                let output = '🗑️ حذف المفاتيح المتضاربة:\n';
                
                conflictingKeys.forEach(key => {
                    if (localStorage.getItem(key)) {
                        localStorage.removeItem(key);
                        output += `  ✅ تم حذف: ${key}\n`;
                        removed++;
                    }
                });
                
                output += `\n🎉 تم حذف ${removed} مفتاح متضارب\n`;
                
                // إنشاء بيانات افتراضية منفصلة
                const dutiesDefaults = {
                    assignmentData: { headers: ['الرقم', 'موقع الواجب', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'من 10 صباحاً إلى 2 ظهراً', 'من 2 ظهراً إلى 6 مساءً', 'ملاحظات'], rows: [] },
                    patrolData: { headers: ['الرقم', 'ملاحظات', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات إضافية'], rows: [] },
                    shiftsData: { headers: ['الرقم', 'موقع المناوبة', 'المناوب الأول', 'المناوب الثاني', 'المناوب الثالث', 'ملاحظات'], rows: [] }
                };
                
                const receiptsDefaults = {
                    patrolData: { headers: ['الرقم', 'ملاحظات', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات إضافية'], rows: [] },
                    shiftsData: { headers: ['الرقم', 'ملاحظات المناوبين', 'المناوب الأول', 'المناوب الثاني', 'المناوب الثالث', 'ملاحظات إضافية'], rows: [] }
                };
                
                // حفظ البيانات المنفصلة
                localStorage.setItem('duties_assignmentData', JSON.stringify(dutiesDefaults.assignmentData));
                localStorage.setItem('duties_patrolData', JSON.stringify(dutiesDefaults.patrolData));
                localStorage.setItem('duties_shiftsData', JSON.stringify(dutiesDefaults.shiftsData));
                localStorage.setItem('duties_selectedLocations', JSON.stringify({}));
                
                localStorage.setItem('receipts_patrolData', JSON.stringify(receiptsDefaults.patrolData));
                localStorage.setItem('receipts_shiftsData', JSON.stringify(receiptsDefaults.shiftsData));
                
                output += '\n✅ تم إنشاء بيانات منفصلة:\n';
                output += '  📝 duties_* (كشف الواجبات)\n';
                output += '  📋 receipts_* (كشف الاستلامات)\n';
                output += '\n🎉 تم الإصلاح بنجاح!\n';
                output += '🔄 يرجى تحديث الصفحات الآن.\n';
                
                statusDiv.innerHTML = output;
                
                setTimeout(() => {
                    alert('✅ تم إصلاح المشكلة!\n\nالآن كل صفحة لها بياناتها المنفصلة.\nيرجى تحديث صفحة كشف الواجبات وصفحة كشف الاستلامات.');
                }, 1000);
                
            }, 1000);
        }
        
        // عرض الحالة عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(showCurrentStatus, 500);
        });
    </script>
</body>
</html>
