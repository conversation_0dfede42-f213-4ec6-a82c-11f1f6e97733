#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة بيانات تجريبية للأفراد والمواقع لاختبار نظام كشف الواجبات
"""

from app import create_app
from models import Personnel, Location
from db import db
from datetime import datetime

def add_test_data():
    """إضافة بيانات تجريبية"""
    app = create_app()
    
    with app.app_context():
        print("🚀 بدء إضافة البيانات التجريبية...")
        
        # التحقق من وجود مواقع
        locations = Location.query.filter_by(status='نشط').all()
        if not locations:
            print("⚠️ لا توجد مواقع نشطة. إضافة مواقع تجريبية...")
            
            # إضافة مواقع تجريبية
            test_locations = [
                {'name': 'البوابة الرئيسية', 'type': 'أمني', 'serial_number': 'LOC001'},
                {'name': 'المخزن الرئيسي', 'type': 'إداري', 'serial_number': 'LOC002'},
                {'name': 'برج المراقبة', 'type': 'حراسة', 'serial_number': 'LOC003'},
                {'name': 'المدخل الجانبي', 'type': 'أمني', 'serial_number': 'LOC004'},
            ]
            
            for loc_data in test_locations:
                location = Location(
                    name=loc_data['name'],
                    type=loc_data['type'],
                    serial_number=loc_data['serial_number'],
                    description=f"موقع {loc_data['name']} - {loc_data['type']}",
                    status='نشط'
                )
                db.session.add(location)
            
            db.session.commit()
            locations = Location.query.filter_by(status='نشط').all()
            print(f"✅ تم إضافة {len(locations)} موقع")
        
        # التحقق من وجود أفراد
        personnel_count = Personnel.query.filter_by(status='نشط').count()
        if personnel_count < 10:
            print(f"⚠️ يوجد {personnel_count} فرد فقط. إضافة أفراد تجريبيين...")
            
            # إضافة أفراد تجريبيين
            test_personnel = [
                {'name': 'أحمد محمد علي', 'rank': 'عريف', 'personnel_id': '1234567890'},
                {'name': 'محمد أحمد سالم', 'rank': 'جندي أول', 'personnel_id': '2345678901'},
                {'name': 'علي سالم أحمد', 'rank': 'رقيب', 'personnel_id': '3456789012'},
                {'name': 'سالم علي محمد', 'rank': 'عريف', 'personnel_id': '4567890123'},
                {'name': 'خالد عبدالله سعد', 'rank': 'جندي', 'personnel_id': '5678901234'},
                {'name': 'عبدالله خالد فهد', 'rank': 'رقيب أول', 'personnel_id': '6789012345'},
                {'name': 'فهد سعد خالد', 'rank': 'عريف', 'personnel_id': '7890123456'},
                {'name': 'سعد فهد عبدالله', 'rank': 'جندي أول', 'personnel_id': '8901234567'},
                {'name': 'ناصر عبدالعزيز فيصل', 'rank': 'رقيب', 'personnel_id': '9012345678'},
                {'name': 'عبدالعزيز ناصر تركي', 'rank': 'عريف', 'personnel_id': '0123456789'},
                {'name': 'تركي فيصل ناصر', 'rank': 'جندي', 'personnel_id': '1122334455'},
                {'name': 'فيصل تركي عبدالعزيز', 'rank': 'جندي أول', 'personnel_id': '2233445566'},
            ]
            
            # توزيع الأفراد على المواقع
            for i, person_data in enumerate(test_personnel):
                location = locations[i % len(locations)]  # توزيع دوري على المواقع
                
                # التحقق من عدم وجود الفرد مسبقاً
                existing = Personnel.query.filter_by(personnel_id=person_data['personnel_id']).first()
                if not existing:
                    person = Personnel(
                        name=person_data['name'],
                        rank=person_data['rank'],
                        personnel_id=person_data['personnel_id'],
                        location_id=location.id,
                        phone=f"05{str(i).zfill(8)}",
                        status='نشط'
                    )
                    db.session.add(person)
            
            db.session.commit()
            new_count = Personnel.query.filter_by(status='نشط').count()
            print(f"✅ تم إضافة أفراد. العدد الإجمالي: {new_count}")
        
        # عرض ملخص البيانات
        print("\n📊 ملخص البيانات:")
        for location in locations:
            personnel_in_location = Personnel.query.filter_by(
                location_id=location.id, 
                status='نشط'
            ).all()
            print(f"📍 {location.name}: {len(personnel_in_location)} فرد")
            for person in personnel_in_location:
                print(f"    - {person.rank} {person.name}")
        
        print("\n✅ تم الانتهاء من إضافة البيانات التجريبية!")

if __name__ == '__main__':
    add_test_data()
