#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import traceback
from flask import Flask

def test_app_import():
    """اختبار استيراد التطبيق والبحث عن أخطاء"""
    try:
        print("🔍 اختبار استيراد التطبيق...")
        
        # محاولة استيراد التطبيق
        from app import app
        print("✅ تم استيراد التطبيق بنجاح")
        
        # اختبار إنشاء سياق التطبيق
        with app.app_context():
            print("✅ تم إنشاء سياق التطبيق بنجاح")
            
            # اختبار استيراد قاعدة البيانات
            from db import db
            print("✅ تم استيراد قاعدة البيانات بنجاح")
            
            # اختبار الاتصال بقاعدة البيانات
            try:
                # محاولة تنفيذ استعلام بسيط باستخدام SQLAlchemy 2.0 syntax
                from sqlalchemy import text
                with db.engine.connect() as connection:
                    result = connection.execute(text("SELECT 1"))
                    print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            except Exception as db_error:
                print(f"❌ خطأ في الاتصال بقاعدة البيانات: {db_error}")
                return False
            
            # اختبار استيراد النماذج
            try:
                from models import User, Personnel, Weapon
                print("✅ تم استيراد النماذج الأساسية بنجاح")
            except Exception as model_error:
                print(f"❌ خطأ في استيراد النماذج: {model_error}")
                traceback.print_exc()
                return False
            
            # اختبار الجداول المطلوبة
            try:
                inspector = db.inspect(db.engine)
                tables = inspector.get_table_names()
                
                required_models = {
                    'User': 'users',
                    'Personnel': 'personnel', 
                    'Weapon': 'weapons'
                }
                
                for model_name, table_name in required_models.items():
                    if table_name in tables:
                        print(f"✅ جدول {model_name} ({table_name}) موجود")
                    else:
                        print(f"❌ جدول {model_name} ({table_name}) مفقود")
                
            except Exception as table_error:
                print(f"❌ خطأ في فحص الجداول: {table_error}")
                return False
        
        # اختبار تشغيل الخادم
        print("\n🚀 اختبار تشغيل الخادم...")
        try:
            # محاولة إنشاء عميل اختبار
            client = app.test_client()
            
            # اختبار الصفحة الرئيسية
            response = client.get('/')
            print(f"📄 استجابة الصفحة الرئيسية: {response.status_code}")
            
            if response.status_code == 500:
                print("❌ خطأ خادم داخلي (500)")
                print("محتوى الاستجابة:")
                print(response.get_data(as_text=True)[:500])
                return False
            elif response.status_code == 404:
                print("⚠️  الصفحة غير موجودة (404)")
            elif response.status_code == 302:
                print("🔄 إعادة توجيه (302) - ربما إلى صفحة تسجيل الدخول")
            elif response.status_code == 200:
                print("✅ الصفحة تعمل بشكل صحيح (200)")
            
            # اختبار صفحة تسجيل الدخول
            login_response = client.get('/auth/login')
            print(f"🔐 استجابة صفحة تسجيل الدخول: {login_response.status_code}")

            # اختبار التوجيه من الصفحة الرئيسية
            if response.status_code == 302:
                location = response.headers.get('Location', '')
                print(f"🔄 إعادة التوجيه إلى: {location}")
            
        except Exception as server_error:
            print(f"❌ خطأ في اختبار الخادم: {server_error}")
            traceback.print_exc()
            return False
        
        print("\n✅ جميع الاختبارات نجحت!")
        return True
        
    except ImportError as import_error:
        print(f"❌ خطأ في الاستيراد: {import_error}")
        traceback.print_exc()
        return False
    except Exception as general_error:
        print(f"❌ خطأ عام: {general_error}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_app_import()
    if not success:
        print("\n💡 نصائح لحل المشاكل:")
        print("1. تأكد من أن PostgreSQL يعمل")
        print("2. تأكد من وجود قاعدة البيانات 'military_warehouse'")
        print("3. تأكد من صحة بيانات الاتصال")
        print("4. قم بتشغيل migrations إذا لزم الأمر")
        sys.exit(1)
    else:
        print("\n🎉 التطبيق جاهز للعمل!")
