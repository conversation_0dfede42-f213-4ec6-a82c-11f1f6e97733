from datetime import datetime
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SubmitField, SelectField, SelectMultipleField
from wtforms.validators import DataRequired, Length, Optional

from db import db
from models import Personnel, Warehouse, Weapon, ActivityLog, WeaponTransaction

# Create the blueprint
personnel_bp = Blueprint('personnel', __name__, url_prefix='/personnel')

# Forms
class PersonnelForm(FlaskForm):
    personnel_id = StringField('الرقم العسكري', validators=[DataRequired(), Length(min=1, max=50)])
    name = StringField('الاسم', validators=[DataRequired(), Length(min=1, max=100)])
    rank = StringField('الرتبة', validators=[Length(max=50)])
    status = SelectField('الحالة', choices=[
        ('نشط', 'نشط'),
        ('إجازة', 'إجازة'),
        ('مهمة', 'مهمة'),
        ('دورة', 'دورة'),
        ('مستلم', 'مستلم'),
        ('رماية', 'رماية')
    ])
    phone = StringField('رقم الهوية الوطنية', validators=[Length(max=20)])
    notes = TextAreaField('ملاحظات')
    warehouse_id = SelectField('المستودع', coerce=int)
    primary_weapons = SelectMultipleField('الأسلحة الأساسية', coerce=int)
    submit = SubmitField('حفظ')

    def __init__(self, *args, **kwargs):
        super(PersonnelForm, self).__init__(*args, **kwargs)
        # Only show warehouses that the current user has access to
        if current_user.is_authenticated:
            if current_user.is_admin_role:
                self.warehouse_id.choices = [(w.id, w.name) for w in Warehouse.query.all()]
                # Get all unassigned weapons from all warehouses for admin
                weapons = []
                for warehouse in Warehouse.query.all():
                    unassigned_weapons = Weapon.query.filter(
                        Weapon.warehouse_id == warehouse.id,
                        ~Weapon.personnel.any()  # This filters for weapons with no personnel assigned
                    ).all()
                    weapons.extend(unassigned_weapons)
            else:
                self.warehouse_id.choices = [(w.id, w.name) for w in current_user.warehouses]
                # Get only unassigned weapons from accessible warehouses
                weapons = []
                for warehouse in current_user.warehouses:
                    # Get weapons that have no personnel assigned (using the personnel relationship)
                    unassigned_weapons = Weapon.query.filter(
                        Weapon.warehouse_id == warehouse.id,
                        ~Weapon.personnel.any()  # This filters for weapons with no personnel assigned
                    ).all()
                    weapons.extend(unassigned_weapons)

            self.primary_weapons.choices = [(w.id, f"{w.name} ({w.serial_number})") for w in weapons]

# Routes
@personnel_bp.route('/')
@login_required
def index():
    # Get personnel from warehouses the user has access to
    personnel_list = []
    if current_user.is_admin_role:
        # Para administradores, mostrar todo el personal de todos los almacenes
        personnel_list = Personnel.query.all()
    else:
        # Para otros roles, solo mostrar personal de los almacenes asignados
        for warehouse in current_user.warehouses:
            personnel_list.extend(Personnel.query.filter_by(warehouse_id=warehouse.id).all())

    return render_template('personnel/index.html', personnel_list=personnel_list, title='قائمة الأفراد')

@personnel_bp.route('/warehouse/<int:warehouse_id>')
@login_required
def warehouse_personnel(warehouse_id):
    # Check if user has access to this warehouse
    warehouse = Warehouse.query.get_or_404(warehouse_id)
    if not current_user.is_admin_role and warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('personnel.index'))

    personnel_list = Personnel.query.filter_by(warehouse_id=warehouse_id).all()
    return render_template('personnel/index.html',
                          personnel_list=personnel_list,
                          warehouse=warehouse,
                          title=f'أفراد {warehouse.name}')

@personnel_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لإضافة أفراد جدد', 'danger')
        return redirect(url_for('personnel.index'))

    form = PersonnelForm()
    if form.validate_on_submit():
        # Check if personnel ID already exists
        existing_personnel = Personnel.query.filter_by(personnel_id=form.personnel_id.data).first()
        if existing_personnel:
            flash('الرقم العسكري موجود بالفعل', 'danger')
            return render_template('personnel/create.html', form=form, title='إضافة فرد جديد')

        # Create new personnel
        personnel = Personnel(
            personnel_id=form.personnel_id.data,
            name=form.name.data,
            rank=form.rank.data,
            status=form.status.data,
            phone=form.phone.data,
            notes=form.notes.data,
            warehouse_id=form.warehouse_id.data
        )
        db.session.add(personnel)
        db.session.flush()  # Get personnel ID

        # Add weapon associations
        if form.primary_weapons.data:
            for weapon_id in form.primary_weapons.data:
                weapon = Weapon.query.get(weapon_id)
                if weapon:
                    personnel.weapons.append(weapon)

                    # Create a weapon checkout transaction
                    transaction = WeaponTransaction(
                        transaction_type='checkout',
                        weapon_id=weapon.id,
                        personnel_id=personnel.id,
                        source_warehouse_id=weapon.warehouse_id,
                        user_id=current_user.id,
                        notes=f"تم ربط السلاح بالفرد {personnel.name} عند إنشاء الحساب"
                    )
                    db.session.add(transaction)

        # Log the personnel creation
        warehouse = Warehouse.query.get(form.warehouse_id.data)
        log = ActivityLog(
            action="إضافة فرد",
            description=f"تم إضافة فرد جديد: {personnel.name} (الرقم العسكري: {personnel.personnel_id})",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=warehouse.id
        )
        db.session.add(log)

        db.session.commit()
        flash('تم إضافة الفرد بنجاح!', 'success')
        return redirect(url_for('personnel.index'))

    return render_template('personnel/create.html', form=form, title='إضافة فرد جديد')

@personnel_bp.route('/<int:personnel_id>')
@login_required
def details(personnel_id):
    personnel = Personnel.query.get_or_404(personnel_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and personnel.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا الفرد', 'danger')
        return redirect(url_for('personnel.index'))

    # Get weapon history (transactions)
    transactions = WeaponTransaction.query.filter_by(personnel_id=personnel.id).order_by(WeaponTransaction.timestamp.desc()).all()

    return render_template('personnel/details.html',
                          personnel=personnel,
                          transactions=transactions,
                          title=f'تفاصيل الفرد: {personnel.name}')

@personnel_bp.route('/<int:personnel_id>/edit', methods=['GET', 'POST'])
@login_required
def edit(personnel_id):
    personnel = Personnel.query.get_or_404(personnel_id)

    # Check if user has access to the warehouse and proper role
    if not current_user.is_admin_role and personnel.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('personnel.index'))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لتعديل هذا الفرد', 'danger')
        return redirect(url_for('personnel.index'))

    form = PersonnelForm(obj=personnel)

    # Update weapon choices to include both unassigned weapons and currently assigned weapons
    if current_user.is_authenticated:
        weapons = []
        if current_user.is_admin_role:
            # Para administradores, mostrar todas las armas de todos los almacenes
            for warehouse in Warehouse.query.all():
                # Get both unassigned weapons and weapons assigned to this personnel
                available_weapons = Weapon.query.filter(
                    Weapon.warehouse_id == warehouse.id,
                    (Weapon.id.in_([w.id for w in personnel.weapons]) | ~Weapon.personnel.any())
                ).all()
                weapons.extend(available_weapons)
        else:
            # Para otros roles, solo mostrar armas de los almacenes asignados
            for warehouse in current_user.warehouses:
                # Get both unassigned weapons and weapons assigned to this personnel
                available_weapons = Weapon.query.filter(
                    Weapon.warehouse_id == warehouse.id,
                    (Weapon.id.in_([w.id for w in personnel.weapons]) | ~Weapon.personnel.any())
                ).all()
                weapons.extend(available_weapons)

        form.primary_weapons.choices = [(w.id, f"{w.name} ({w.serial_number})") for w in weapons]

    # Pre-select current weapons
    if request.method == 'GET':
        form.personnel_id.data = personnel.personnel_id
        form.name.data = personnel.name
        form.rank.data = personnel.rank
        form.status.data = personnel.status
        form.phone.data = personnel.phone
        form.notes.data = personnel.notes
        form.warehouse_id.data = personnel.warehouse_id
        form.primary_weapons.data = [w.id for w in personnel.weapons]

    if form.validate_on_submit():
        # Check if personnel ID already exists (excluding this personnel)
        existing_personnel = Personnel.query.filter(
            Personnel.personnel_id == form.personnel_id.data,
            Personnel.id != personnel.id
        ).first()

        if existing_personnel:
            flash('الرقم العسكري موجود بالفعل', 'danger')
            return render_template('personnel/edit.html', form=form, personnel=personnel, title='تعديل فرد')

        # Update personnel data
        personnel.personnel_id = form.personnel_id.data
        personnel.name = form.name.data
        personnel.rank = form.rank.data
        personnel.status = form.status.data
        personnel.phone = form.phone.data
        personnel.notes = form.notes.data

        # Check if warehouse changed
        if personnel.warehouse_id != form.warehouse_id.data:
            old_warehouse = personnel.warehouse
            new_warehouse = Warehouse.query.get(form.warehouse_id.data)

            # Log the warehouse change
            log_transfer = ActivityLog(
                action="نقل فرد",
                description=f"تم نقل الفرد: {personnel.name} (الرقم العسكري: {personnel.personnel_id}) من {old_warehouse.name} إلى {new_warehouse.name}",
                ip_address=request.remote_addr,
                user_id=current_user.id,
                warehouse_id=old_warehouse.id
            )
            db.session.add(log_transfer)

            # Update personnel warehouse
            personnel.warehouse_id = form.warehouse_id.data

        # Update weapon associations
        new_weapon_ids = form.primary_weapons.data

        # Remove weapons that are no longer associated
        for weapon in list(personnel.weapons):
            if weapon.id not in new_weapon_ids:
                personnel.weapons.remove(weapon)

                # Create a weapon return transaction
                return_transaction = WeaponTransaction(
                    transaction_type='return',
                    weapon_id=weapon.id,
                    personnel_id=personnel.id,
                    source_warehouse_id=weapon.warehouse_id,
                    user_id=current_user.id,
                    notes=f"تم إلغاء ربط السلاح من الفرد {personnel.name} عند تعديل البيانات"
                )
                db.session.add(return_transaction)

        # Add new weapon associations
        for weapon_id in new_weapon_ids:
            weapon = Weapon.query.get(weapon_id)
            if weapon and weapon not in personnel.weapons:
                personnel.weapons.append(weapon)

                # Create a weapon checkout transaction
                checkout_transaction = WeaponTransaction(
                    transaction_type='checkout',
                    weapon_id=weapon.id,
                    personnel_id=personnel.id,
                    source_warehouse_id=weapon.warehouse_id,
                    user_id=current_user.id,
                    notes=f"تم ربط السلاح بالفرد {personnel.name} عند تعديل البيانات"
                )
                db.session.add(checkout_transaction)

        # Log the personnel update
        log = ActivityLog(
            action="تعديل فرد",
            description=f"تم تعديل بيانات الفرد: {personnel.name} (الرقم العسكري: {personnel.personnel_id})",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=personnel.warehouse_id
        )
        db.session.add(log)

        db.session.commit()
        flash('تم تحديث بيانات الفرد بنجاح!', 'success')
        return redirect(url_for('personnel.details', personnel_id=personnel.id))

    return render_template('personnel/edit.html', form=form, personnel=personnel, title='تعديل فرد')

@personnel_bp.route('/<int:personnel_id>/status/<status>')
@login_required
def update_status(personnel_id, status):
    personnel = Personnel.query.get_or_404(personnel_id)

    # Check if user has access to the warehouse and proper role
    if not current_user.is_admin_role and personnel.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('personnel.index'))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لتعديل حالة هذا الفرد', 'danger')
        return redirect(url_for('personnel.index'))

    # Validate status
    if status not in ['نشط', 'إجازة', 'مهمة', 'دورة', 'مستلم', 'رماية']:
        flash('حالة غير صالحة', 'danger')
        return redirect(url_for('personnel.details', personnel_id=personnel.id))

    # Update status
    old_status = personnel.status
    personnel.status = status

    # Update weapons status if personnel goes on leave, mission, course, shooting or becomes a recipient
    if status in ['إجازة', 'مهمة', 'دورة', 'مستلم', 'رماية']:
        for weapon in personnel.weapons:
            # Move weapons to storage when personnel is on leave, mission, course or becomes a recipient
            if weapon.status == 'نشط':
                weapon.status = status

                # Log weapon status change with improved description
                log_weapon = ActivityLog(
                    action="تغيير حالة سلاح",
                    description=f"تم تغيير حالة السلاح للفرد {personnel.name}: {weapon.name} (الرقم التسلسلي: {weapon.serial_number}) من نشط إلى {status}",
                    ip_address=request.remote_addr,
                    user_id=current_user.id,
                    warehouse_id=personnel.warehouse_id
                )
                db.session.add(log_weapon)

    # Log the status change with improved description
    log = ActivityLog(
        action="تغيير حالة فرد",
        description=f"تم تغيير حالة الفرد: {personnel.name} من {old_status} إلى {status}",
        ip_address=request.remote_addr,
        user_id=current_user.id,
        warehouse_id=personnel.warehouse_id
    )
    db.session.add(log)

    # حفظ إدخال في التقرير الأسبوعي
    from utils import save_weekly_report_entry
    save_weekly_report_entry(personnel, old_status, status, current_user, "تغيير حالة مباشر")

    db.session.commit()
    flash(f'تم تغيير حالة الفرد إلى {status} بنجاح!', 'success')
    return redirect(url_for('personnel.details', personnel_id=personnel.id))

@personnel_bp.route('/<int:personnel_id>/delete', methods=['POST'])
@login_required
def delete_personnel(personnel_id):
    personnel = Personnel.query.get_or_404(personnel_id)

    # Check if user has access to the warehouse and proper role
    if not current_user.is_admin_role and personnel.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('personnel.index'))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لحذف هذا الفرد', 'danger')
        return redirect(url_for('personnel.index'))

    # Check if personnel has any assigned weapons
    if len(personnel.weapons) > 0:
        flash('لا يمكن حذف الفرد لأن لديه أسلحة مخصصة. يرجى فك ارتباط جميع الأسلحة أولاً.', 'danger')
        return redirect(url_for('personnel.index'))

    try:
        # Store warehouse_id for logging before deletion
        warehouse_id = personnel.warehouse_id
        personnel_name = personnel.name
        personnel_id = personnel.personnel_id

        # Delete all related weapon transactions
        WeaponTransaction.query.filter_by(personnel_id=personnel.id).delete()

        # Delete all related activity logs
        ActivityLog.query.filter(
            (ActivityLog.description.like(f'%{personnel.name}%')) |
            (ActivityLog.description.like(f'%{personnel.personnel_id}%'))
        ).delete()

        # Delete the personnel
        db.session.delete(personnel)

        # Log the deletion
        log = ActivityLog(
            action="حذف فرد",
            description=f"تم حذف الفرد: {personnel_name} (الرقم العسكري: {personnel_id})",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=warehouse_id
        )
        db.session.add(log)

        # Commit all changes
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء محاولة حذف الفرد. يرجى المحاولة مرة أخرى.', 'danger')
        return redirect(url_for('personnel.index'))

    flash(f'تم حذف الفرد {personnel.name} (الرقم العسكري: {personnel.personnel_id}) بنجاح!', 'success')
    return redirect(url_for('personnel.index'))

# API Endpoints
@personnel_bp.route('/api/list')
@login_required
def api_list():
    """API لجلب قائمة الأفراد"""
    try:
        # Get personnel from warehouses the user has access to
        personnel_list = []
        if current_user.is_admin_role:
            personnel_list = Personnel.query.all()
        else:
            for warehouse in current_user.warehouses:
                personnel_list.extend(Personnel.query.filter_by(warehouse_id=warehouse.id).all())

        # تحويل البيانات إلى تنسيق JSON
        personnel_data = []
        for person in personnel_list:
            personnel_data.append({
                'id': person.id,
                'personnel_id': person.personnel_id,
                'national_id': person.phone,  # استخدام phone كـ national_id (رقم الهوية الوطنية)
                'name': person.name,
                'rank': person.rank,
                'status': person.status,
                'phone': person.phone,
                'warehouse_id': person.warehouse_id,
                'warehouse_name': person.warehouse.name if person.warehouse else 'غير محدد',
                'unit': person.warehouse.name if person.warehouse else 'غير محدد'
            })

        return jsonify(personnel_data)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@personnel_bp.route('/api/search')
@login_required
def api_search():
    """API للبحث عن فرد بالهوية الوطنية"""
    try:
        national_id = request.args.get('national_id', '').strip()

        if not national_id:
            return jsonify({'error': 'رقم الهوية الوطنية مطلوب'}), 400

        # البحث عن الفرد برقم الهوية الوطنية (phone field) أو الرقم العسكري (personnel_id)
        personnel = None
        if current_user.is_admin_role or current_user.is_company_duty:
            # مدير النظام ومناوب السرية يمكنهم البحث في جميع الأفراد
            personnel = Personnel.query.filter_by(phone=national_id).first()
            if not personnel:
                personnel = Personnel.query.filter_by(personnel_id=national_id).first()
        else:
            # البحث في المستودعات المخصصة للمستخدم فقط
            warehouse_ids = [w.id for w in current_user.warehouses]
            if warehouse_ids:  # التأكد من وجود مستودعات
                personnel = Personnel.query.filter(
                    Personnel.phone == national_id,
                    Personnel.warehouse_id.in_(warehouse_ids)
                ).first()
                if not personnel:
                    personnel = Personnel.query.filter(
                        Personnel.personnel_id == national_id,
                        Personnel.warehouse_id.in_(warehouse_ids)
                    ).first()
            else:
                # إذا لم يكن للمستخدم مستودعات، لا يمكنه البحث
                return jsonify({'error': 'ليس لديك صلاحية للبحث عن الأفراد'}), 403

        if personnel:
            # تحديد رقم الهوية الوطنية - إما من phone أو personnel_id
            national_id_value = personnel.phone if personnel.phone and len(personnel.phone) == 10 else personnel.personnel_id

            return jsonify({
                'id': personnel.id,
                'personnel_id': personnel.personnel_id,
                'national_id': national_id_value,
                'name': personnel.name,
                'rank': personnel.rank,
                'status': personnel.status,
                'phone': personnel.phone,
                'warehouse_id': personnel.warehouse_id,
                'warehouse_name': personnel.warehouse.name if personnel.warehouse else 'غير محدد',
                'unit': personnel.warehouse.name if personnel.warehouse else 'غير محدد'
            })
        else:
            return jsonify({'error': 'لم يتم العثور على فرد بهذا الرقم'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@personnel_bp.route('/search')
@login_required
def search():
    query = request.args.get('q', '')
    if not query:
        return redirect(url_for('personnel.index'))

    # Search for personnel by ID, name, rank
    if current_user.is_admin_role or current_user.is_company_duty:
        # مدير النظام ومناوب السرية يمكنهم البحث في جميع الأفراد
        personnel_list = Personnel.query.filter(
            (Personnel.personnel_id.ilike(f'%{query}%') |
             Personnel.name.ilike(f'%{query}%') |
             Personnel.rank.ilike(f'%{query}%'))
        ).all()
    else:
        # للأدوار الأخرى، البحث في المستودعات المخصصة فقط
        warehouse_ids = [w.id for w in current_user.warehouses]
        if warehouse_ids:
            personnel_list = Personnel.query.filter(
                Personnel.warehouse_id.in_(warehouse_ids),
                (Personnel.personnel_id.ilike(f'%{query}%') |
                 Personnel.name.ilike(f'%{query}%') |
                 Personnel.rank.ilike(f'%{query}%'))
            ).all()
        else:
            personnel_list = []

    return render_template('personnel/search_results.html',
                          personnel_list=personnel_list,
                          query=query,
                          title='نتائج البحث')
