/* ===== Display Screens Custom Stylesheet ===== */
/* Author: نظام عتاد
   Description: Custom CSS for warehouse display screens based on the provided image
*/

/* ===== Variables ===== */
:root {
  /* Display Screen Colors */
  --display-bg: #1d1d1d;
  --display-header-bg: #242424;
  --display-accent: #0d6efd;
  --display-success: #28a745; /* لون أخضر لحالة نشط */
  --display-warning: #ffc107; /* لون أصفر لحالة إجازة */
  --display-danger: #dc3545; /* لون أحمر لحالة دورة */
  --display-info: #17a2b8;
  --display-mission: #fd7e14; /* لون برتقالي لحالة مهمة */
  --display-maintenance: #FAAFBE; /* لون وردي لحالة صيانة */
  --display-recipient: #0d6efd; /* لون أزرق لحالة مستلم */
  --display-vacant: #6c757d; /* لون رمادي لحالة شاغر */
  --display-cycle: #dc3545; /* لون أحمر لحالة دورة */
  --display-shooting: #C8BBBE; /* لون Lavender Blush3 لحالة رماية */
  --display-text: #ffffff;
  --display-text-secondary: #aaaaaa;
  --display-border: #333333;

  /* Light Mode Colors */
  --display-light-bg: #eef2f7;
  --display-light-header-bg: #dde3ec;
  --display-light-panel-bg: #e9edf3;
  --display-light-text: #1a1a1a;
  --display-light-text-secondary: #64748b;
  --display-light-border: #cbd5e1;
}

/* ===== Main Display Container ===== */
.display-screen {
  background-color: var(--display-bg);
  min-height: 100vh;
  padding: 0;
  color: var(--display-text);
  font-family: 'Cairo', sans-serif;
  overflow: hidden;
}

body.light-theme .display-screen {
  background-color: var(--display-light-bg);
  color: var(--display-light-text);
}

/* ===== Header Section ===== */
.display-header {
  background-color: var(--display-header-bg);
  padding: 1rem;
  border-bottom: 1px solid var(--display-border);
  text-align: center;
  position: relative;
}

body.light-theme .display-header {
  background-color: var(--display-light-header-bg);
  border-bottom: 1px solid var(--display-light-border);
}

.display-header h1 {
  font-size: 1.8rem;
  margin: 0;
  color: var(--display-text);
}

body.light-theme .display-header h1 {
  color: var(--display-light-text);
}

.display-date {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--display-accent);
  font-size: 1.2rem;
  text-align: left;
}

.display-logo {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
}

/* ===== Navigation Tabs ===== */
.display-tabs {
  display: flex;
  background-color: #111111;
  border-bottom: 1px solid var(--display-border);
  padding: 0.5rem 1rem;
}

.display-tab {
  padding: 0.5rem 1rem;
  margin-right: 1rem;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.display-tab.active {
  background-color: var(--display-accent);
  color: white;
}

.display-tab i {
  margin-right: 0.5rem;
}

/* ===== Content Section ===== */
.display-content {
  padding: 1rem;
}

.display-row {
  display: flex;
  margin-bottom: 1rem;
}

.display-col {
  flex: 1;
  padding: 0.5rem;
}

/* ===== Personnel Panel ===== */
.personnel-panel {
  background-color: #111111;
  border-radius: 10px;
  padding: 1rem;
  height: 100%;
}

body.light-theme .personnel-panel {
  background-color: var(--display-light-panel-bg);
}

.personnel-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--display-border);
  padding-bottom: 0.5rem;
}

body.light-theme .personnel-panel-header {
  border-bottom: 1px solid var(--display-light-border);
}

.personnel-panel-header h2 {
  font-size: 1.2rem;
  margin: 0;
  display: flex;
  align-items: center;
}

body.light-theme .personnel-panel-header h2 {
  color: var(--display-light-text);
}

.personnel-panel-header h2 i {
  margin-right: 0.5rem;
  color: var(--display-accent);
}

.personnel-panel-header .count {
  font-size: 1.2rem;
  font-weight: bold;
}

.personnel-chart-container {
  height: 250px;
  position: relative;
}

.personnel-legend {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 0.5rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 0.25rem;
}

.legend-active {
  background-color: var(--display-success);
}

.legend-leave {
  background-color: var(--display-warning);
}

.legend-mission {
  background-color: var(--display-mission);
}

.legend-maintenance {
  background-color: var(--display-maintenance);
}

.legend-retired {
  background-color: var(--display-accent);
}

/* ===== Weapons Panel ===== */
.weapons-panel {
  background-color: #111111;
  border-radius: 10px;
  padding: 1rem;
  height: 100%;
}

body.light-theme .weapons-panel {
  background-color: var(--display-light-panel-bg);
}

.weapons-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--display-border);
  padding-bottom: 0.5rem;
}

body.light-theme .weapons-panel-header {
  border-bottom: 1px solid var(--display-light-border);
}

.weapons-panel-header h2 {
  font-size: 1.2rem;
  margin: 0;
  display: flex;
  align-items: center;
}

body.light-theme .weapons-panel-header h2 {
  color: var(--display-light-text);
}

.weapons-panel-header h2 i {
  margin-right: 0.5rem;
  color: var(--display-accent);
}

.weapons-panel-header .count {
  font-size: 1.2rem;
  font-weight: bold;
}

.weapons-status-bars {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.weapon-status-bar {
  display: flex;
  align-items: center;
}

.weapon-status-label {
  width: 80px;
  font-size: 0.9rem;
}

.weapon-status-progress {
  flex-grow: 1;
  height: 20px;
  background-color: #222222;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

body.light-theme .weapon-status-progress {
  background-color: #d1d8e2;
}

.weapon-status-progress-bar {
  height: 100%;
  background-color: var(--display-success);
}

.weapon-status-count {
  width: 30px;
  text-align: right;
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

/* ===== Transactions Panel ===== */
.transactions-panel {
  background-color: #111111;
  border-radius: 10px;
  padding: 1rem;
  margin-top: 1rem;
}

body.light-theme .transactions-panel {
  background-color: var(--display-light-panel-bg);
}

.transactions-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--display-border);
  padding-bottom: 0.5rem;
}

body.light-theme .transactions-panel-header {
  border-bottom: 1px solid var(--display-light-border);
}

.transactions-panel-header h2 {
  font-size: 1.2rem;
  margin: 0;
  display: flex;
  align-items: center;
}

body.light-theme .transactions-panel-header h2 {
  color: var(--display-light-text);
}

.transactions-panel-header h2 i {
  margin-right: 0.5rem;
  color: var(--display-accent);
}

.transactions-list {
  max-height: 200px;
  overflow-y: auto;
}

.transaction-item {
  padding: 0.5rem;
  border-bottom: 1px solid #222222;
  font-size: 0.9rem;
  display: flex;
  justify-content: space-between;
}

body.light-theme .transaction-item {
  border-bottom: 1px solid var(--display-light-border);
  color: var(--display-light-text);
}

.transaction-time {
  color: var(--display-text-secondary);
  font-size: 0.8rem;
}

body.light-theme .transaction-time {
  color: var(--display-light-text-secondary);
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 768px) {
  .display-row {
    flex-direction: column;
  }

  .display-col {
    width: 100%;
    margin-bottom: 1rem;
  }
}
