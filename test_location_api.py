#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import create_app
from models import Location

def test_location_api():
    """اختبار API المواقع للتأكد من إصلاح مشكلة condition_status"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 اختبار API المواقع...")
            
            # اختبار استعلام المواقع
            locations = Location.query.all()
            print(f"📊 تم العثور على {len(locations)} موقع")
            
            if locations:
                # اختبار to_dict() للموقع الأول
                location = locations[0]
                print(f"🏢 اختبار الموقع: {location.name}")
                
                result = location.to_dict()
                print("✅ نجح استدعاء to_dict()!")
                print(f"📋 عدد المعدات: {result['equipment_count']}")
                print(f"👥 عدد الأفراد: {result['personnel_count']}")
                
                # اختبار استعلام المعدات مباشرة
                equipment_count = location.equipment.count()
                print(f"🔧 عدد المعدات (استعلام مباشر): {equipment_count}")
                
            else:
                print("⚠️  لا توجد مواقع في قاعدة البيانات")
            
            print("✅ جميع الاختبارات نجحت!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = test_location_api()
    if success:
        print("\n🎉 API المواقع يعمل بشكل صحيح!")
    else:
        print("\n❌ هناك مشكلة في API المواقع.")
