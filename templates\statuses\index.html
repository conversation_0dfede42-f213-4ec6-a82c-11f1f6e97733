{% extends "base.html" %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/warehouse-list.css') }}">
<style>
    .barcode-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #ddd;
        padding: 10px;
        margin-bottom: 15px;
        direction: ltr;
        text-align: left;
    }

    .barcode-item {
        padding: 5px;
        margin-bottom: 5px;
        border-bottom: 1px solid #eee;
    }

    .barcode-item:last-child {
        border-bottom: none;
    }

    .barcode-item.success {
        background-color: rgba(40, 167, 69, 0.1);
    }

    .barcode-item.warning {
        background-color: rgba(255, 193, 7, 0.1);
    }

    .barcode-item.danger {
        background-color: rgba(220, 53, 69, 0.1);
    }

    .status-card {
        border-right: 4px solid var(--accent-color);
        margin-bottom: 15px;
    }

    .status-card.active {
        border-color: #28a745;
    }

    .status-card.leave {
        border-color: #ffc107;
    }

    .status-card.mission {
        border-color: #007bff;
    }

    .status-card.maintenance {
        border-color: #FAAFBE;
    }

    .status-card.recipient {
        border-color: #17a2b8;
    }

    #barcodeInput {
        direction: ltr;
        text-align: left;
    }

    #barcodesTextarea {
        direction: ltr;
        text-align: left;
        height: 200px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>إدارة الحالات</h3>
        <p class="text-muted">تغيير حالة مجموعة من الأسلحة والأفراد المرتبطين بها بشكل جماعي</p>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5>تحديث الحالات</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('statuses.update_status') }}">
                    {{ form.hidden_tag() }}

                    <div class="form-group">
                        <label for="weapon_status">{{ form.weapon_status.label }}</label>
                        {{ form.weapon_status(class="form-control") }}
                    </div>

                    <div class="form-group">
                        <label for="personnel_status">{{ form.personnel_status.label }}</label>
                        {{ form.personnel_status(class="form-control") }}
                    </div>

                    <div class="form-group">
                        <label for="barcodesTextarea">{{ form.barcodes.label }}</label>
                        <div class="input-group mb-3">
                            <input type="text" id="barcodeInput" class="form-control" placeholder="أدخل الباركود هنا">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="addBarcodeBtn">إضافة</button>
                            </div>
                        </div>
                        {{ form.barcodes(class="form-control", id="barcodesTextarea", rows="10") }}
                        <small class="form-text text-muted">أدخل كل باركود في سطر منفصل، أو استخدم قارئ الباركود لإضافة الباركودات تلقائيًا.</small>
                    </div>

                    <div class="form-group">
                        <label for="notes">{{ form.notes.label }}</label>
                        {{ form.notes(class="form-control", rows="3") }}
                    </div>

                    <div class="form-group">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5>معلومات الباركود</h5>
            </div>
            <div class="card-body">
                <div id="weaponInfo">
                    <div class="text-center py-5">
                        <i class="fas fa-qrcode fa-3x mb-3 text-muted"></i>
                        <p class="text-muted">قم بإدخال باركود السلاح أو رقم الهوية الوطنية للفرد لعرض المعلومات</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>الباركودات المضافة</h5>
            </div>
            <div class="card-body">
                <div id="barcodesList" class="barcode-list">
                    <div class="text-center py-3">
                        <p class="text-muted">لم تتم إضافة أي باركودات بعد</p>
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" id="clearBarcodesBtn">مسح الكل</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const barcodeInput = document.getElementById('barcodeInput');
        const barcodesTextarea = document.getElementById('barcodesTextarea');
        const addBarcodeBtn = document.getElementById('addBarcodeBtn');
        const clearBarcodesBtn = document.getElementById('clearBarcodesBtn');
        const barcodesList = document.getElementById('barcodesList');
        const weaponInfo = document.getElementById('weaponInfo');

        // إضافة باركود عند الضغط على زر الإضافة
        addBarcodeBtn.addEventListener('click', function() {
            addBarcode();
        });

        // إضافة باركود عند الضغط على Enter
        barcodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addBarcode();
            }
        });

        // مسح جميع الباركودات
        clearBarcodesBtn.addEventListener('click', function() {
            barcodesTextarea.value = '';
            updateBarcodesList();
        });

        // تحديث قائمة الباركودات عند تغيير النص
        barcodesTextarea.addEventListener('input', function() {
            updateBarcodesList();
        });

        // دالة إضافة باركود
        function addBarcode() {
            const barcode = barcodeInput.value.trim();
            if (barcode) {
                // التحقق من عدم وجود الباركود مسبقًا
                const existingBarcodes = barcodesTextarea.value.split('\n').filter(b => b.trim());
                if (!existingBarcodes.includes(barcode)) {
                    // إضافة الباركود إلى النص
                    if (barcodesTextarea.value) {
                        barcodesTextarea.value += '\n' + barcode;
                    } else {
                        barcodesTextarea.value = barcode;
                    }

                    // البحث عن معلومات السلاح
                    fetchWeaponInfo(barcode);

                    // تحديث قائمة الباركودات
                    updateBarcodesList();
                }

                // مسح حقل الإدخال
                barcodeInput.value = '';
                barcodeInput.focus();
            }
        }

        // دالة تحديث قائمة الباركودات
        function updateBarcodesList() {
            const barcodes = barcodesTextarea.value.split('\n').filter(b => b.trim());

            if (barcodes.length === 0) {
                barcodesList.innerHTML = `
                    <div class="text-center py-3">
                        <p class="text-muted">لم تتم إضافة أي باركودات بعد</p>
                    </div>
                `;
                return;
            }

            barcodesList.innerHTML = '';
            barcodes.forEach((barcode, index) => {
                const barcodeItem = document.createElement('div');
                barcodeItem.className = 'barcode-item';
                barcodeItem.textContent = barcode;
                barcodesList.appendChild(barcodeItem);
            });
        }

        // دالة جلب معلومات السلاح
        function fetchWeaponInfo(barcode) {
            // إظهار حالة التحميل
            weaponInfo.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري البحث عن معلومات السلاح...</p>
                </div>
            `;

            // إرسال طلب AJAX للبحث عن السلاح
            const formData = new FormData();
            formData.append('barcode', barcode);

            fetch('{{ url_for("statuses.search_weapon") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.found) {
                    if (data.is_personnel) {
                        // عرض معلومات الفرد
                        let weaponsHtml = '';
                        if (data.weapons && data.weapons.length > 0) {
                            weaponsHtml = `
                                <h6 class="mt-3">الأسلحة المرتبطة:</h6>
                                <ul class="list-group">
                                    ${data.weapons.map(weapon => `
                                        <li class="list-group-item">
                                            <div><strong>${weapon.name}</strong> (${weapon.serial_number})</div>
                                            <div class="badge badge-${getStatusBadgeClass(weapon.status)}">${weapon.status}</div>
                                        </li>
                                    `).join('')}
                                </ul>
                            `;
                        }

                        weaponInfo.innerHTML = `
                            <div class="card status-card ${getStatusCardClass(data.status)}">
                                <div class="card-body">
                                    <h5 class="card-title">${data.name}</h5>
                                    <h6 class="card-subtitle mb-2 text-muted">رقم الهوية الوطنية: ${data.national_id}</h6>
                                    <div class="mb-2">
                                        <span class="badge badge-${getStatusBadgeClass(data.status)}">${data.status}</span>
                                    </div>
                                    <p class="card-text">الرقم العسكري: ${data.personnel_id}</p>
                                    <p class="card-text">المستودع: ${data.warehouse}</p>
                                    <div class="alert alert-info mt-3">${data.message}</div>
                                    ${weaponsHtml}
                                </div>
                            </div>
                        `;
                    } else {
                        // عرض معلومات السلاح
                        let personnelHtml = '';
                        if (data.personnel && data.personnel.length > 0) {
                            personnelHtml = `
                                <h6 class="mt-3">الأفراد المرتبطين:</h6>
                                <ul class="list-group">
                                    ${data.personnel.map(person => `
                                        <li class="list-group-item">
                                            <div><strong>${person.name}</strong> (${person.personnel_id})</div>
                                            <div class="badge badge-${getStatusBadgeClass(person.status)}">${person.status}</div>
                                        </li>
                                    `).join('')}
                                </ul>
                            `;
                        } else {
                            personnelHtml = `
                                <div class="alert alert-info mt-3">
                                    لا يوجد أفراد مرتبطين بهذا السلاح
                                </div>
                            `;
                        }

                        weaponInfo.innerHTML = `
                            <div class="card status-card ${getStatusCardClass(data.status)}">
                                <div class="card-body">
                                    <h5 class="card-title">${data.name}</h5>
                                    <h6 class="card-subtitle mb-2 text-muted">${data.serial_number}</h6>
                                    <div class="mb-2">
                                        <span class="badge badge-${getStatusBadgeClass(data.status)}">${data.status}</span>
                                    </div>
                                    <p class="card-text">المستودع: ${data.warehouse}</p>
                                    ${personnelHtml}
                                </div>
                            </div>
                        `;
                    }
                } else {
                    // عرض رسالة الخطأ
                    weaponInfo.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            ${data.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                weaponInfo.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        حدث خطأ أثناء البحث عن معلومات السلاح
                    </div>
                `;
            });
        }

        // دالة تحديد لون حالة السلاح
        function getStatusBadgeClass(status) {
            switch (status) {
                case 'نشط': return 'success';
                case 'إجازة': return 'warning';
                case 'مهمة': return 'mission';
                case 'صيانة': return 'maintenance';
                case 'دورة': return 'danger';
                case 'شاغر': return 'dark';
                case 'مستلم': return 'recipient';
                case 'رماية': return 'shooting';
                default: return 'dark';
            }
        }

        // دالة تحديد لون بطاقة حالة السلاح
        function getStatusCardClass(status) {
            switch (status) {
                case 'نشط': return 'active';
                case 'إجازة': return 'leave';
                case 'مهمة': return 'mission';
                case 'صيانة': return 'maintenance';
                case 'دورة': return 'cycle';
                case 'شاغر': return 'vacant';
                case 'مستلم': return 'recipient';
                case 'رماية': return 'shooting';
                default: return '';
            }
        }

        // تحديث قائمة الباركودات عند تحميل الصفحة
        updateBarcodesList();
    });
</script>
{% endblock %}
